<!-- omit in toc -->
# Debugging tools

- [Debug in concourse](#debug-in-concourse)
- [Running a debug script](#running-a-debug-script)
- [Fixing terraform lock error after an orange build](#fixing-terraform-lock-error-after-an-orange-build)
- [Restoring mongodb backup](#restoring-mongodb-backup)
- [Test a new pipeline job in concourse with fly](#test-a-new-pipeline-job-in-concourse-with-fly)
- [Deploy and debug Kafka connectors](#deploy-and-debug-kafka-connectors)

## Debug in concourse

This is a simple guide to debug in concourse. A short but detailed [video](https://www.loom.com/share/d58b41eb1dbe4b58a45d5460aae47a56) can be reviewed here.

To manually debug in concourse, you should perform the following steps:

- Log in to concourse, either via the web UI [Galoy Concourse](https://ci.blink.sv/) or via the terminal with the [`fly`](https://concourse-ci.org/fly.html) CLI. With either of these options, we can preview a list of pipelines (and their jobs), categorized by teams.

* set a TARGET_ALIAS and TEAM and log in via a link:
	```bash
  TARGET_ALIAS=ciblink
  TEAM_NAME=dev
	fly -t $TARGET_ALIAS login -n $TEAM_NAME -c https://ci.blink.sv
	```

*  You can view the teams on the web UI or via fly:
  ```
  fly -t $TARGET_ALIAS teams
  ```
  ```
  cepler
  dev
  galoy
  galoy-mobile
  galoy-reporting
	```

- Concourse keeps alive the most recent failed/aborted docker containers in which each step in a job is tested. These containers can be hijacked via the fly CLI to manually debug failing integration tests.
- To view a list of the pipelines and jobs within the selected pipeline, run this command with `fly`:
* list pipelines
  ```bash
  fly -t $TARGET_ALIAS pipelines
  ```

* list jobs of the chosen PIPELINE
  ```bash
  fly -t $TARGET_ALIAS jobs -p $PIPELINE
  ```

- Take note of the name of the job that failed so as to hijack its container runtime. To do so, run the following command and select the step of interest in the task.

* enter a JOB
  ```bash
  fly -t $TARGET_ALIAS hijack -j $PIPELINE/$JOB
  ```

- Within the hijacked container runtime, you now have access to the shell and can manually run each step in `ci/tasks/test-integration` to find the cause of failure in building.


## Running a debug script

1. Login to bastion
2. Run `k -n galoy-staging-galoy get pod` and copy the name of one of the api pods.
3. Run `k -n galoy-staging-galoy get pod <name copied in previous step> -o json > debug-pod.json`
4. make the following changes:

remove:
- `livenessProbe`, `readinessProbe`, `nodeName`, `ownerReferences`, `status` and `metadata.label.app`, `initContainers`

update:
`metadata.name` to `debug-pod` and replace the value of the `image` key with a node image from https://hub.docker.com/_/node, for example: `node:18-buster`. Also replace the `args` key within `container:` with `command: ["/bin/sh", "-c", "sleep 500000"]` so that the pod just waits on becoming ready.

1 liner to do it with jq:

```
jq 'del(.spec.nodeName, .metadata.ownerReferences, .status, .metadata.labels.app, .spec.initContainers, .spec.containers[0].livenessProbe, .spec.containers[0].readinessProbe, .spec.containers[0].args) | .metadata.name = "debug-pod" | .spec.containers[0].image = "node:18-buster" | .spec.containers[0].command = ["/bin/sh", "-c", "sleep 500000"]' debug-pod.json > debug-pod2.json
```

5. Run `k -n galoy-staging-galoy apply -f debug-pod2.json`
6. Exec into the newly created pod with `k -n galoy-staging-galoy exec -it debug-pod -- bash`
7. Run `git clone https://github.com/galoymoney/galoy.git`
8. Run `cd galoy && yarn install --production false`
9. Run the debug script, for example `yarn ts-node --files -r tsconfig-paths/register src/debug/reimburse.ts`

## Fixing terraform lock error after an orange build
Sometimes when the terraform galoy-<env> pipeline fails, it leaves a lock on the state file. This will cause the next terraform apply to fail. To fix this, you can -
1. Run the following command to remove the lock (for staging):
`gsutil rm gs://galoy-staging-tf-state/galoy-staging/galoy/default.tflock`.
2. On rerunning the pipeline, it will likely encounter another error - `Error: another operation (install/upgrade/rollback) is in progress`. To fix this, you need to remove a secret from the galoy-staging namespace that contains the information related to the helm release. The name of the secret is in the format - `sh.helm.release.v1.galoy.vXXX` where XXX is an integer. There will be a list of these secrets, retrievable by running `k -n galoy-staging-galoy get secret | grep sh.helm.release.v1.galoy`, with increasing XXX and the secret with the largest suffix is the one that needs to be deleted. To delete the secret, run `k -n galoy-staging-galoy delete secret sh.helm.release.v1.galoy.vXXX`. Rerun the pipeline.
3. The pipeline could still fail again with an error related to not being able to patch a k8s job resource. It can be solved by deleting the price history migrate job or the mongodb migrate job (depending on the error returned in the concourse logs) with the same XXX suffix - `k -n galoy-staging-galoy delete job galoy-price-history-postgres-migrate-XXX` or `k -n galoy-staging-galoy delete job galoy-mongodb-migrate-XXX`. After this, rerun the pipeline and it should succeed.

## Restoring mongodb backup

1. Go to google cloud console at `console.cloud.google.com/storage`. Select the correct project, then select galoy-{env}-backup and identify and click on the backup file to restore. Copy the `gsutil URI` of the object.
2. Go to bastion of the environment, run `k -n galoy-<env>-galoy get deployments` to list all deployments. Then scale each deployment to zero replicas by running `k -n galoy-<env>-galoy scale deployment <deployment name> --replicas=0` so that no pods talk to mongodb while we do the restore.
3. Run `k -n galoy-<env>-galoy port-forward galoy-mongodb-0 27017 &` to run the port-forward in background. The mongodb-0 pod is the master usually.
4. Run `gsutil cp <backup uri> .` to copy the backup .gz file to your current location.
5. Run `PASSWORD=$(k -n galoy-staging-galoy get secret galoy-mongodb -o json | jq -r '.data["mongodb-passwords"]' | base64 -d)` to get the mongo password
6. Run `mongorestore --host localhost --port 27017 -d galoy --drop --gzip --archive=<backup file name>.gz -u testGaloy -p $PASSWORD`. Note that the `--drop` flag will drop the database before start restore.

## Test a new pipeline job in concourse with fly
You can test a new pipeline job in concourse with fly. For example, you may have created a new Dockerfile to deploy a new application, like the `galoy-websocket-server`. You can see the example pipeline code here https://github.com/GaloyMoney/galoy/pull/2427/files#diff-1d0bbbf28914b9bc860a34ad16f16af0a6996287a6757589f82da847185a8c68. The steps below will show you how to build the docker image and deploy to gcr using concourse and fly.

1. Pre-reqs
  - Install ytt locally
  - goto galoy codebase `galoy/ci/values.yaml`
  - change the `git_branch` to your feature branch name for testing
2. Login to fly
```
TARGET_ALIAS=dev
TEAM_NAME=dev
fly -t $TARGET_ALIAS login -n $TEAM_NAME -c https://ci.blink.sv
# enter token
```
3. Pause the release job

Pause the release job to avoid the test image from advancing to charts (the gcr image will still be created)
```
PIPELINE_NAME=galoy-app
JOB=release
fly -t $TARGET_ALIAS pause-job -j $PIPELINE_NAME/$JOB
```
4. Deploy new job via repipe
```
FLY_TARGET="dev" ./ci/repipe
# the results should show a diff, check to make sure it looks correct before you type "Y"
```
The diff for the `galoy-websocket-server` example looks like this
```
  - name: build-websocket-edge-image
    serial: true
    plan:
      - { get: repo, trigger: true }
      - task: build
        privileged: true
        config:
          platform: linux
          image_resource:
            type: registry-image
            source:
              repository: vito/oci-build-task
          inputs:
            - name: repo
          outputs:
            - name: image
          params:
            CONTEXT: repo
            BUILD_ARGS_FILE: repo/.env
            DOCKERFILE: "repo/Dockerfile-websocket"
          run:
            path: build
      - put: websocket-edge-image
        params:
          image: image/image.tar
```
5. After testing, merge the changes to main
6. Unpause the release job
```
fly -t $TARGET_ALIAS unpause-job -j $PIPELINE_NAME/$JOB
```
7. You new concourse pipeline job should be ready to go and available to view at https://ci.blink.sv/

## [Deploy and debug Kafka connectors](kafka-connect.md)
