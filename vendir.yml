apiVersion: vendir.k14s.io/v1alpha1
kind: Config
directories:
- path: modules/infra/vendor
  contents:
  - path: git-ref
    inline:
      paths:
        ref: |
          680891fa2e20af364f0ddc7a032401307f899338
  - path: tf
    git:
      url: https://github.com/GaloyMoney/galoy-infra.git
      ref: 680891fa2e20af364f0ddc7a032401307f899338
    includePaths:
    - modules/bootstrap/gcp/**
    - modules/inception/gcp/**
    - modules/platform/gcp/**
    - modules/postgresql/gcp/**
    - modules/smoketest/gcp/**
    newRootPath: modules
- path: modules/services/bitcoin/vendor/bitcoind
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 29ea9caf11baec03422f718f98392fa8c2d5d43e
    includePaths:
    - charts/bitcoind/**/*
    newRootPath: charts/bitcoind
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 29ea9caf11baec03422f718f98392fa8c2d5d43e
    includePaths:
    - ci/tasks/bitcoind-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          29ea9caf11baec03422f718f98392fa8c2d5d43e
- path: modules/services/bitcoin/vendor/lnd1
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 7bddd07ae59eef508aa220621de60d4b18bb9666
    includePaths:
    - charts/lnd/**/*
    newRootPath: charts/lnd
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 7bddd07ae59eef508aa220621de60d4b18bb9666
    includePaths:
    - ci/tasks/lnd-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          7bddd07ae59eef508aa220621de60d4b18bb9666
- path: modules/services/bitcoin/vendor/lnd2
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 7bddd07ae59eef508aa220621de60d4b18bb9666
    includePaths:
    - charts/lnd/**/*
    newRootPath: charts/lnd
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 7bddd07ae59eef508aa220621de60d4b18bb9666
    includePaths:
    - ci/tasks/lnd-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          7bddd07ae59eef508aa220621de60d4b18bb9666
- path: modules/services/bitcoin/vendor/specter
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: bac4373d48116492846aa306818524b8f1d0cdc4
    includePaths:
    - charts/specter/**/*
    newRootPath: charts/specter
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: bac4373d48116492846aa306818524b8f1d0cdc4
    includePaths:
    - ci/tasks/specter-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          bac4373d48116492846aa306818524b8f1d0cdc4
- path: modules/services/bitcoin/vendor/bria
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: af48974418b8dcec0fc4f362df6efa29b4b6afb8
    includePaths:
    - charts/bria/**/*
    newRootPath: charts/bria
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: af48974418b8dcec0fc4f362df6efa29b4b6afb8
    includePaths:
    - ci/tasks/bria-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          af48974418b8dcec0fc4f362df6efa29b4b6afb8
- path: modules/services/bitcoin/vendor/fulcrum
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 6c6975563eb51bc14c3b5b547b9747446feb7cd2
    includePaths:
    - charts/fulcrum/**/*
    newRootPath: charts/fulcrum
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 6c6975563eb51bc14c3b5b547b9747446feb7cd2
    includePaths:
    - ci/tasks/fulcrum-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          6c6975563eb51bc14c3b5b547b9747446feb7cd2
- path: modules/galoy/vendor/galoy
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: eaee8fa5614b34403dbb3ba1fd4a9eff1aefb9d4
    includePaths:
    - charts/galoy/**/*
    newRootPath: charts/galoy
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: eaee8fa5614b34403dbb3ba1fd4a9eff1aefb9d4
    includePaths:
    - ci/tasks/galoy-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          eaee8fa5614b34403dbb3ba1fd4a9eff1aefb9d4
- path: modules/services/monitoring/vendor/monitoring
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 87ae0b0d962805a0597d44459d8e2c0d7254631b
    includePaths:
    - charts/monitoring/**/*
    newRootPath: charts/monitoring
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 87ae0b0d962805a0597d44459d8e2c0d7254631b
    includePaths:
    - ci/tasks/monitoring-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          87ae0b0d962805a0597d44459d8e2c0d7254631b
- path: modules/services/addons/vendor/admin-panel
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 2f29c0438445c32f2177305b6960df56a9ab8656
    includePaths:
    - charts/admin-panel/**/*
    newRootPath: charts/admin-panel
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 2f29c0438445c32f2177305b6960df56a9ab8656
    includePaths:
    - ci/tasks/admin-panel-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          2f29c0438445c32f2177305b6960df56a9ab8656
- path: modules/services/addons/vendor/galoy-pay
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 06221a57a68708c983d2c6cdafd1e402a8474837
    includePaths:
    - charts/galoy-pay/**/*
    newRootPath: charts/galoy-pay
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 06221a57a68708c983d2c6cdafd1e402a8474837
    includePaths:
    - ci/tasks/galoy-pay-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          06221a57a68708c983d2c6cdafd1e402a8474837
- path: modules/services/stablesats/vendor/stablesats
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 2f29c0438445c32f2177305b6960df56a9ab8656
    includePaths:
    - charts/stablesats/**/*
    newRootPath: charts/stablesats
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 2f29c0438445c32f2177305b6960df56a9ab8656
    includePaths:
    - ci/tasks/stablesats-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          2f29c0438445c32f2177305b6960df56a9ab8656
- path: modules/services/addons/vendor/api-dashboard
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 06221a57a68708c983d2c6cdafd1e402a8474837
    includePaths:
    - charts/api-dashboard/**/*
    newRootPath: charts/api-dashboard
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 06221a57a68708c983d2c6cdafd1e402a8474837
    includePaths:
    - ci/tasks/api-dashboard-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          06221a57a68708c983d2c6cdafd1e402a8474837
- path: modules/services/galoy-deps/vendor/galoy-deps
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 4c818bc401bfe7b26aaeef51090da6323579e535
    includePaths:
    - charts/galoy-deps/**/*
    newRootPath: charts/galoy-deps
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 4c818bc401bfe7b26aaeef51090da6323579e535
    includePaths:
    - ci/tasks/galoy-deps-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          4c818bc401bfe7b26aaeef51090da6323579e535
- path: modules/services/galoy-deps/vendor/strimzi-kafka-operator
  contents:
  - path: crds
    git:
      url: https://github.com/strimzi/strimzi-kafka-operator.git
      ref: 0.39.0
    includePaths:
    - helm-charts/helm3/strimzi-kafka-operator/crds/*.yaml
    newRootPath: helm-charts/helm3/strimzi-kafka-operator/crds
- path: modules/services/monitoring/vendor/kafka-connect
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: da666b837cceb71e299ff19bd309b5d22af020d3
    includePaths:
    - charts/kafka-connect/**/*
    newRootPath: charts/kafka-connect
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: da666b837cceb71e299ff19bd309b5d22af020d3
    includePaths:
    - ci/tasks/kafka-connect-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          da666b837cceb71e299ff19bd309b5d22af020d3
- path: modules/services/blink-addons/vendor/circles
  contents:
  - path: chart
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: 27920b700341087dc1ea9ebef59d786812c95708
    includePaths:
    - charts/circles/**/*
    newRootPath: charts/circles
  - path: ci
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: 27920b700341087dc1ea9ebef59d786812c95708
    includePaths:
    - ci/tasks/circles-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          27920b700341087dc1ea9ebef59d786812c95708
- path: modules/services/blink-addons/vendor/blink-fiat
  contents:
  - path: chart
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: a4bdf436fac8498280369be2b4bdecf51a38c3f3
    includePaths:
    - charts/blink-fiat/**/*
    newRootPath: charts/blink-fiat
  - path: ci
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: a4bdf436fac8498280369be2b4bdecf51a38c3f3
    includePaths:
    - ci/tasks/blink-fiat-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          a4bdf436fac8498280369be2b4bdecf51a38c3f3
- path: modules/services/blink-addons/vendor/blink-kyc
  contents:
  - path: chart
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: 27920b700341087dc1ea9ebef59d786812c95708
    includePaths:
    - charts/blink-kyc/**/*
    newRootPath: charts/blink-kyc
  - path: ci
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: 27920b700341087dc1ea9ebef59d786812c95708
    includePaths:
    - ci/tasks/blink-kyc-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          27920b700341087dc1ea9ebef59d786812c95708
- path: modules/services/addons/vendor/map
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 06221a57a68708c983d2c6cdafd1e402a8474837
    includePaths:
    - charts/map/**/*
    newRootPath: charts/map
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 06221a57a68708c983d2c6cdafd1e402a8474837
    includePaths:
    - ci/tasks/map-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          06221a57a68708c983d2c6cdafd1e402a8474837
- path: modules/services/addons/vendor/voucher
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 06221a57a68708c983d2c6cdafd1e402a8474837
    includePaths:
    - charts/voucher/**/*
    newRootPath: charts/voucher
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 06221a57a68708c983d2c6cdafd1e402a8474837
    includePaths:
    - ci/tasks/voucher-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          06221a57a68708c983d2c6cdafd1e402a8474837
- path: modules/services/cala/vendor/cala
  contents:
  - path: chart
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 5fd70375531a619f12d8ebdd6736edd310c1d364
    includePaths:
    - charts/cala/**/*
    newRootPath: charts/cala
  - path: ci
    git:
      url: https://github.com/blinkbitcoin/charts.git
      ref: 5fd70375531a619f12d8ebdd6736edd310c1d364
    includePaths:
    - ci/tasks/cala-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          5fd70375531a619f12d8ebdd6736edd310c1d364
- path: modules/services/cala/vendor/cala-enterprise
  contents:
  - path: chart
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: 7c01e1b47f6d50d80c348cb025cd2b6caa1f454f
    includePaths:
    - charts/cala-enterprise/**/*
    newRootPath: charts/cala-enterprise
  - path: ci
    git:
      url: **************:blinkbitcoin/private-charts.git
      ref: 7c01e1b47f6d50d80c348cb025cd2b6caa1f454f
    includePaths:
    - ci/tasks/cala-enterprise-smoketest.sh
    - ci/tasks/get-smoketest-settings.sh
    newRootPath: ci
  - path: git-ref
    inline:
      paths:
        ref: |
          7c01e1b47f6d50d80c348cb025cd2b6caa1f454f
