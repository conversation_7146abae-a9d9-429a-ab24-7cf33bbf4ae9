#!/bin/bash
# ./internal-swap-out.sh <lnd out peer public key> <(optional: default 1st param) lnd in peer public key> <(optional: default 500) max fee ppm>

namespace=galoy-bbw-bitcoin
lndout=lnd1-0
lndout_peer=$1
lndin=lnd2-0
lndin_peer=${2:-$lndout_peer}
min_rebalance=5000000
fee_ppm=${3:-500}

lndout_peer_alias=$(kubectl -n $namespace exec $lndout -c lnd -- lncli getnodeinfo $lndout_peer | jq -r .node.alias)
lndin_peer_alias=$(kubectl -n $namespace exec $lndout -c lnd -- lncli getnodeinfo $lndin_peer | jq -r .node.alias)
output_channels=$(kubectl -n $namespace exec $lndout -c lnd -- lncli listchannels --peer $lndout_peer)

# Query channel to drain
output_channel=$(echo $output_channels |  jq '[.channels[] | {id: .chan_id, capacity:.capacity|tonumber, balance:.local_balance|tonumber, reserve: .local_chan_reserve_sat|tonumber, point: .channel_point, node: .remote_pubkey} | select(.balance - .reserve > 10000000)] | sort_by(-.balance) | .[0]')
if test -z "$output_channel"
then
  echo "No output channel to rebalance";
  exit 1;
fi

# select max possible amount to rebalance
chanId=$(echo $output_channel |  jq -r ".id")
output_capacity=$(echo $input_channel |  jq -r ".capacity")
output_amount=$(echo $output_channel |  jq -r ".balance - .reserve")
if ! (( $output_amount >= $min_rebalance ))
then
  echo "Invalid output amount";
  exit 1;
fi

input_channels=$(kubectl -n $namespace exec $lndin -c lnd -- lncli listchannels --peer $lndin_peer)

# Query channel to fill
input_channel=$(echo $input_channels |  jq '[.channels[] | {id: .chan_id, capacity:.capacity|tonumber, balance:.remote_balance|tonumber, reserve: .remote_chan_reserve_sat|tonumber} | select(.balance - .reserve > 1000000)] | sort_by(.balance) | .[0]')
if test -z "$input_channel"
then
  echo "No input channel to rebalance";
  exit 1;
fi

input_chanId=$(echo $input_channel |  jq -r ".id")
input_capacity=$(echo $input_channel |  jq -r ".capacity")
input_amount=$(echo $input_channel |  jq -r ".balance - .reserve")

if ! (( $input_amount > 0 ))
then
  echo "Invalid input amount";
  exit 1;
fi

rebalance_amount=$input_amount
if (( $rebalance_amount > $output_amount ))
then
  rebalance_amount=$output_amount
fi
# rebalance_amount=$((rebalance_amount - reserve_amount))
if (( $rebalance_amount > 50000000 ))
then
  rebalance_amount=50000000
fi
fee=$((rebalance_amount * fee_ppm / 1000000))
rebalance_amount=$((rebalance_amount - fee))

echo "From $lndout_peer_alias ($lndout) to $lndin_peer_alias ($lndin)"
echo "Output channel id: $chanId"
echo "Max output amount: $output_amount"
echo "Input channel id: $input_chanId"
echo "Input max amount: $input_amount of $input_capacity"
echo "Rebalance amount: $rebalance_amount"
echo "Rebalance fee($fee_ppm ppm): $fee"

read -p "Are you sure you want to rebalance ?(Y/n) " -n 1 -r
echo    # (optional) move to a new line
if [[ ! $REPLY =~ ^[Yy]$ ]]
then
    echo "Canceled"
    [[ "$0" = "$BASH_SOURCE" ]] && exit 1 || return 1 # handle exits from shell or function but don't exit interactive shell
fi

invoice=$(kubectl -n $namespace exec $lndin -c lnd -- lncli addinvoice --memo "internal swap" $rebalance_amount | jq -r .payment_request)
echo "Invoice: $invoice"

kubectl -n $namespace exec $lndout -c lnd -- lncli payinvoice -f \
    --allow_self_payment \
    --fee_limit $fee \
    --outgoing_chan_id $chanId \
    --last_hop $lndin_peer \
    $invoice
