#!/bin/bash
# ./close-fanout.sh <peer publick key> <sat per v/byte for fanout tx>
# ./close-fanout.sh 03037dc08e9ac63b82581f79b662a4d0ceca8a8ca162b1af3551595b8f2d97b70a 1

namespace=galoy-bbw-bitcoin
lndin=lnd1-0
lndout=lnd2-0
fan_amount=1000000
min_fan_amount=100000
min_onchain_fan_amount=10000000
min_onchain_reserve=500000
# River1: 03037dc08e9ac63b82581f79b662a4d0ceca8a8ca162b1af3551595b8f2d97b70a
# River2: 03aab7e9327716ee946b8fbfae039b0db85356549e72c5cca113ea67893d0821e5
peer=$1
sat_per_vbyte=${2:-1}

channels=$(kubectl -n $namespace exec $lndout -c lnd -- lncli listchannels --peer $peer)
total_channels=$(echo $channels |  jq '.channels | length')
channel_to_close=$(echo $channels |  jq '[.channels[] | {id: .chan_id, capacity:.capacity|tonumber, balance:.local_balance|tonumber, reserve: .local_chan_reserve_sat|tonumber, point: .channel_point, pending: .pending_htlcs}] | sort_by(-.balance) | .[0]')
balance=$(echo $channel_to_close |  jq -r .balance)
chan_id=$(echo $channel_to_close |  jq -r .id)
chan_point=$(echo $channel_to_close |  jq -r .point)
pending=$(echo $channel_to_close |  jq -r '.pending | length')
echo "Peer: $peer"
echo "Total channels: $total_channels"
echo "Channel to close: $channel_to_close"
echo "Penindg HTLCs: $pending"
read -p "Are you sure you want to close $chan_id ($lndout) with $balance sats ?(Y/n) " -n 1 -r
echo    # (optional) move to a new line
if [[ $REPLY =~ ^[Yy]$ ]]
then
  echo "Executing close $chan_point"
  kubectl -n $namespace exec $lndout -c lnd -- lncli closechannel --chan_point $chan_point --conf_target 6
fi

# Fan out
totalbalance=$(kubectl -n $namespace exec $lndout -c lnd -- lncli walletbalance | jq -r .total_balance)
read -p "Are you sure you want to fanout $totalbalance($sat_per_vbyte sat/vbyte) to $lndin ?(Y/n) " -n 1 -r
echo    # (optional) move to a new line
if [[ ! $REPLY =~ ^[Yy]$ ]]
then
    echo "Canceled"
    [[ "$0" = "$BASH_SOURCE" ]] && exit 1 || return 1 # handle exits from shell or function but don't exit interactive shell
fi

amount=$((totalbalance - min_onchain_reserve))
if (( amount < min_onchain_fan_amount ))
then
  echo "Total balance too low $totalbalance";
  exit 1;
fi

echo "Total amount to fanout: $amount"
splits=""
while [ $amount -gt 0 ]
do
  key=$(kubectl -n $namespace exec $lndin --container lnd -- lncli newaddress p2wkh | jq .address -r)
  value=$(shuf -i 1500000-2500000 -n 1)
  sep=","
  if (( $value > $amount ))
  then
    value=$amount
    sep=""
  fi
  if (( $value >= $min_fan_amount ))
  then
    splits+="\"$key\":$value$sep"
  fi
  amount=$(( $amount - $fan_amount ))
done

outputs="{$splits}"
echo "Outputs: $outputs"

kubectl -n $namespace exec $lndout -c lnd -- lncli sendmany $outputs --min_confs 0 --sat_per_vbyte $sat_per_vbyte --label fanout
