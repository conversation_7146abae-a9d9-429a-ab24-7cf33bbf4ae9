# Swap out with River channels

## Check active node available balance and UTXOs

### Balance
```
k -n galoy-bbw-bitcoin exec lnd1-0 --container lnd -- lncli walletbalance
```

### UTXOs
```
k -n galoy-bbw-bitcoin exec lnd1-0 --container lnd -- lncli listunspent | jq '[.utxos[] | select(.confirmations > 0)] | length'
```

## Check balances of River's channels

### River's nodes
- River Financial 1: `03037dc08e9ac63b82581f79b662a4d0ceca8a8ca162b1af3551595b8f2d97b70a`
- River Financial 2: `03aab7e9327716ee946b8fbfae039b0db85356549e72c5cca113ea67893d0821e5`

### Outbound liquidity

You need to validate if we have enough outbound liquidity in River's channels for our main node

```
bos peers --node lnd1 --sort outbound_liquidity
```

by default River is opening channels with 75M sats so if we are below that value you need to be careful of not running out of outbound liquidity (payments to chivo)

### Inbound liquidity

you need o validate if we have enough inbound liquidity in River's channels for our second node. We use lnd2 because its channels are setup with less routing fees than lnd1 and also is easier/cleaner to do the fan out.

```
bos peers --node lnd2 --sort outbound_liquidity
```
note: if you have issues connecting to lnd2 please check readme bos instructions

## Swap out

River is opening channels automatically if they dont have outbound liquidity with us so is recommended to always have at least 1 channel ready to be closed in lnd2 because if we close all channels with them will take more time to get new channels.

```
# example of 2 channels 1 empty and 1 ready to be closed
│ Alias                    │ Inbound    │ In Fee       │ Outbound   │ Public Key
│ River Financial 1        │ 0.******** │ 0.01% (150)  │ 0.******** │ 03037dc08e9ac63b82581f79b662a4d0ceca8a8ca162b1af3551595b8f2d97b70a
```
### Rebalance
[internal-swap-out.sh](./swap-scripts/internal-swap-out.sh) script help us to rebalance from lnd1 (main node) to lnd2 (node used to do the swap out):

```
./internal-swap-out.sh <lnd out peer public key> <(optional: default 1st param) lnd in peer public key> <(optional: default 500) max fee ppm>

```
Examples:
```
# From River Financial 1 (lnd1) to River Financial 1 (lnd2)
./internal-swap-out.sh 03037dc08e9ac63b82581f79b662a4d0ceca8a8ca162b1af3551595b8f2d97b70a

# From River Financial 2 (lnd1) to River Financial 2 (lnd2)
./internal-swap-out.sh 03aab7e9327716ee946b8fbfae039b0db85356549e72c5cca113ea67893d0821e5

# From River Financial 1 (lnd1) to River Financial 2 (lnd2)
./internal-swap-out.sh 03037dc08e9ac63b82581f79b662a4d0ceca8a8ca162b1af3551595b8f2d97b70a 03aab7e9327716ee946b8fbfae039b0db85356549e72c5cca113ea67893d0821e5
```
notes:
- please check available outbound liquidity (main offchain node) in the channels before decide what to execute because River sometimes change outbound node.
- rebalance is limited to 50M sats so please run the script until the destination channel is full. This was setup to avoid risk more than 50M sats in case HTLC get stuck (pending)

### Fan out

[close-fanout.sh](./swap-scripts/close-fanout.sh.sh) script help us to close the channel with most outbound liquidity and then fan out to our main onchain node.
```
./close-fanout.sh <peer publick key> <sat per v/byte for fanout tx>
```
Examples:
```
# Close River Financial 1 (lnd2) channel and fan out with 21 sats/vbyte to lnd1 onchain wallet
./close-fanout.sh 03037dc08e9ac63b82581f79b662a4d0ceca8a8ca162b1af3551595b8f2d97b70a 21

# Close River Financial 2 (lnd2) channel and fan out with 1 sats/vbyte to lnd1 onchain wallet
./close-fanout.sh 03aab7e9327716ee946b8fbfae039b0db85356549e72c5cca113ea67893d0821e5 1
```
Note: closing tx is always done with 1 sat/vbyte and fan out tx use CPFP so the effective rate is always less than the setup value (please check mempool before decide)
