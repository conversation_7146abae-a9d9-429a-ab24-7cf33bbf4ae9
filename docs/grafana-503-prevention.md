# Preventing Grafana 503 Errors in Terraform

## Problem Description

When deploying Grafana resources via Terraform, you may encounter 503 Service Temporarily Unavailable errors. This typically happens when:

1. <PERSON><PERSON> is still starting up after Helm deployment
2. There's no retry mechanism in the Grafana provider
3. Insufficient wait time between Helm deployment and resource creation
4. Missing readiness checks for Grafana availability

## Prevention Strategies Implemented

### 1. Grafana Provider Retry Configuration

Added retry configuration to both Grafana provider instances:

```hcl
provider "grafana" {
  url                  = local.grafana_url
  auth                 = "admin:${random_password.grafana.result}"
  insecure_skip_verify = true
  
  # Add retry configuration to handle temporary unavailability
  retry_wait_min_seconds = 1
  retry_wait_max_seconds = 30
  retry_max              = 10
}
```

**Benefits:**
- Automatically retries failed requests up to 10 times
- Uses exponential backoff (1-30 seconds)
- Handles temporary network issues and service startup delays

### 2. Explicit Grafana Readiness Check

Added a `null_resource` with health check before creating Grafana resources:

```hcl
resource "null_resource" "wait_for_grafana" {
  depends_on = [helm_release.monitoring]
  
  provisioner "local-exec" {
    command = <<-EOT
      echo "Waiting for <PERSON><PERSON> to be ready..."
      for i in {1..30}; do
        if curl -f -s -k --max-time 10 "${local.grafana_url}/api/health" > /dev/null 2>&1; then
          echo "Grafana is ready!"
          exit 0
        fi
        echo "Attempt $i: Grafana not ready yet, waiting 10 seconds..."
        sleep 10
      done
      echo "Grafana failed to become ready after 5 minutes"
      exit 1
    EOT
  }
}
```

**Benefits:**
- Ensures Grafana is fully operational before resource creation
- Provides clear feedback on readiness status
- Fails fast if Grafana doesn't become ready within 5 minutes

### 3. Enhanced Resource Dependencies

Updated all Grafana resources to depend on the readiness check:

```hcl
resource "grafana_data_source" "stackdriver" {
  # ... configuration ...
  
  depends_on = [
    helm_release.monitoring,
    null_resource.wait_for_grafana
  ]
}
```

**Benefits:**
- Ensures proper ordering of resource creation
- Prevents race conditions between Helm deployment and Grafana resource creation

### 4. Resource Timeouts and Lifecycle Rules

Added timeouts and lifecycle management:

```hcl
resource "grafana_data_source" "stackdriver" {
  # ... configuration ...
  
  timeouts {
    create = "5m"
    update = "5m"
    delete = "5m"
  }

  lifecycle {
    create_before_destroy = true
  }
}
```

**Benefits:**
- Allows sufficient time for resource operations
- Prevents resource conflicts during updates
- Ensures zero-downtime deployments

## Additional Recommendations

### 1. Monitor Grafana Startup Time

Consider adding monitoring for Grafana startup time to identify patterns:

```bash
# Check Grafana pod startup time
kubectl get pods -n monitoring -l app.kubernetes.io/name=grafana -o jsonpath='{.items[*].status.containerStatuses[*].state.running.startedAt}'
```

### 2. Increase Grafana Resource Limits

If 503 errors persist, consider increasing Grafana's resource limits in the Helm values:

```yaml
grafana:
  resources:
    limits:
      cpu: 1000m
      memory: 1Gi
    requests:
      cpu: 500m
      memory: 512Mi
```

### 3. Add Grafana Readiness Probe

Ensure Grafana has proper readiness probes configured:

```yaml
grafana:
  readinessProbe:
    httpGet:
      path: /api/health
      port: 3000
    initialDelaySeconds: 30
    periodSeconds: 10
    timeoutSeconds: 5
    failureThreshold: 3
```

### 4. Use Terraform Retry Logic

For critical deployments, consider using Terraform's built-in retry mechanisms:

```bash
# Retry terraform apply with exponential backoff
terraform apply -auto-approve || sleep 30 && terraform apply -auto-approve
```

## Troubleshooting

### If 503 Errors Still Occur

1. **Check Grafana logs:**
   ```bash
   kubectl logs -n monitoring deployment/monitoring-grafana
   ```

2. **Verify Grafana service status:**
   ```bash
   kubectl get svc -n monitoring monitoring-grafana
   curl -k https://your-grafana-url/api/health
   ```

3. **Check resource constraints:**
   ```bash
   kubectl top pods -n monitoring
   kubectl describe pod -n monitoring -l app.kubernetes.io/name=grafana
   ```

4. **Increase wait time:**
   Modify the wait loop in `null_resource.wait_for_grafana` to wait longer.

### Emergency Recovery

If Grafana resources are stuck in a bad state:

```bash
# Remove problematic resources from state
terraform state rm module.monitoring.grafana_data_source.stackdriver
terraform state rm module.monitoring.grafana_dashboard.main

# Re-import after Grafana is stable
terraform import module.monitoring.grafana_data_source.stackdriver <datasource-id>
terraform import module.monitoring.grafana_dashboard.main <dashboard-uid>
```

## Files Modified

- `modules/services/monitoring/dashboards.tf` - Added retry config, wait resource, timeouts
- `modules/services/addons/main.tf` - Added retry config to Grafana provider
- `docs/grafana-503-prevention.md` - This documentation file

## Testing

After implementing these changes, test the deployment:

1. Deploy to a test environment
2. Monitor Grafana startup time
3. Verify all resources are created successfully
4. Test dashboard functionality

The combination of these strategies should significantly reduce or eliminate 503 errors during Terraform deployments.
