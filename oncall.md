# On Call procedures and hand off info

Being on call means you will receive the first notification that indicates something might be going wrong with our service.
Please ACK the alert in pagerduty ASAP since if you don't it will escalate to your backup in 20 minutes (who might be asleep).

Once you have ACKed an alert it is generally your responsibility to perform an initial asessment (or explicitly hand it off if you are currently not in a position to do so).
If you conclude on initial assesment that there is a real customer-effecting service degredation in flight, take action to bring together the ppl necesary to fix things ASAP.
If you can determin the cause of the alert is not infact critical, reflect on the nature of the alert and wether or not it made sense to be there in the first place.

During the week on call your primary task is to take action on the alerts which may involve taking the initiative to improve and/or fix alert problems that affect, degrade, or may likely disrupt the production pipeline.
Any to-dos that fall out of the individual alerts should be executed right away (eg initiating an incident response or fixing config to supress a false positive or improve the alert to be able to understand quicker what is going on next time someone sees it).

When you are not responding to alerts you can reflect on the overall state of the alerting system and working on general improvements if you can identify some.
If you feel there is nothing for you to do to improve the on call experience continuing with other non-alert related tasks is also fine.

## Pagerduty
Each environment is represented by a `service` [in pagerduty](https://galoy.pagerduty.com/service-directory).
Events are routed from honeycomb via a [global ruleset](https://galoy.pagerduty.com/rules/rulesets/_default).

Wether or not an alert is triggered is currently dependent on the `urgency` setting of the `service`.
Current high urgency environments:
- galoy-bbw

Current low urgency environments (shouldn't wake anyone up):
- galoy-staging

The per environment config for pagerduty (ie. `service`, `ruleset_rule` and `urgency`) are [configured here](./modules/services/monitoring/pagerduty.tf)

## Honeycomb triggers

Honeycomb `triggers` (that get forwarded and routed through pageduty) are [configured here](./modules/services/monitoring/honeycomb_triggers.tf).

## Kubemonkey

Be aware that kubemonkey runs every week day from 9UTC - 11UTC and randomly kills pods on `galoy-staging` and `galoy-bbw`.
This regularly causes alerts.
If you get an alert in this time its advisable to check the #kubemonkey-notifications channel in slack to see if a service going down was the root cause.
