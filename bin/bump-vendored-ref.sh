#!/bin/bash

TMPDIR=""
TMPDIR=$(mktemp -d -t repipe.XXXXXX)
trap "rm -rf ${TMPDIR}" INT TERM QUIT EXIT

chart=$(echo $1 | sed 's/-/_/g')

sed "s/^${chart}_git_ref:.*/${chart}_git_ref: ${2}/" vendir/values.yml > ${TMPDIR}/new_values.yml

galoy_deps_git_ref=$(yq ${TMPDIR}/new_values.yml -o json | jq -r '.galoy_deps_git_ref')
curl -L -o ${TMPDIR}/Chart.yaml \
  "https://raw.githubusercontent.com/blinkbitcoin/charts/${galoy_deps_git_ref}/charts/galoy-deps/Chart.yaml"

strimzi_kafka_operator_version=$(yq  ${TMPDIR}/Chart.yaml -o json | jq -r '.dependencies[] | select(.name == "strimzi-kafka-operator") | .version')

sed "s/^strimzi_kafka_operator_version:.*/strimzi_kafka_operator_version: ${strimzi_kafka_operator_version}/" ${TMPDIR}/new_values.yml > ${TMPDIR}/new_values_with_strimzi.yml

mv ${TMPDIR}/new_values_with_strimzi.yml vendir/values.yml
