#!/bin/bash

set -eu

REPO_ROOT=$(git rev-parse --show-toplevel)
REPO_ROOT_DIR="${REPO_ROOT##*/}"

: ${BASTION_USER:?- Bastion user is not set}

if [[ ${BASTION_ZONE:-""} == "" ]]; then
  pushd gcp/${1}/inception > /dev/null
  BASTION_ZONE=$(tofu output bastion_zone | jq -r)
  GCP_PROJECT=$(tofu output gcp_project | jq -r)
  popd > /dev/null
fi

gcloud compute start-iap-tunnel ${1}-bastion --zone=${BASTION_ZONE} --project=${GCP_PROJECT} 22 --local-host-port=localhost:2222 &
trap 'jobs -p | xargs kill' EXIT

sleep 5

ADDITIONAL_SSH_OPTS=${ADDITIONAL_SSH_OPTS:-""}
echo "Syncing ${REPO_ROOT##*/} to bastion"
rsync --exclude '**/.terraform/**' --exclude '**.terrafor*' -avr -e "ssh ${ADDITIONAL_SSH_OPTS} -p 2222" \
  ${REPO_ROOT}/ ${BASTION_USER}@localhost:${REPO_ROOT_DIR}
