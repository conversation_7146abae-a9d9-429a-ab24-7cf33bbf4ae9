deployment: addons
environments:
  gcp-galoy-staging:
    latest:
    - modules/services/addons/**
    - modules/infra/vendor/tf/postgresql/**

    - gcp/galoy-staging/shared/*
    - gcp/galoy-staging/addons/*

  gcp-galoy-bbw:
    passed: gcp-galoy-staging
    ignore_queue: true
    propagated:
    - modules/services/addons/**
    - modules/infra/vendor/tf/postgresql/**
    latest:
    - gcp/galoy-bbw/shared/*
    - gcp/galoy-bbw/addons/*

  admin-panel-release:
    passed: gcp-galoy-bbw
    propagated:
      - modules/services/addons/vendor/admin-panel/git-ref/ref

  galoy-pay-release:
    passed: gcp-galoy-bbw
    propagated:
      - modules/services/addons/vendor/galoy-pay/git-ref/ref

  dealer-release:
    passed: gcp-galoy-bbw
    propagated:
    - modules/services/addons/vendor/dealer/git-ref/ref
