---
version: 14
current:
  head_commit: b0abd7ca7a1a0e32d1cadd950946b7f88b6feb6b
  propagated_head: 61e33e455066585f701cb076d8d49fe1b295ae0a
  files:
    "{gcp-galoy-staging}/bin/sync-to-bastion.sh":
      file_hash: f63cb7bc88bc55fe67319a0f71dcc399c7e55514
      from_commit: b8c397817799c39d58e6609e297b50a00bf5eda8
      message: "fix: move to tofu"
    "{gcp-galoy-staging}/ci/tasks/workers.sh":
      file_hash: 35d2cef76b061409137dc62145f1bc1f71a6543e
      from_commit: 61e33e455066585f701cb076d8d49fe1b295ae0a
      message: "refactor  switch to tofu (#7554)"
    "{gcp-galoy-staging}/modules/services/workers/main.tf":
      file_hash: fa3e02a06af464ba62c724f78f88d28ec0d2fd93
      from_commit: 9d3313da6c2ffdcfa596fe92be610ce14e374e52
      message: "feat: vm based workers (#6742)"
    "{gcp-galoy-staging}/modules/services/workers/workers-values.yml.tmpl":
      file_hash: dac1c47f2c82a8ac0e00a9bedb7b7c8324805f02
      from_commit: a7da6ee2584f3a0e75967d2d46f238abf2e85867
      message: "chore: use containerd runtime"
    "{latest}/gcp/galoy-bbw/shared/main.tf":
      file_hash: cfaac3c223a0bcb483083290adab8160cfacf569
      from_commit: 697d1c38e1d448ae0958182a5948bbdbd7a40c2c
      message: "chore: remove US from blocklist for now (#7072)"
    "{latest}/gcp/galoy-bbw/workers/main.tf":
      file_hash: 2c8fb98849ce7f060d691f9c6cb877f70bbc9f74
      from_commit: a8df37163be0d5ede239db5b3a1e8cb2ef700ea6
      message: Move folder and env name in cepler fow galoy-bbw
    "{latest}/gcp/galoy-bbw/workers/workers-scaling.yml":
      file_hash: 203ebd09931d6045a98ee1681d9680f517114988
      from_commit: 20d1b95dd694ae9fc0850a22a2e13d42b8fad981
      message: "chore: bump bbw worker"
propagated_from: gcp-galoy-staging

