---
version: 127
current:
  head_commit: 6514f6cf8265ca682de296b05459bb6a0a59578f
  propagated_head: f2643fbbf18b40b3588c7d1b3f50c98fdfcb289e
  files:
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/create-dms.sh":
      file_hash: 71e1a07123d132031eee636ac4ce98d2300dd337
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/postgres-perms-update.sh":
      file_hash: 3d90741c6affcb8a65219327097726ec54c5df68
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-db-swap.sh":
      file_hash: 91dd520580913c7a6a945d33d4595a8655073838
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-state-rm.sh":
      file_hash: c80c2912e7b807fc3e516fe93b218a4d7afd2b87
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/database/main.tf":
      file_hash: 9a0426f4e89c88fae5681084c6102a74181fcfba
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/main.tf":
      file_hash: af19399be2b3c580d4cb438a1cb44363e83f6bdb
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/migration/main.tf":
      file_hash: e1b48f6e0a08249e8d2b8794dfc63b23d051448a
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/outputs.tf":
      file_hash: 45d03b0075d0e46b7ff53dcf19b70ce96c304617
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/read-replica.tf":
      file_hash: 541c04251858b2cbde90ae3e74a48147d91093f3
      from_commit: 42e395bea256af0c89e50583aa8bf46077413305
      message: "chore(deps): bump galoy-infra modules to 'e76b37a'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/variables.tf":
      file_hash: 5d0fe5d208387d1e2776b1d9af5acb1bb598998d
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/cert-manager-values.yml.tmpl":
      file_hash: 2a2db1bc859ccc19b7045799a92a80cd1fb0a00b
      from_commit: 4f4d2d0f7c4ecdbfbdc340018c0b05ebcec58738
      message: "fix: galoy-deps trigger_ref missing"
    "{gcp-galoy-staging}/modules/services/galoy-deps/ingress-values.yml.tmpl":
      file_hash: d0493ae56a6f6de80375135bfca137b45bb37b55
      from_commit: 53f29e253f61e849471d704ad3053c41d0d814d0
      message: "chore: dummy commit to reset bbw ingress"
    "{gcp-galoy-staging}/modules/services/galoy-deps/ingress.tf":
      file_hash: a40fed7a8a926bd50ef4e079bfe4ee59cd55b79c
      from_commit: 914b95d26f72c7063698a6e27337932624999ca8
      message: "revert: \"chore: otlp config for ingress nginx\""
    "{gcp-galoy-staging}/modules/services/galoy-deps/kafka-values.yml.tmpl":
      file_hash: b643aceddd82b75850fee3a3cc76464d04eed8db
      from_commit: 4f4d2d0f7c4ecdbfbdc340018c0b05ebcec58738
      message: "fix: galoy-deps trigger_ref missing"
    "{gcp-galoy-staging}/modules/services/galoy-deps/kafka.tf":
      file_hash: b3d4d225e136511d106ed0c660592acb641cdf79
      from_commit: e7078cfd524c3a4d3202256a2e06ccc21e72241c
      message: "chore: galoy-deps module for volcano (#7186)"
    "{gcp-galoy-staging}/modules/services/galoy-deps/kubemonkey-values.yml.tmpl":
      file_hash: a75df8d7325bf26cb9d15198bd9193b4b1a8b6a7
      from_commit: d0798c8f236c6a7e793f4a17c8c85658c03bcdda
      message: "chore: set kubemonkey dryRun false"
    "{gcp-galoy-staging}/modules/services/galoy-deps/kubemonkey.tf":
      file_hash: 757e827fd606c7fbf9e7a1a93186b865ecf64445
      from_commit: 7df1e47ee7121c62edd6827ca5ac06f81cfd9999
      message: "chore: add galoy-deps resources (#3907)"
    "{gcp-galoy-staging}/modules/services/galoy-deps/otel-values.yml.tmpl":
      file_hash: 4b2ef6526fe9dcd518a62211c76d9d5a08f3d03e
      from_commit: db973233f3b279027651157621c274bab3f21a0f
      message: "chore: Rollback preset for k8s_events (#7965)"
    "{gcp-galoy-staging}/modules/services/galoy-deps/otel.tf":
      file_hash: 199e7c9702ef98280b458ef5f35d7c2d4ea1c32d
      from_commit: 0b735aa4c074227438273322cebc75dd4423c074
      message: "chore: unify honeycomb datasets (#6683)"
    "{gcp-galoy-staging}/modules/services/galoy-deps/variables.tf":
      file_hash: 332eb67351d067a1379171fe4d946756718529c3
      from_commit: 8c6f870636cf5571ee0b7f434b24eaeef840c6de
      message: "chore: remove bitcoin ns from kubemonkey whitelist (#7218)"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/galoy-deps/chart/Chart.lock":
      file_hash: 1a0f6ea41468cf86893fd5ea1540c8b947dd13fc
      from_commit: a8560d8a28a692fde02650877ae87d358842a666
      message: "chore: bump galoy-deps-chart to '0f207575325ee796fadfb3cc6d4ac3f88911f7f2'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/galoy-deps/chart/Chart.yaml":
      file_hash: 9d8ad906cb8867540624f2962417529ff0ff8292
      from_commit: a8560d8a28a692fde02650877ae87d358842a666
      message: "chore: bump galoy-deps-chart to '0f207575325ee796fadfb3cc6d4ac3f88911f7f2'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/galoy-deps/chart/templates/NOTES.txt":
      file_hash: e69de29bb2d1d6434b8b29ae775ad8c2e48c5391
      from_commit: 5c27be5c0cc66cabc5751fd6f9f36719f7fa8182
      message: "chore: bump galoy-deps-chart to 'a4fb953639c8019610e954c61c3eee04bdac6d1f'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/galoy-deps/chart/templates/_helpers.tpl":
      file_hash: 66ea0f52c0cf165d062dcaf1178e3fd34eb06e8e
      from_commit: 5c27be5c0cc66cabc5751fd6f9f36719f7fa8182
      message: "chore: bump galoy-deps-chart to 'a4fb953639c8019610e954c61c3eee04bdac6d1f'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/galoy-deps/chart/templates/kafka-cluster.yaml":
      file_hash: 6905f7ff730ad4e676f02412e392438fcab81877
      from_commit: 10dc200aae7222c3caff05c48a64cbd96d215894
      message: "chore: bump galoy-deps-chart to 'a6e3752771e687697ffe4b384505a6cf2c50ce8e'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/galoy-deps/chart/values.yaml":
      file_hash: a5d758bb159f3ac8408021d05d6a229c21bc1d7e
      from_commit: f2643fbbf18b40b3588c7d1b3f50c98fdfcb289e
      message: "chore: bump galoy-deps-chart to '4c818bc401bfe7b26aaeef51090da6323579e535'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/galoy-deps/ci/tasks/galoy-deps-smoketest.sh":
      file_hash: 1e0327adce2270997e47d378c4be1000c902bb63
      from_commit: f3cf88d72e98b7ec351f1952e061243a1781cba0
      message: "chore: bump galoy-deps-chart to '1667b71bc1101a36d47fed2cad34869360392107'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/galoy-deps/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 5c27be5c0cc66cabc5751fd6f9f36719f7fa8182
      message: "chore: bump galoy-deps-chart to 'a4fb953639c8019610e954c61c3eee04bdac6d1f'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/galoy-deps/git-ref/ref":
      file_hash: cb50b7f30defe353e4d2b7f79c1c5ae5bce5d8f2
      from_commit: f2643fbbf18b40b3588c7d1b3f50c98fdfcb289e
      message: "chore: bump galoy-deps-chart to '4c818bc401bfe7b26aaeef51090da6323579e535'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/strimzi-kafka-operator/crds/040-Crd-kafka.yaml":
      file_hash: e01deee37cb224539ad3bf73d4c3066fb297394f
      from_commit: 3b07a153c887a7480914ecb92901cce73a6bd89f
      message: "chore: bump galoy-deps-chart to '5db637552e1539efe0c958af87b65995f9eb2692'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/strimzi-kafka-operator/crds/041-Crd-kafkaconnect.yaml":
      file_hash: a6b891873c8e8ad9b5913446a4fc755d4a8a0890
      from_commit: 3b07a153c887a7480914ecb92901cce73a6bd89f
      message: "chore: bump galoy-deps-chart to '5db637552e1539efe0c958af87b65995f9eb2692'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/strimzi-kafka-operator/crds/042-Crd-strimzipodset.yaml":
      file_hash: 8234a8be6a146c8ba41feb7ab1b967c2d996ad1d
      from_commit: 34499a522125bf949c95898fe4493281b18c0c41
      message: "chore: bump galoy-deps-chart to 'd815ec2a57088d4ab68162697adfba2157bca103'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/strimzi-kafka-operator/crds/043-Crd-kafkatopic.yaml":
      file_hash: 3ded35f9ef2810d8240756b57d0b48d663e0f34c
      from_commit: b6d8d0c78f29666d1e87eb3fb10b58f6c64a89fb
      message: "ci: sync strimzi-kafka-operator via vendir"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/strimzi-kafka-operator/crds/044-Crd-kafkauser.yaml":
      file_hash: f6707d5583dad696dfe2d08d143163127c0b11bf
      from_commit: 10dc200aae7222c3caff05c48a64cbd96d215894
      message: "chore: bump galoy-deps-chart to 'a6e3752771e687697ffe4b384505a6cf2c50ce8e'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/strimzi-kafka-operator/crds/045-Crd-kafkamirrormaker.yaml":
      file_hash: ee612421488d0458ce850ca6c475f6b975914422
      from_commit: 3b07a153c887a7480914ecb92901cce73a6bd89f
      message: "chore: bump galoy-deps-chart to '5db637552e1539efe0c958af87b65995f9eb2692'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/strimzi-kafka-operator/crds/046-Crd-kafkabridge.yaml":
      file_hash: 26d22e0383cd502dc8f498e6d9414a74ffd1a5b3
      from_commit: 3b07a153c887a7480914ecb92901cce73a6bd89f
      message: "chore: bump galoy-deps-chart to '5db637552e1539efe0c958af87b65995f9eb2692'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/strimzi-kafka-operator/crds/047-Crd-kafkaconnector.yaml":
      file_hash: 999101d197f2ab85de97d82e22990a8165e2b09b
      from_commit: 10dc200aae7222c3caff05c48a64cbd96d215894
      message: "chore: bump galoy-deps-chart to 'a6e3752771e687697ffe4b384505a6cf2c50ce8e'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/strimzi-kafka-operator/crds/048-Crd-kafkamirrormaker2.yaml":
      file_hash: b1c38c6ae61158b4a2a53f4cc6558a1acd7c3efe
      from_commit: 3b07a153c887a7480914ecb92901cce73a6bd89f
      message: "chore: bump galoy-deps-chart to '5db637552e1539efe0c958af87b65995f9eb2692'"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/strimzi-kafka-operator/crds/049-Crd-kafkarebalance.yaml":
      file_hash: ee95e2ae6ad52c80c262c1780c56f326e557e842
      from_commit: b6d8d0c78f29666d1e87eb3fb10b58f6c64a89fb
      message: "ci: sync strimzi-kafka-operator via vendir"
    "{gcp-galoy-staging}/modules/services/galoy-deps/vendor/strimzi-kafka-operator/crds/04A-Crd-kafkanodepool.yaml":
      file_hash: 239749c85100aaaf920569cc0afac669c3dbe044
      from_commit: 3b07a153c887a7480914ecb92901cce73a6bd89f
      message: "chore: bump galoy-deps-chart to '5db637552e1539efe0c958af87b65995f9eb2692'"
    "{latest}/gcp/galoy-bbw/galoy-deps/ingress-scaling.yml":
      file_hash: 720cd3f7d21f36d62de8da060332ba1754a81d31
      from_commit: 9e0c7687c9edee4efee989e252aa26d439296066
      message: "chore: adjust resource limits (#3917)"
    "{latest}/gcp/galoy-bbw/galoy-deps/kafka-scaling.yml":
      file_hash: f814c94f9ef5df6e4caa58c3d7ee90b45460ed8c
      from_commit: 7df1e47ee7121c62edd6827ca5ac06f81cfd9999
      message: "chore: add galoy-deps resources (#3907)"
    "{latest}/gcp/galoy-bbw/galoy-deps/kubemonkey-scaling.yml":
      file_hash: f3bdae80439362b226eb39924d952d203a373588
      from_commit: 9e0c7687c9edee4efee989e252aa26d439296066
      message: "chore: adjust resource limits (#3917)"
    "{latest}/gcp/galoy-bbw/galoy-deps/main.tf":
      file_hash: f71af6a4f0e779b5498aa021d8695d80a99091cc
      from_commit: 1e68c942a0788b681d8b8c82a2fe5f2073605b60
      message: "refactor: remove locals block in gcp galoy-deps"
    "{latest}/gcp/galoy-bbw/galoy-deps/otel-scaling.yml":
      file_hash: b858e40be1ddaf2193a9a2626ff23409d3d143a5
      from_commit: 7df1e47ee7121c62edd6827ca5ac06f81cfd9999
      message: "chore: add galoy-deps resources (#3907)"
    "{latest}/gcp/galoy-bbw/shared/main.tf":
      file_hash: cfaac3c223a0bcb483083290adab8160cfacf569
      from_commit: 697d1c38e1d448ae0958182a5948bbdbd7a40c2c
      message: "chore: remove US from blocklist for now (#7072)"
propagated_from: gcp-galoy-staging

