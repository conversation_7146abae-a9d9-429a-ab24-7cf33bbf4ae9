---
version: 201
current:
  head_commit: dd19c241ed9c92dfc9d0416d831d318f61eb23e7
  propagated_head: 74b2d505b450a0556f3b8110b93b41992649a7a0
  files:
    "{gcp-galoy-staging}/modules/services/monitoring/cloudsql-alerts.tf":
      file_hash: bdb515536f9194ab1680f8b5e9b8d2d01f8dd562
      from_commit: 97381e064297871615f27f4af6aec76692b0c935
      message: "fix: set gcp project on monitoring policy"
    "{gcp-galoy-staging}/modules/services/monitoring/dashboard.json":
      file_hash: 54fb154c653b1f38d02880465c578c3ee3a95266
      from_commit: 4b5ee83e20a2192d9bcfc97daba7bf0644a1fde1
      message: "feat: add pending incoming/outgoing htlc to grafana (#4533)"
    "{gcp-galoy-staging}/modules/services/monitoring/dashboards.tf":
      file_hash: 29bc998d838e6d71d61ef21e4aa6dddf00a0f581
      from_commit: 3237dd87cc85ef79bda1a83a17da3e1d39f60874
      message: "chore: remove lndmon config and dashboards (#2675)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/boards_bria.tf":
      file_hash: 435d18e3069d024fb36009c47b3f4d287efdd70b
      from_commit: 9c83d46a51b44ac535aeb42382db0b5e442daf87
      message: "chore: adapt boards and triggers to the new Honeycomb env (#7781)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/boards_event_queues.tf":
      file_hash: 75259c3520104fdd813150c8f4419c48238ca879
      from_commit: 9c83d46a51b44ac535aeb42382db0b5e442daf87
      message: "chore: adapt boards and triggers to the new Honeycomb env (#7781)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/boards_gql.tf":
      file_hash: 5b55c1b47674b5dba46886a3192fdb5bcd277528
      from_commit: 9c83d46a51b44ac535aeb42382db0b5e442daf87
      message: "chore: adapt boards and triggers to the new Honeycomb env (#7781)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/boards_ingress.tf":
      file_hash: b49819c03c68f00637be3ee630416b3024789ef1
      from_commit: 9c83d46a51b44ac535aeb42382db0b5e442daf87
      message: "chore: adapt boards and triggers to the new Honeycomb env (#7781)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/boards_metrics.tf":
      file_hash: 320f20be89d18b0bc739ce5ad1863e009be70816
      from_commit: b08c8e99b988372ac628783e1ef26cae565bd1ea
      message: "fix: honeycomb column names (#6686)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/boards_payments.tf":
      file_hash: 41eff8bdb45bdb95fd8be2f8c1322a423f7a3259
      from_commit: c3f3ffcbffc8bc5766f81976c79612fcd1c4ef99
      message: "chore: fix missed dataset name changes (#7787)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/boards_stablesats.tf":
      file_hash: 3d9c5938ab63069ab4f475b56890c5dc0fc89676
      from_commit: c3f3ffcbffc8bc5766f81976c79612fcd1c4ef99
      message: "chore: fix missed dataset name changes (#7787)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/boards_users.tf":
      file_hash: a4a861fbb485cf3a31321eacf721bce0d023faac
      from_commit: 9c83d46a51b44ac535aeb42382db0b5e442daf87
      message: "chore: adapt boards and triggers to the new Honeycomb env (#7781)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/derived_columns.tf":
      file_hash: 6ed1cf99c3b790b9345e82523c5060215d47f4e4
      from_commit: 9c83d46a51b44ac535aeb42382db0b5e442daf87
      message: "chore: adapt boards and triggers to the new Honeycomb env (#7781)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/main.tf":
      file_hash: 61a05cd342e5e5be35a7df06bb8f169f858b102a
      from_commit: 00bd1af51a08b52cfee13abea4553b283ea1389b
      message: "chore: remove honeycomb agent (#6679)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/triggers_bria.tf":
      file_hash: 2d5b08712020c10ece0ff0df95cb8bed631e1d11
      from_commit: 1add5fef661290b4922532d39862b0192f63baed
      message: "ci: update references to blink-deployments (#7979)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/triggers_cronjob.tf":
      file_hash: 4fb4ef5daa52124c38f90fa78ae785fc21248f9a
      from_commit: b110a6e5e2915e98869672d6d12c794eec6c1fec
      message: "chore: remove slack alerts from honeycomb recipients (#7786)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/triggers_galoy.tf":
      file_hash: bd15104475676a81c412e78408fb811782a0a9d6
      from_commit: b110a6e5e2915e98869672d6d12c794eec6c1fec
      message: "chore: remove slack alerts from honeycomb recipients (#7786)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/triggers_ingress.tf":
      file_hash: 50a399c76e7e4be7251ad99931a3f4aafc651b2e
      from_commit: b110a6e5e2915e98869672d6d12c794eec6c1fec
      message: "chore: remove slack alerts from honeycomb recipients (#7786)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/triggers_lnd.tf":
      file_hash: f2568a6571b83e3fe484058c0697f8bf9efe1c6a
      from_commit: b110a6e5e2915e98869672d6d12c794eec6c1fec
      message: "chore: remove slack alerts from honeycomb recipients (#7786)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/triggers_mongodb.tf":
      file_hash: db635d5bca01aacd16d72f6431566d7fd7b1680b
      from_commit: b110a6e5e2915e98869672d6d12c794eec6c1fec
      message: "chore: remove slack alerts from honeycomb recipients (#7786)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/triggers_nginx.tf":
      file_hash: 553e611391f62ac4cbeb9f2a57cb1911c51690d6
      from_commit: b110a6e5e2915e98869672d6d12c794eec6c1fec
      message: "chore: remove slack alerts from honeycomb recipients (#7786)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/triggers_payments.tf":
      file_hash: 28748403e2c344fe5230d9832a5ced6836161eeb
      from_commit: b110a6e5e2915e98869672d6d12c794eec6c1fec
      message: "chore: remove slack alerts from honeycomb recipients (#7786)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/triggers_phone_prefix.tf":
      file_hash: 4220e97a3f64c1e428bd9946e238a8fbafd764fc
      from_commit: b110a6e5e2915e98869672d6d12c794eec6c1fec
      message: "chore: remove slack alerts from honeycomb recipients (#7786)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/triggers_pod_BackOff.tf":
      file_hash: 1006050c6dd7952447128dfb9d662b3cd89035c8
      from_commit: 74b2d505b450a0556f3b8110b93b41992649a7a0
      message: "fix: trigger pod Backoff to only alert on_change (#7995)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/triggers_sms.tf":
      file_hash: 596f1bb0bbff41e4b5b3e52b72b0687a648e15c9
      from_commit: b110a6e5e2915e98869672d6d12c794eec6c1fec
      message: "chore: remove slack alerts from honeycomb recipients (#7786)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/triggers_stablesats.tf":
      file_hash: 5e81b94c09cea7e2963f37599e1c0d23e457b5ec
      from_commit: b110a6e5e2915e98869672d6d12c794eec6c1fec
      message: "chore: remove slack alerts from honeycomb recipients (#7786)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/triggers_trigger.tf":
      file_hash: cf3653aebb67a5f08b37379ba9df134fcf170c46
      from_commit: b110a6e5e2915e98869672d6d12c794eec6c1fec
      message: "chore: remove slack alerts from honeycomb recipients (#7786)"
    "{gcp-galoy-staging}/modules/services/monitoring/honeycomb/variables.tf":
      file_hash: 395bbb6390f1df7ddcc61a7e5df32af48395eabf
      from_commit: 38f48b8e6dd9ecbab935a36fa9d9b28136ad3700
      message: "chore: add honeycomb trigger for pod BackOff (#7970)"
    "{gcp-galoy-staging}/modules/services/monitoring/kafka-connect/kafka-connect-values.yml.tmpl":
      file_hash: e1157545da67e1e4e050b1bc4bf30f4e87ed7ef8
      from_commit: 3600452cc093fc461c2cf43366af3894787e9bd4
      message: "feat: kafka-source-postgres, refactor connectors (#4011)"
    "{gcp-galoy-staging}/modules/services/monitoring/kafka-connect/kafka-sink-bigquery.tf":
      file_hash: 75c17452865a604102d662cbea2f8599a09be224
      from_commit: bd0638bbf07952a7660705ba9937bfc668a7db07
      message: "fix: kafka sink connector won't stop on errors (#4819)"
    "{gcp-galoy-staging}/modules/services/monitoring/kafka-connect/kafka-source-mongodb-galoy.tf":
      file_hash: 36723e19f4c05e7ef1ed7e01d715611967448784
      from_commit: 272d7515494e5596fbef904775a876c44f379ad2
      message: "chore: add wallets collection to kafka topics (#4650)"
    "{gcp-galoy-staging}/modules/services/monitoring/kafka-connect/kafka-source-pg-stablesats.tf":
      file_hash: 802deb5fb3cf487ed2ad7560e95ac77897c0af38
      from_commit: 1551144fa32d120f82207d804e9663cac447f231
      message: "feat: add kafka pg stablesats streaming (#4295)"
    "{gcp-galoy-staging}/modules/services/monitoring/kafka-connect/main.tf":
      file_hash: 3fdafd89edb2b899c7bfd3d4b1ff69c3d40064d8
      from_commit: 6029fc5ab247bdf3d072c6da470f9177d6fac7d3
      message: "fix: kafka-connect chart path (#4268)"
    "{gcp-galoy-staging}/modules/services/monitoring/log-sinks.tf":
      file_hash: 39294da8ceab38e875ea4f7dccdd3ea0b209f567
      from_commit: f311ffd94ba328038feed9332cee8c19460aa38b
      message: "chore: disable nginx ingress log ingestion (#6826)"
    "{gcp-galoy-staging}/modules/services/monitoring/main.tf":
      file_hash: 0ca8977a475a52859cd37369e8723048264edc91
      from_commit: d9faea24c95d69831d7d378de30e7d6b6e6b8be7
      message: "fix: update pagerduty provider in modules and use terraform registry"
    "{gcp-galoy-staging}/modules/services/monitoring/monitoring-values.yml.tmpl":
      file_hash: ee0512e135ebeca86d92f8aa637cda29012f62e6
      from_commit: 7e7b67e2e1683f39945efad797d2d68b6c6ec310
      message: "fix: use nginx className for grafana"
    "{gcp-galoy-staging}/modules/services/monitoring/pagerduty.tf":
      file_hash: aef6025e7e04ca6a8687c6fc6a20658551649a77
      from_commit: 6fc71be88444d4c56b0fd86000f2e577c80de3bf
      message: "chore: set staging pagerduty alert urgency to low (#7830)"
    "{gcp-galoy-staging}/modules/services/monitoring/variables.tf":
      file_hash: a7ab8bcc3db559e317026466a0f3577eff418902
      from_commit: 6fc71be88444d4c56b0fd86000f2e577c80de3bf
      message: "chore: set staging pagerduty alert urgency to low (#7830)"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/ci/tasks/get-smoketest-settings.sh":
      file_hash: 4d28e80ec720fd413e2c45b6212542e9250f4089
      from_commit: 83494dc53c0a99891337a884c9268684df2ff068
      message: "Bump monitoring-chart to '5ad15bdeb54562b900a91ff05268285ba281722f'"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/kafka-connect/chart/Chart.yaml":
      file_hash: a0e8b9fde9e289a653e31a385e161d59c94f1fc5
      from_commit: 3600452cc093fc461c2cf43366af3894787e9bd4
      message: "feat: kafka-source-postgres, refactor connectors (#4011)"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/kafka-connect/chart/templates/helpers.tpl":
      file_hash: 6fc7355504b7f5dd4a5783312f39165ac21adeee
      from_commit: 3600452cc093fc461c2cf43366af3894787e9bd4
      message: "feat: kafka-source-postgres, refactor connectors (#4011)"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/kafka-connect/chart/templates/kafka-connect.yaml":
      file_hash: a4b77e5736d1d1bf45012a5752e26dee5e0b3a37
      from_commit: 3600452cc093fc461c2cf43366af3894787e9bd4
      message: "feat: kafka-source-postgres, refactor connectors (#4011)"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/kafka-connect/chart/templates/network-policy-allow-from-smoketest-namespace.yaml":
      file_hash: 709c4f435914c2472101ab3097cff87370e440f0
      from_commit: 3600452cc093fc461c2cf43366af3894787e9bd4
      message: "feat: kafka-source-postgres, refactor connectors (#4011)"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/kafka-connect/chart/values.yaml":
      file_hash: 4995b45c47085ebc0a93d2ecd61f8374ebbd3a87
      from_commit: f75c7a6ab53dbb5e69295cbb246d462769c8441b
      message: "chore: bump kafka-connect-chart to 'da666b837cceb71e299ff19bd309b5d22af020d3'"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/kafka-connect/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 3600452cc093fc461c2cf43366af3894787e9bd4
      message: "feat: kafka-source-postgres, refactor connectors (#4011)"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/kafka-connect/ci/tasks/kafka-connect-smoketest.sh":
      file_hash: 56097143fd7ca6fd3564d608210af99235ce8d9e
      from_commit: 3600452cc093fc461c2cf43366af3894787e9bd4
      message: "feat: kafka-source-postgres, refactor connectors (#4011)"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/kafka-connect/git-ref/ref":
      file_hash: 8370848a2b99f2f0ecbf2f32a8cf18daeef63cde
      from_commit: f75c7a6ab53dbb5e69295cbb246d462769c8441b
      message: "chore: bump kafka-connect-chart to 'da666b837cceb71e299ff19bd309b5d22af020d3'"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/lndmon/dashboards/chain.json":
      file_hash: a14a64626c07ada5ca605d579cf82da054a50e4f
      from_commit: 07b959793cf28b0c8ebc14c933b8332d28446a7b
      message: Add lnd dashboards
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/lndmon/dashboards/channels.json":
      file_hash: bf35f14ef0dfd1dfe304a7b4279a6870a7966d59
      from_commit: 07b959793cf28b0c8ebc14c933b8332d28446a7b
      message: Add lnd dashboards
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/lndmon/dashboards/dashboard.yml":
      file_hash: 14716ee197ed9782616d891f4ca0433dcc843d6d
      from_commit: 07b959793cf28b0c8ebc14c933b8332d28446a7b
      message: Add lnd dashboards
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/lndmon/dashboards/network.json":
      file_hash: 089d7c24cfff30c1ff619c64f338474c2ab5ca53
      from_commit: 07b959793cf28b0c8ebc14c933b8332d28446a7b
      message: Add lnd dashboards
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/lndmon/dashboards/peers.json":
      file_hash: e848c9f3f6799bef94319b376c997ae23070e34d
      from_commit: 07b959793cf28b0c8ebc14c933b8332d28446a7b
      message: Add lnd dashboards
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/lndmon/dashboards/perf.json":
      file_hash: 97b4db6362051847bc621067dcc74028306b62b0
      from_commit: 07b959793cf28b0c8ebc14c933b8332d28446a7b
      message: Add lnd dashboards
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/lndmon/dashboards/routing.json":
      file_hash: a7ce7ed6f90f2f94dcdf6ac7af50da10e8865087
      from_commit: 07b959793cf28b0c8ebc14c933b8332d28446a7b
      message: Add lnd dashboards
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/lndmon/git-ref/ref":
      file_hash: 57870e986d705e960b63493aeec1f2f5369f208c
      from_commit: 07b959793cf28b0c8ebc14c933b8332d28446a7b
      message: Add lnd dashboards
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/monitoring/chart/Chart.lock":
      file_hash: d259f48eeb6bb35e3c5fdafa884b246bc7b6dd02
      from_commit: 6775add0c6f70c997540f4744859db67afd84521
      message: "chore: bump monitoring-chart to '87ae0b0d962805a0597d44459d8e2c0d7254631b'"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/monitoring/chart/Chart.yaml":
      file_hash: c5c66e033f74d0a2b636eecf2a7ccd9a28cb3e6d
      from_commit: 6775add0c6f70c997540f4744859db67afd84521
      message: "chore: bump monitoring-chart to '87ae0b0d962805a0597d44459d8e2c0d7254631b'"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/monitoring/chart/values.yaml":
      file_hash: c897747c78900ea5b8eb1b7bf68491fd36e4b4cb
      from_commit: 496409e2cabeaabd5864cc93c32e007c5f7ae4fb
      message: "chore: bump monitoring-chart to 'd47c2f4923e1b4e413f3736f99eece4aa4f3b19c'"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/monitoring/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 5b0965d84ffb867431ce5ef8c448b4eb50482efd
      message: "Bump monitoring-chart to '2dbe942943c6c56af57a3b8b2fa13cf511c0247f'"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/monitoring/ci/tasks/monitoring-smoketest.sh":
      file_hash: 4ea7ab89469a276e6f426709eb3e8a6651ff00fd
      from_commit: dd68c5573eff892c88e9ec17f5bc7a1701761f51
      message: "chore: bump monitoring-chart to '9237890fe99d38519d349d3cb0feff3da893883e'"
    "{gcp-galoy-staging}/modules/services/monitoring/vendor/monitoring/git-ref/ref":
      file_hash: c049884a224f183d9db7e04a5f75d2297b57618f
      from_commit: 6775add0c6f70c997540f4744859db67afd84521
      message: "chore: bump monitoring-chart to '87ae0b0d962805a0597d44459d8e2c0d7254631b'"
    "{latest}/gcp/galoy-bbw/monitoring/kafka-connect-scaling.yml":
      file_hash: 15c4ab81dcf94f3c60817353d5c8711706be7e86
      from_commit: ac52da0a83040a51ad9e1558cae181387156204f
      message: "chore: increase kafka-connect cpu limit on bbw (#4314)"
    "{latest}/gcp/galoy-bbw/monitoring/main.tf":
      file_hash: f5abe612daf7e7e4d330014db2bf0293595bbe47
      from_commit: 2fe12f560236b2829389ef95c04223226449c1d2
      message: "chore: set grafana_allowed_oauth_domain blinkbtc.com and blink.sv for grafana (#7699)"
    "{latest}/gcp/galoy-bbw/monitoring/monitoring-scaling.yml":
      file_hash: ac1d92386c8ebb2ce42e04877cbf244be6a8466c
      from_commit: cd93108b751881d9bb348ab2ce9f9d70dc51905c
      message: "chore: bump bbw monitoring scaling"
    "{latest}/gcp/galoy-bbw/shared/main.tf":
      file_hash: cfaac3c223a0bcb483083290adab8160cfacf569
      from_commit: 697d1c38e1d448ae0958182a5948bbdbd7a40c2c
      message: "chore: remove US from blocklist for now (#7072)"
propagated_from: gcp-galoy-staging

