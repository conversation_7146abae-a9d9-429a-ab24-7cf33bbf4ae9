---
version: 138
current:
  head_commit: acd9686158ba0971aa12060838feef22216e536a
  propagated_head: d01cc27656a1a91a52958aca5ed0ed3a26829b82
  files:
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/create-dms.sh":
      file_hash: 71e1a07123d132031eee636ac4ce98d2300dd337
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/postgres-perms-update.sh":
      file_hash: 3d90741c6affcb8a65219327097726ec54c5df68
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-db-swap.sh":
      file_hash: 91dd520580913c7a6a945d33d4595a8655073838
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-state-rm.sh":
      file_hash: c80c2912e7b807fc3e516fe93b218a4d7afd2b87
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/database/main.tf":
      file_hash: 9a0426f4e89c88fae5681084c6102a74181fcfba
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/main.tf":
      file_hash: af19399be2b3c580d4cb438a1cb44363e83f6bdb
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/migration/main.tf":
      file_hash: e1b48f6e0a08249e8d2b8794dfc63b23d051448a
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/outputs.tf":
      file_hash: 45d03b0075d0e46b7ff53dcf19b70ce96c304617
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/read-replica.tf":
      file_hash: 541c04251858b2cbde90ae3e74a48147d91093f3
      from_commit: 42e395bea256af0c89e50583aa8bf46077413305
      message: "chore(deps): bump galoy-infra modules to 'e76b37a'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/variables.tf":
      file_hash: 5d0fe5d208387d1e2776b1d9af5acb1bb598998d
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/services/stablesats/main.tf":
      file_hash: df4327258d21dd13a8dddb0a535eccef13e74835
      from_commit: 7dfc01f970421c8038d44570f7260ce3227f7b3d
      message: "chore: revert bitcoin / stablesats upgrade"
    "{gcp-galoy-staging}/modules/services/stablesats/stablesats-sensitive-values.yml.tmpl":
      file_hash: e3288d8935077ec57c59908783dabc1560ef3446
      from_commit: 59cd56128a86450c0e947940340449d06245578d
      message: "fix: stablesats breaking upgrade (#3103)"
    "{gcp-galoy-staging}/modules/services/stablesats/stablesats-values.yml.tmpl":
      file_hash: 5a43ffd398c5f3a2bb255042d183a2a60181e853
      from_commit: 0d7f6f2c26693b696103742a1f17153910f9e93a
      message: "fix: stablesats otel host"
    "{gcp-galoy-staging}/modules/services/stablesats/variables.tf":
      file_hash: 27989044760dea863084a0fd4b9a655ec4d9428a
      from_commit: e82f210dfee8c429fd9a31aa9e04d0be09741776
      message: "chore: fixing frequency (#4845)"
    "{gcp-galoy-staging}/modules/services/stablesats/vendor/stablesats/chart/Chart.lock":
      file_hash: 3a015a473a95dd500c168475f9162365170c9d16
      from_commit: 09a45b9fbbb2298e992e6460f58ae10c824c18ec
      message: "chore: bump stablesats-chart to 'b1008a387c00f16e695b11afd1cc21d47f9cbfa1'"
    "{gcp-galoy-staging}/modules/services/stablesats/vendor/stablesats/chart/Chart.yaml":
      file_hash: 9256b0f662ac98f5725869505478f734c951b84f
      from_commit: 09a45b9fbbb2298e992e6460f58ae10c824c18ec
      message: "chore: bump stablesats-chart to 'b1008a387c00f16e695b11afd1cc21d47f9cbfa1'"
    "{gcp-galoy-staging}/modules/services/stablesats/vendor/stablesats/chart/templates/_helpers.tpl":
      file_hash: 6adc9afc3536c7422dcede27c3258ee2ecb7ff47
      from_commit: 26fc6af25126024c39cef17f68250fc1d1c6953e
      message: "Bump stablesats-chart to '5026444eeb44f408317f74dcbcc1fa1b3211f5c4'"
    "{gcp-galoy-staging}/modules/services/stablesats/vendor/stablesats/chart/templates/stablesats-dealer-cm.yaml":
      file_hash: 1323cf498626d591f692b27f79f8552bad338baf
      from_commit: 2eabd752ee5f4ed7cbf1d88b6e9d9738f9244896
      message: "chore: bump stablesats-chart to 'ce6e15ec665fb87596483ad1d9fa4b78080ca619'"
    "{gcp-galoy-staging}/modules/services/stablesats/vendor/stablesats/chart/templates/stablesats-dealer-deployment.yaml":
      file_hash: 55eaf45b93735ea91f144b4e20750219f6181b27
      from_commit: 411961ef9682064ebc5b9c331b206e6bbbe9674d
      message: "chore: bump stablesats-chart to 'db268d40ae476363f75a7ef26cc2398a370d1b4c'"
    "{gcp-galoy-staging}/modules/services/stablesats/vendor/stablesats/chart/templates/stablesats-price-cm.yaml":
      file_hash: 6d3e5d0430d2f119d81a5d313fed495301cc476c
      from_commit: 2eabd752ee5f4ed7cbf1d88b6e9d9738f9244896
      message: "chore: bump stablesats-chart to 'ce6e15ec665fb87596483ad1d9fa4b78080ca619'"
    "{gcp-galoy-staging}/modules/services/stablesats/vendor/stablesats/chart/templates/stablesats-price-deployment.yaml":
      file_hash: 8e5fe53b671c6567fc2eb26138647a414ff1718d
      from_commit: 411961ef9682064ebc5b9c331b206e6bbbe9674d
      message: "chore: bump stablesats-chart to 'db268d40ae476363f75a7ef26cc2398a370d1b4c'"
    "{gcp-galoy-staging}/modules/services/stablesats/vendor/stablesats/chart/templates/stablesats-price-svc.yaml":
      file_hash: 54a77d6f0e1fe98f7a3958f2be46647dd3694cc8
      from_commit: 5e0b0c8ffdad9641231b7f088c4a00671a68f2e7
      message: "chore: bump stablesats-chart to '7dc4b3b37f1f026e3bada9f82513c1a56778fcb7'"
    "{gcp-galoy-staging}/modules/services/stablesats/vendor/stablesats/chart/templates/stablesats-secrets.yaml":
      file_hash: e9f8157c75710a4b4ef9372b1645afe7e9fdd9ca
      from_commit: e4dc37ecbe940c59e92dcb344342530eb8187ba7
      message: "chore: bump stablesats-chart to 'c5c54ece8a7aef24d688f719146ae380d7bc3831'"
    "{gcp-galoy-staging}/modules/services/stablesats/vendor/stablesats/chart/values.yaml":
      file_hash: ddb38a14852c041f7b5cb064f408fa208264d94b
      from_commit: 14cf4fecd324405997763563f4765a8ae33c7e3d
      message: "chore: bump stablesats-chart to 'fb81a575af39da8739eee33f96ad75bfd05f5f28'"
    "{gcp-galoy-staging}/modules/services/stablesats/vendor/stablesats/ci/tasks/get-smoketest-settings.sh":
      file_hash: f1692795dc894a350cb5aeb9fa86313ba60bc0fa
      from_commit: 26fc6af25126024c39cef17f68250fc1d1c6953e
      message: "Bump stablesats-chart to '5026444eeb44f408317f74dcbcc1fa1b3211f5c4'"
    "{gcp-galoy-staging}/modules/services/stablesats/vendor/stablesats/ci/tasks/stablesats-smoketest.sh":
      file_hash: ba1235ad22ec982fd034c9ac07628307902ae2e9
      from_commit: 76f2e1576a7e0c0822411d2f1483f35ad299e0d2
      message: "fix: ref value in stablesats vendir config"
    "{gcp-galoy-staging}/modules/services/stablesats/vendor/stablesats/git-ref/ref":
      file_hash: 55cbdc7fcd33514bc6677f74ddc623de13cab6ca
      from_commit: 09a45b9fbbb2298e992e6460f58ae10c824c18ec
      message: "chore: bump stablesats-chart to 'b1008a387c00f16e695b11afd1cc21d47f9cbfa1'"
    "{latest}/gcp/galoy-bbw/shared/main.tf":
      file_hash: cfaac3c223a0bcb483083290adab8160cfacf569
      from_commit: 697d1c38e1d448ae0958182a5948bbdbd7a40c2c
      message: "chore: remove US from blocklist for now (#7072)"
    "{latest}/gcp/galoy-bbw/stablesats/main.tf":
      file_hash: 9e12a38c95a938590dd3bf414713e030634e3d97
      from_commit: cc7036e42771807a6a537213e19adce0eb2b5e24
      message: "chore: dummy commit to trigger sts"
    "{latest}/gcp/galoy-bbw/stablesats/stablesats-scaling.yml":
      file_hash: f9346070dde632d6947202ba43c28c85b0ec2cee
      from_commit: dd98a8b5114b8e07f19092566cf579458ec68a13
      message: "chore: scale down stablesats to 1 dealer in prod"
propagated_from: gcp-galoy-staging

