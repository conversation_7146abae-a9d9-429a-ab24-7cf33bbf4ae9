---
version: 113
current:
  head_commit: 313f7707ab200f932d451c3f8dc33cf9befda3a2
  propagated_head: fd521b3a62157efb3e0966f53b4f09b22c3a9dad
  files:
    "{gcp-galoy-staging}/modules/infra/vendor/git-ref/ref":
      file_hash: 06d4a544667851a892965e4ce77f3ce8a382da32
      from_commit: fd521b3a62157efb3e0966f53b4f09b22c3a9dad
      message: "chore(deps): bump galoy-infra modules to '93f9dd1'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/bootstrap/gcp/external-users.tf":
      file_hash: 31c9d1075134e317802aade5ca5b81e93e3e6e35
      from_commit: c59f6333e1df122d8b6243a20a309126e9afe273
      message: "Bump galoy-infra modules to '4551767'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/bootstrap/gcp/inception-account.tf":
      file_hash: 6a14037d361b648e9c1b8cb19c3037efff05fc30
      from_commit: 063232d035699519fe3205f41b4042748f93aa51
      message: "chore(deps): bump galoy-infra modules to '07f7cc5'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/bootstrap/gcp/outputs.tf":
      file_hash: 4c41cd681672ac059228d7f68219d10b707465c2
      from_commit: 91a2e03930d3ac6ad50ee33cfe8d4c27cb9447ce
      message: Remove indirection for infra modules
    "{gcp-galoy-staging}/modules/infra/vendor/tf/bootstrap/gcp/services.tf":
      file_hash: 102402c87c4dc28b48279556300558abef916fbe
      from_commit: 6bbb9651c453f3b2404482c4f9ddf8a2baa71bc5
      message: "chore(deps): bump galoy-infra modules to 'a73151e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/bootstrap/gcp/tf-state-bucket.tf":
      file_hash: a2d67c5771b5308d1b6a5d1002175f11f0d69959
      from_commit: 6bbb9651c453f3b2404482c4f9ddf8a2baa71bc5
      message: "chore(deps): bump galoy-infra modules to 'a73151e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/bootstrap/gcp/variables.tf":
      file_hash: 5010e2e7f46b92567c42e2da143777a077ccf09a
      from_commit: 3bb8b77014ac8c66d2004a60e0b87385bd06a5f8
      message: "chore(deps): bump galoy-infra modules to '228d7d0'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/backups-bucket.tf":
      file_hash: d21ecc6dc49ca6da3bab60fa4d4878eb95b2bad0
      from_commit: 79b90ee92bd5484c745c830d8a26edb6228e2366
      message: "chore(deps): bump galoy-infra modules to 'eb527a5'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/bastion-access-role.tf":
      file_hash: d829758767fcc239942d98af6a757b6adaac4210
      from_commit: 060b7a924ddf77d45af7bb416bb4389882e3c6a4
      message: "Bump galoy-infra modules to '566fe9a'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/bastion-service-account.tf":
      file_hash: 7b67370ab7d5720464e26fbc7bf362c4168302e5
      from_commit: eb2a4bcd21515868c3c732b2d1d8a68dc9a1f7cc
      message: "Bump galoy-infra modules to '827af04'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/bastion-startup.tmpl":
      file_hash: 48e93710b7a48e0b02dd37f0e728120a9d0c8a76
      from_commit: 801f6f2f5aa2d3ccad9cfd1ae8443c9541df5f65
      message: "chore(deps): bump galoy-infra modules to 'a70ae04'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/bastion.tf":
      file_hash: fc2a362c825831fafd54ff21ad08c7ac3093c84c
      from_commit: a3831fd490914b45e2c668c6bba68523c96801ca
      message: "chore(deps): bump galoy-infra modules to 'e7ba15b'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/grafana-account.tf":
      file_hash: 091ebda917a27dfddc7ea80d25f1f43ba16b7a87
      from_commit: 1c7b5394a50c3ceac45b48aa76e76ac7dc0759d7
      message: "Bump galoy-infra modules to '37dffad'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/inception-roles.tf":
      file_hash: b10f79a098048f53a64d537441325c057bd4921b
      from_commit: 8a84f1ab27a801dfbb8cac90c1ed12fa375c1d42
      message: "chore(deps): bump galoy-infra modules to 'f91c42a'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/logs-viewer.tf":
      file_hash: 470249dade04dbc99569a6837825268f03e14b94
      from_commit: 0ad1c96d8f6986d4f39222dc10c99d634927be63
      message: "chore(deps): bump galoy-infra modules to 'a544dc1'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/nat.tf":
      file_hash: 032b1f74261330491c198f0cd0345f09ed8089e4
      from_commit: 42e55eabb7dc4ea5f4e22cece2f32a537f94c0bf
      message: "Bump galoy-infra modules to 'e76f722'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/network.tf":
      file_hash: abdd9d20e4294d57ebf249a570bd4d49ae1b7e40
      from_commit: 77863d78d401ec4b8eacd7949c7b13c12a98bb88
      message: "chore(deps): bump galoy-infra modules to '0bb9f23'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/node-account.tf":
      file_hash: a291f0057b288caf7d94ff3d4b3ee6032e29c6b2
      from_commit: c05e1361faa04ba7588d61f22cae0a24fb616af3
      message: "Bump galoy-infra modules to '41abc92'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/outputs.tf":
      file_hash: 5fcc3014b15975fe299d4bed2c3efc842b1874c4
      from_commit: 81824e1e5e756fbbedb11ccf635b654146ebe70a
      message: "Bump galoy-infra modules to '66120b5'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/platform-roles.tf":
      file_hash: f3f36d2cb3d05551223856461f729412909cc797
      from_commit: 842c765bad505a06631c92b1853f958035f4625a
      message: "chore(deps): bump galoy-infra modules to 'a5ec0c9'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/platform-users.tf":
      file_hash: f67d025ae0269ecbd9a3d1309c9141cdb64ee90a
      from_commit: c05e1361faa04ba7588d61f22cae0a24fb616af3
      message: "Bump galoy-infra modules to '41abc92'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/tf-state-bucket.tf":
      file_hash: d82dfd87359c2cacee778395fbbcdb3fe0214e6f
      from_commit: 6bbb9651c453f3b2404482c4f9ddf8a2baa71bc5
      message: "chore(deps): bump galoy-infra modules to 'a73151e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/inception/gcp/variables.tf":
      file_hash: 1a2a2cedcead352b49adcad263179b9986b9b444
      from_commit: ce0d78a4472bc723fb896b02725424df12942b36
      message: "chore(deps): bump galoy-infra modules to '2a35688'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/platform/gcp/firewall.tf":
      file_hash: 2e34ffc0d5fff9757c45095add902a765b705005
      from_commit: 91a2e03930d3ac6ad50ee33cfe8d4c27cb9447ce
      message: Remove indirection for infra modules
    "{gcp-galoy-staging}/modules/infra/vendor/tf/platform/gcp/kube.tf":
      file_hash: 114a9f826aba12f62969ad4fd7b76919636c7ce3
      from_commit: 1b1877b7b28f2d750e0038600057563991568da3
      message: "chore(deps): bump galoy-infra modules to '1634885'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/platform/gcp/lnd-ip.tf":
      file_hash: 9de7446bea2fe301b6a18cc0eee69a4e3cdbdcaa
      from_commit: 18f0bfdb2feeccad149a945c3973e4b6f375d706
      message: "chore(deps): bump galoy-infra modules to '5d4a01e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/platform/gcp/network.tf":
      file_hash: e9ac1ee36be9b3bbfbd18a71c2de90b498cc490a
      from_commit: 91a2e03930d3ac6ad50ee33cfe8d4c27cb9447ce
      message: Remove indirection for infra modules
    "{gcp-galoy-staging}/modules/infra/vendor/tf/platform/gcp/outputs.tf":
      file_hash: 716b5a5798061cd5a632275a057a2d5aa330ce13
      from_commit: d7da83ce58dd71e7fe049e86a22aaacff3ba1446
      message: "chore(deps): bump galoy-infra modules to '90a56ac'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/platform/gcp/variables.tf":
      file_hash: 5059d69787f5e6d3318c46dcf013d5161f7f0b33
      from_commit: fd521b3a62157efb3e0966f53b4f09b22c3a9dad
      message: "chore(deps): bump galoy-infra modules to '93f9dd1'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/create-dms.sh":
      file_hash: 71e1a07123d132031eee636ac4ce98d2300dd337
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/postgres-perms-update.sh":
      file_hash: 3d90741c6affcb8a65219327097726ec54c5df68
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-db-swap.sh":
      file_hash: 91dd520580913c7a6a945d33d4595a8655073838
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/bin/terraform-state-rm.sh":
      file_hash: c80c2912e7b807fc3e516fe93b218a4d7afd2b87
      from_commit: ca9d11defe69ef2c9d68d1d57b5fbfe045dedf8d
      message: "chore(deps): bump galoy-infra modules to '42c62ac'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/database/main.tf":
      file_hash: 9a0426f4e89c88fae5681084c6102a74181fcfba
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/main.tf":
      file_hash: af19399be2b3c580d4cb438a1cb44363e83f6bdb
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/migration/main.tf":
      file_hash: e1b48f6e0a08249e8d2b8794dfc63b23d051448a
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/outputs.tf":
      file_hash: 45d03b0075d0e46b7ff53dcf19b70ce96c304617
      from_commit: 9a063d86d94de2e0969e2da4ecd2ad18b49ff527
      message: "chore(deps): bump galoy-infra modules to 'c810aec'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/read-replica.tf":
      file_hash: 541c04251858b2cbde90ae3e74a48147d91093f3
      from_commit: 42e395bea256af0c89e50583aa8bf46077413305
      message: "chore(deps): bump galoy-infra modules to 'e76b37a'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/postgresql/gcp/variables.tf":
      file_hash: 5d0fe5d208387d1e2776b1d9af5acb1bb598998d
      from_commit: d01cc27656a1a91a52958aca5ed0ed3a26829b82
      message: "chore(deps): bump galoy-infra modules to '9f54c8e'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/smoketest/gcp/concourse-k8s-access.tf":
      file_hash: 00964d70a44380d171e7cbf3b98904f90594076f
      from_commit: 4a0e1231e32bc0a24916c85fe68d413299416799
      message: "chore(deps): bump galoy-infra modules to '534ccf9'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/smoketest/gcp/kubeconfig.tmpl.yml":
      file_hash: 04af819af71afb895542cc3960e92c3935876676
      from_commit: 66bd93dfff463bcadf32795bbc79162d53a4b4d0
      message: "chore: bump stablesats-chart to '4cb59e855b4850c94574c7cc71138a856e65a4b8'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/smoketest/gcp/main.tf":
      file_hash: 10402d8dfdfb08aa09e4763aaf36a6987c244398
      from_commit: 476e0f7c792e124ddaeb3ed3ebf3eddd800b30ba
      message: "Bump galoy-infra modules to '763dbae'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/smoketest/gcp/output.tf":
      file_hash: 8ad6b733066a4bb5d2d2d9213378b043991ffb03
      from_commit: 66bd93dfff463bcadf32795bbc79162d53a4b4d0
      message: "chore: bump stablesats-chart to '4cb59e855b4850c94574c7cc71138a856e65a4b8'"
    "{gcp-galoy-staging}/modules/infra/vendor/tf/smoketest/gcp/variables.tf":
      file_hash: 4fbd60c1742d6a34e01a9a09ebdb68f69c4ab18f
      from_commit: 5eb2d1e274baad6becdd93874a60d6a63ada6f08
      message: "chore(deps): bump galoy-infra modules to 'd989e3d'"
    "{latest}/gcp/galoy-bbw/inception/main.tf":
      file_hash: e6f1041f988a627c8eca6a44f04864e4afc19e84
      from_commit: b33d5378bc4f391c8468f0ebe41783289c7c8b9e
      message: "chore: change prod gcp backups bucket name (#7676)"
    "{latest}/gcp/galoy-bbw/platform/main.tf":
      file_hash: 59b70048c1128d7485fb2e3d1e4c28f1fbb8ac2b
      from_commit: 2a84fd5e54c75c13c449caffe0c77440e8ed0ac3
      message: "fix(ci): Set lnd to true (#7730)"
    "{latest}/gcp/galoy-bbw/shared/main.tf":
      file_hash: cfaac3c223a0bcb483083290adab8160cfacf569
      from_commit: 697d1c38e1d448ae0958182a5948bbdbd7a40c2c
      message: "chore: remove US from blocklist for now (#7072)"
    "{latest}/gcp/galoy-bbw/smoketest/main.tf":
      file_hash: d9e64ec26ec0cf2f192d27ab4df9ad0e03bd95c5
      from_commit: 8f32ee7468d88041f5e5394396a355b554aeaff5
      message: "chore: enable k8s secret reader on bbw (#6222)"
propagated_from: gcp-galoy-staging

