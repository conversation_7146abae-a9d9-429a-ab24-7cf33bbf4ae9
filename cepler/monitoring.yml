deployment: monitoring
environments:
  gcp-galoy-staging:
    latest:
    - modules/services/monitoring/**

    - gcp/galoy-staging/shared/*
    - gcp/galoy-staging/monitoring/*

  gcp-galoy-bbw:
    passed: gcp-galoy-staging
    ignore_queue: true
    propagated:
    - modules/services/monitoring/**
    latest:
    - gcp/galoy-bbw/shared/*
    - gcp/galoy-bbw/monitoring/*

  monitoring-release:
    passed: gcp-galoy-bbw
    propagated:
      - modules/services/monitoring/vendor/monitoring/git-ref/ref

  kafka-connect-release:
    passed: gcp-galoy-bbw
    propagated:
      - modules/services/monitoring/vendor/kafka-connect/git-ref/ref
