deployment: galoy
environments:
  gcp-galoy-staging:
    latest:
    - modules/galoy/**
    - modules/infra/vendor/tf/postgresql/**

    - gcp/galoy-staging/shared/*
    - gcp/galoy-staging/galoy/*

  gcp-galoy-bbw:
    passed: gcp-galoy-staging
    ignore_queue: true
    propagated:
    - modules/galoy/**
    - modules/infra/vendor/tf/postgresql/**
    latest:
    - gcp/galoy-bbw/shared/*
    - gcp/galoy-bbw/galoy/*

  galoy-release:
    passed: gcp-galoy-bbw
    propagated:
      - modules/galoy/vendor/galoy/git-ref/ref
