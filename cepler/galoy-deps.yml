deployment: galoy-deps
environments:
  gcp-galoy-staging:
    latest:
    - modules/services/galoy-deps/**
    - modules/infra/vendor/tf/postgresql/**

    - gcp/galoy-staging/shared/*
    - gcp/galoy-staging/galoy-deps/*

  gcp-galoy-bbw:
    passed: gcp-galoy-staging
    ignore_queue: true
    propagated:
    - modules/services/galoy-deps/**
    - modules/infra/vendor/tf/postgresql/**
    latest:
    - gcp/galoy-bbw/shared/*
    - gcp/galoy-bbw/galoy-deps/*

  galoy-deps-release:
    passed: gcp-galoy-bbw
    propagated:
      - modules/services/galoy-deps/vendor/galoy-deps/git-ref/ref
