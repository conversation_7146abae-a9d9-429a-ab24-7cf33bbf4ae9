deployment: bitcoin
environments:
  gcp-galoy-staging:
    latest:
    - modules/services/bitcoin/**
    - modules/infra/vendor/tf/postgresql/**

    - gcp/galoy-staging/shared/*
    - gcp/galoy-staging/bitcoin/*

  gcp-galoy-bbw:
    passed: gcp-galoy-staging
    ignore_queue: true
    propagated:
    - modules/services/bitcoin/**
    - modules/infra/vendor/tf/postgresql/**
    latest:
    - gcp/galoy-bbw/shared/*
    - gcp/galoy-bbw/bitcoin/*

  bitcoind-release:
    passed: gcp-galoy-bbw
    propagated:
      - modules/services/bitcoin/vendor/bitcoind/git-ref/ref

  lnd-release:
    passed: gcp-galoy-bbw
    propagated:
      - modules/services/bitcoin/vendor/lnd1/git-ref/ref

  specter-release:
    passed: gcp-galoy-bbw
    propagated:
      - modules/services/bitcoin/vendor/specter/git-ref/ref

  bria-release:
    passed: gcp-galoy-bbw
    propagated:
      - modules/services/bitcoin/vendor/bria/git-ref/ref

  fulcrum-release:
    passed: gcp-galoy-bbw
    propagated:
      - modules/services/bitcoin/vendor/bria/git-ref/ref
