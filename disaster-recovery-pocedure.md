<!-- omit in toc -->
# Disaster Recovery Procedure
This document outlines the steps to recover the project in case of a catastrophic failure or data loss. The most likely scenario is to restore the backed up data to an existing environment.

- [Prerequisites](#prerequisites)
- [Ensure the GCP Environment is Working Correctly](#ensure-the-gcp-environment-is-working-correctly)
- [Restore the Stateful Components](#restore-the-stateful-components)
  - [1. Lightning Network Daemon (LND) Recovery](#1-lightning-network-daemon-lnd-recovery)
    - [1.1 Recover from seed](#11-recover-from-seed)
    - [1.2 Forced In-Place Rescan](#12-forced-in-place-rescan)
    - [1.3 Restore the funds in the channels with the SCB](#13-restore-the-funds-in-the-channels-with-the-scb)
  - [2. PostgreSQL Recovery](#2-postgresql-recovery)
    - [2.1 Point in Time Recovery (PITR)](#21-point-in-time-recovery-pitr)
      - [2.1.1 Preparation](#211-preparation)
      - [2.1.2 Clone the database from a previous state](#212-clone-the-database-from-a-previous-state)
      - [2.1.3 Switch to the cloned database instance](#213-switch-to-the-cloned-database-instance)
        - [******* Sync the deployments repo to the bastion](#2131-sync-the-deployments-repo-to-the-bastion)
        - [******* Construct the secrets manually as Concourse would do](#2132-construct-the-secrets-manually-as-concourse-would-do)
        - [******* See the proposed changes and apply gradually](#2133-see-the-proposed-changes-and-apply-gradually)
        - [2.1.3.4 Import the different database name suffix](#2134-import-the-different-database-name-suffix)
        - [******* Import the recovered database](#2135-import-the-recovered-database)
        - [******* Apply the changed resources one by one](#2136-apply-the-changed-resources-one-by-one)
      - [2.1.4 When verified that the recovery was successful delete the corrupted database](#214-when-verified-that-the-recovery-was-successful-delete-the-corrupted-database)
      - [2.2 Restore an instance from a backup](#22-restore-an-instance-from-a-backup)
    - [3. MongoDB Recovery](#3-mongodb-recovery)
  - [Verification and Testing](#verification-and-testing)

## Prerequisites
- Access to GCP console
- Access to AWS console and CLI (optional)
- Access to the private GitHub repositories (blink-deployments, galoy-org-infra)
- Access to Vault cluster
- Intact Galoy-Org Cluster
- Access to the LND1 and LND2 seeds, passphrases or root keys and recent Static Channel Backups

## Ensure the GCP Environment is Working Correctly
* In case there is no environment  deployed follow the [Environment Deployment Procedure](deployment.md) to provision a new one.
* Inject the existing secrets with Concourse from the Vault through the galoy-org cluster.

## Restore the Stateful Components
### 1. Lightning Network Daemon (LND) Recovery
* see the [LND Recovery Instructions](https://github.com/lightningnetwork/lnd/blob/master/docs/recovery.md) for details.

* to run lncli commands exec into the container eg.:
  ```
  env=galoy-staging
  k -n ${env}-bitcoin exec lnd1-0 -c lnd -- lncli -n signet getinfo
  ```

#### 1.1 Recover from seed
* Recovering the 24 word AEZEED format seed will restore the node ID and onchain funds after a rescan.
* In the lnd container run:
  ```
  lncli create
  ```
* use the wallet password from the Secrets:
  * for lnd1:
  ```
  env=galoy-staging
  k -n ${env}-bitcoin get secret lnd1-pass -o jsonpath='{.data.password}' | base64 -d
  ```
  * for lnd2:
  ```
  env=galoy-staging
  k -n ${env}-bitcoin get secret lnd2-pass -o jsonpath='{.data.password}' | base64 -d
  ```
* use the backed up 24 word mnemonic, the cypher passphrase or a root key
* note that in case of no other backup a wallet.db file with it's encryption password can be used.
* might need a larger number for `address look-ahead`, if lnd used more than the default 2500 addresses to look for.
* to track the recovery progress, one can use the command `lncli getrecoveryinfo`.

#### 1.2 Forced In-Place Rescan
* The initial rescan will only run on a clean node. If lnd restarts before the rescan finished it needs to be restarted manually.
* Edit the statefulset of the lnd container to run with the extra flag:
  ```
  lnd --reset-wallet-transactions
  ```
* Command to edit the lnd statefulset:
  ```
  env=galoy-staging
  k -n ${env}-bitcoin edit statefulset lnd1
  ```
  add after the line:
  ```
        - image: lightninglabs/lnd:v0.xx.x-beta
  ```
  the command:
  ```
          command:
            - /bin/sh
            - -c
            - lnd --reset-wallet-transactions
  ```

* The `--reset-wallet-transactions` flag will reset the best synced height of the wallet back to its birthday on every restart.
* Need to ensure that the recovery is complete before removing the flag so the next restart will not reset.

#### 1.3 Restore the funds in the channels with the SCB
* use `gsutil cp` to copy the `channel.backup` file to the bastion.
* The funds in the channels can be restored from the Static Channel Backup (`channel.backup` file) with the command:
  ```
  env=galoy-staging
  k -n ${env}-bitcoin exec lnd1-0 -c lnd -- lncli restorechanbackup --multi_file=<path to the channel.backup file on the bastion>
  ```

* This command will trigger the peers to initiate a force close of the channels. The funds become available once the force close transactions are confirmed.
* There is no way to preserve the open channels when restoring with an SCB.
* The offline peers cannot force close so there is a possibility for funds to be stuck.<br>
* The `restorechanbackup` can be run any time without limitations.

* Run the galoy cron job to ensure proper synchronization. Check in the `lnbalancesync` graph of the Grafana Dashboard.

### 2. PostgreSQL Recovery
#### 2.1 Point in Time Recovery (PITR)
PITR allows you to recover data from a specific point in time, down to a fraction of a second, via write-ahead log archiving.

If the logs are stored in Cloud Storage, then Cloud SQL uploads logs every five minutes or less. As a result, if a Cloud SQL instance is available, then the instance can be recovered to the latest time.
If the instance isn't available, then the recovery point objective is typically five minutes or less.

To perform PITR follow the docs at: [cloud.google.com/sql/docs/postgres/backup-recovery/pitr](https://cloud.google.com/sql/docs/postgres/backup-recovery/pitr)

* Get the latest recovery time
  * Use the Instance ID from https://console.cloud.google.com/sql/instances
  * Run on the bastion:
    ```
    gcloud sql instances get-latest-recovery-time INSTANCE_NAME
    ```

In following examples show how to work on the galoy-staging cluster restoring the voucher database.

##### 2.1.1 Preparation
* Pause the relevant pipeline in councourse or with fly-cli
* Scale the voucher app down to 0, e.g.:
  ```
  kubectl -n galoy-staging-addons scale deployment voucher --replicas=0
  ```
##### 2.1.2 Clone the database from a previous state
* Identify the name of the database instance in [console.cloud.google.com/sql](https://console.cloud.google.com/sql) or run the command on the bastion after log in:
  ```
  gcloud instances list
  ```
* Clone the database from a previous time when the db is known to be intact. Change only the suffix of the instance name. For example if the current name of the instance is galoy-staging-voucher-85bfacd1, a new name could be galoy-staging-voucher-85bfacd2. Use the RFC C3339 timestamp format.
  ```
  gcloud instances clone FROM_INSTANCE_NAME TO_INSTANCE_NAME --point-in-time TIME_FORMAT_RFC_3339
  ```

##### 2.1.3 Switch to the cloned database instance
This needs manual manipulation of the terraform states.

###### ******* Sync the deployments repo to the bastion
* run the following command locally inside this repo:
  ```
  cd gcp/galoy/staging/inception
  tf init
  cd ../../../..
  make sync-staging # runs the sync-to-bastion.sh - takes the repo to the bastion
  ```

* Connect to the bastion
  ```
  make ssh-bastion
  ```

###### ******* Construct the secrets manually as Concourse would do
* Look for the module in blink-deployments to see the structure of the secret object
* Fill it up with the secrets from the galoy-org cluster
* Create a `var.tfvars` file for example for the addons module:
  ```
  secrets = <<EOT
  {
    "admin_panel_google_oauth_client_id": "",
    "admin_panel_google_oauth_client_secret": "",
    "nostr_private_key": "",
    "pg_admin_password": "",
    "voucher_escrow_api_key": "",
  }
  EOT
  ```

###### ******* See the proposed changes and apply gradually

Run the following command to check if we can properly run terraform commands on the relevant module:
  ```
  cd blink-deployments/gcp/galoy/staging/addons
  tf init
  tf plan
  ```

* In case of an error caused by a k8s pod not being accessible from the bastion use port forwarding, e.g.:
  ```
  k -n galoy-staging-addons port-forward svc/galoy-hydra-admin 4445 &
  ```
* Then change the endpoint to 127.0.0.1:4445 in the terraform config in blink-deployments before running `tf plan` again.

* To target a specific resource use:
  ```
  tf plan -target <module name from 'tf state list'>
  ```
###### 2.1.3.4 Import the new database name suffix, instance and sql admin user
* We'll encode the new database name suffix to import it into the db_name_suffix resource in the terraform state.
  ```
  terraform state rm module.addons.module.voucher_pg.random_id.db_name_suffix
  OUTPUT=`echo "85bfacd2" | xxd -r -p | base64 | tr '/+' '_-' | tr -d '='`
  terraform import module.addons.module.voucher_pg.random_id.db_name_suffix $OUTPUT
  ```
* Import the new database instance
  ```
  tf state rm module.addons.module.voucher_pg.google_sql_database_instance.instance
  terraform import module.addons.module.voucher_pg.google_sql_database_instance.instance galoy-staging/galoy-staging-voucher-85bfacd2
  ```
* Import the new SQL admin user
  ```
  tf state rm module.addons.module.voucher_pg.google_sql_user.admin
  terraform import module.addons.module.voucher_pg.google_sql_user.admin galoy-staging/galoy-staging-voucher-85bfacd2/galoy-staging-voucher-admin
  ```
* Run tf apply targeting the sql user to see if the import was successful. If there's a diff that shows the password needs to be changed, then apply it:
  ```
  tf apply -target module.addons.module.voucher_pg.google_sql_user.admin
  ```
###### ******* Import or update remaining resources
* Target the whole database to see if there is a diff. We expect a minor diff to be present, wherein some of the resource will be updated with the new database name suffix. Enter yes to apply the changes.
  ```
  tf apply -target module.addons.module.voucher_pg
  ```
###### ******* Get the application back up
* Unpause the Concourse pipeline and trigger it to run.
* It should update the secret where the pg connection string was changed.

* Scale the app back up:
  ```
  kubectl -n galoy-staging-addons scale deployment voucher --replicas=0
  ```

##### 2.1.4 When verified that the recovery was successful delete the corrupted database
* disable deletion protection for the unused database
* delete the unused database

##### 2.2 Restore an instance from a backup
The automated backup is daily so can lose maximum up to a day worth of data.

1. Navigate to Cloud SQL in GCP console.
2. Find the backup you want to use.
3. Select "Restore".
4. Choose the database to overwrite as the destination.
5. Start the restore process.

For detailed instructions, refer to: [GCP PostgreSQL Restore Documentation](https://cloud.google.com/sql/docs/postgres/backup-recovery/restoring)

#### 3. MongoDB Recovery
The automated backup is daily so can lose maximum up to a day worth of data.

Ensure you have the latest backup available. There is a copy saved on AWS accessible by the AWS accounts of Kartik, Justin and Nicolas.<br />
Follow the documented [MongoDB recovery procedure](debugging.md#restoring-mongodb-backup).

### Verification and Testing
1. Verify all components are running correctly in kubernetes.
2. Test the functionalities of the applications.
3. Monitor system logs and Honeycomb for any errors or inconsistencies.

Example of the steps taken to test these instructions: https://github.com/GaloyMoney/blink-deployments/pull/7512#issue-**********

Remember to update this document regularly as the system architecture or recovery procedures change.
