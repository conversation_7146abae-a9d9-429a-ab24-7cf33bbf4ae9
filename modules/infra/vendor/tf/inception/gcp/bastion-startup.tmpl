#!/bin/bash

sed -i'' 's/pam_mkhomedir.so$/pam_mkhomedir.so umask=0077/' /etc/pam.d/sshd # Make all files private by default

curl -fsSL https://apt.releases.hashicorp.com/gpg | apt-key add -
apt-add-repository "deb [arch=amd64] https://apt.releases.hashicorp.com $(lsb_release -cs) main"

echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list
curl https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key --keyring /usr/share/keyrings/cloud.google.gpg add -

# Keep make and terraform the first items installed as they are needed
# for testflight to complete
apt-get update && apt-get install -y make jq tree wget redis postgresql vault unzip gnupg

curl --proto '=https' --tlsv1.2 -fsSL https://get.opentofu.org/install-opentofu.sh -o install-opentofu.sh \
  && chmod +x install-opentofu.sh \
  && ./install-opentofu.sh --install-method standalone --opentofu-version ${opentofu_version} \
  && rm -f install-opentofu.sh

cat <<EOF > /etc/profile.d/aliases.sh
alias tf="terraform"
alias k="kubectl"
alias g="git"
alias gs="git status"
alias kauth="gcloud container clusters get-credentials ${cluster_name} --zone ${zone} --project ${project}"

export GALOY_ENVIRONMENT=${project}
export KUBE_CONFIG_PATH=~/.kube/config
export BRIA_ADMIN_API_URL=http://********:2743
export BRIA_API_URL=http://********:2742
source <(bos completion bash)
EOF

%{ if bastion_revoke_on_exit }
cat <<EOF >> /etc/profile.d/auto-revoke.sh
onExit() {
  gcloud auth revoke
  echo Y | gcloud auth application-default revoke
}
trap onExit EXIT
EOF
%{ endif }

curl -LO https://storage.googleapis.com/kubernetes-release/release/v${kubectl_version}/bin/linux/amd64/kubectl
chmod +x ./kubectl
mv ./kubectl /usr/local/bin

curl https://raw.githubusercontent.com/helm/helm/master/scripts/get-helm-3 | bash

mkdir bria && cd bria \
  && wget https://github.com/GaloyMoney/bria/releases/download/${bria_version}/bria-x86_64-unknown-linux-musl-${bria_version}.tar.gz -O bria.tar.gz \
  && tar --strip-components=1 -xf bria.tar.gz \
  && mv bria /usr/local/bin && cd ../ && rm -rf ./bria

wget -O- https://k14s.io/install.sh | bash

wget https://github.com/bodymindarts/cepler/releases/download/v${cepler_version}/cepler-x86_64-unknown-linux-musl-${cepler_version}.tar.gz \
  && tar -zxvf cepler-x86_64-unknown-linux-musl-${cepler_version}.tar.gz \
  && mv cepler-x86_64-unknown-linux-musl-${cepler_version}/cepler /usr/local/bin \
  && chmod +x /usr/local/bin/cepler \
  && rm -rf ./cepler-*

wget https://bitcoincore.org/bin/bitcoin-core-${bitcoin_version}/bitcoin-${bitcoin_version}-x86_64-linux-gnu.tar.gz \
  && tar -xvf bitcoin-${bitcoin_version}-x86_64-linux-gnu.tar.gz \
  && mv bitcoin-${bitcoin_version}/bin/* /usr/local/bin

wget -qO - https://www.mongodb.org/static/pgp/server-5.0.asc | apt-key add - \
  && echo "deb [ arch=amd64,arm64 ] https://repo.mongodb.org/apt/ubuntu bionic/mongodb-org/5.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-5.0.list \
  && apt-get update \
  && apt-get install -y mongodb-org-tools

wget https://github.com/lightningnetwork/lnd/releases/download/v${lnd_version}-beta/lnd-linux-amd64-v${lnd_version}-beta.tar.gz \
   && tar -xvf lnd-linux-amd64-v${lnd_version}-beta.tar.gz \
   && mv lnd-linux-amd64-v${lnd_version}-beta/lncli /usr/local/bin \
   && rm -rf lnd-linux-amd64-v${lnd_version}-*

mkdir k9s && cd k9s \
   && wget https://github.com/derailed/k9s/releases/download/v${k9s_version}/k9s_Linux_amd64.tar.gz \
   && tar -xvf k9s_Linux_amd64.tar.gz \
   && mv k9s /usr/local/bin \
   && cd .. && rm -rf k9s*

mkdir kratos && cd kratos \
   && wget https://github.com/ory/kratos/releases/download/v${kratos_version}/kratos_${kratos_version}-linux_64bit.tar.gz \
   && tar -xvf kratos_${kratos_version}-linux_64bit.tar.gz \
   && mv kratos /usr/local/bin \
   && cd .. && rm -rf kratos*

curl -sL https://deb.nodesource.com/setup_20.x | bash - \
  && apt-get install -y nodejs \
  && npm config set prefix '/usr' \
  && npm i -g balanceofsatoshis@${bos_version} \
  && ln -s ../lib/node_modules/balanceofsatoshis/bos /usr/bin/

apt-get install -y google-cloud-sdk-gke-gcloud-auth-plugin
