resource "google_monitoring_notification_channel" "pagerduty_channel" {
  display_name = "PagerDuty"
  type         = "pagerduty"
  project      = local.gcp_project
  enabled      = true

  labels = {
    service_key = local.pagerduty_api_key
  }
}

resource "google_monitoring_alert_policy" "high_cpu_usage" {
  display_name = "High CPU Usage Alert"
  project      = local.gcp_project

  conditions {
    display_name = "CPU usage over 80%"

    condition_threshold {
      filter     = "metric.type=\"cloudsql.googleapis.com/database/cpu/utilization\" AND resource.type=\"cloudsql_database\""
      duration   = "300s"
      comparison = "COMPARISON_GT"

      threshold_value = 0.8
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
    }
  }

  combiner = "OR"

  notification_channels = [google_monitoring_notification_channel.pagerduty_channel.id]

  documentation {
    content   = "CPU utilization is over 80% for more than 5 minutes."
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "high_disk_usage" {
  display_name = "High Disk Usage Alert"
  project      = local.gcp_project

  conditions {
    display_name = "Disk utilization over 80%"

    condition_threshold {
      filter     = "metric.type=\"cloudsql.googleapis.com/database/disk/utilization\" AND resource.type=\"cloudsql_database\""
      duration   = "300s"
      comparison = "COMPARISON_GT"

      threshold_value = 0.8
      aggregations {
        alignment_period   = "300s"
        per_series_aligner = "ALIGN_MEAN"
      }
    }
  }

  combiner = "OR"

  notification_channels = [google_monitoring_notification_channel.pagerduty_channel.id]

  documentation {
    content   = "Disk utilization is over 80% for more than 5 minutes."
    mime_type = "text/markdown"
  }
}

resource "google_monitoring_alert_policy" "high_read_io" {
  display_name = "High Read IO Operations Alert"
  project      = local.gcp_project

  conditions {
    display_name = "Read IO operations exceed threshold"

    condition_threshold {
      filter     = "metric.type=\"cloudsql.googleapis.com/database/disk/read_ops_count\" AND resource.type=\"cloudsql_database\""
      duration   = "300s"
      comparison = "COMPARISON_GT"

      threshold_value = 250 # Set this threshold based on your typical workload and performance needs
      aggregations {
        alignment_period     = "300s"
        per_series_aligner   = "ALIGN_RATE"
        cross_series_reducer = "REDUCE_SUM"
        group_by_fields      = ["resource.label.database_id"]
      }
    }
  }

  combiner = "OR"

  notification_channels = [google_monitoring_notification_channel.pagerduty_channel.id]

  documentation {
    content   = "Disk read IO operations have exceeded the defined threshold for more than 5 minutes."
    mime_type = "text/markdown"
  }
}
