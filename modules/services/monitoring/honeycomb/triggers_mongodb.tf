data "honeycombio_query_specification" "mongodb_slow_exporter" {
  calculation {
    op     = "P90"
    column = "duration_ms"
  }

  time_range = 600 // 10 minutes

  filter {
    column = "service.name"
    op     = "="
    value  = "${local.name_prefix}-exporter"
  }

  filter {
    column = "db.mongodb.collection"
    op     = "exists"
  }
}

resource "honeycombio_query" "mongodb_slow_exporter" {
  dataset    = local.exporter_dataset_name
  query_json = data.honeycombio_query_specification.mongodb_slow_exporter.json
}

resource "honeycombio_trigger" "mongodb_slow_exporter" {
  name        = "${local.name_prefix}-mongodb-slow-exporter"
  description = "Alert when mongodb takes more than 10 minutes to return for exporter"
  alert_type  = "on_true"

  query_id = honeycombio_query.mongodb_slow_exporter.id
  dataset  = local.exporter_dataset_name

  frequency = 600 // 10 minutes

  threshold {
    op    = ">"
    value = 600000 // 10 minutes
  }


  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}
