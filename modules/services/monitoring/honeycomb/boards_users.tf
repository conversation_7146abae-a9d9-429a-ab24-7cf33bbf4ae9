data "honeycombio_query_specification" "phone_declined_breakdown" {
  calculation {
    op = "COUNT"
  }

  filter {
    column = "name"
    op     = "contains"
    value  = "initiateVerify"
  }

  filter {
    column = "error.name"
    op     = "="
    value  = "InvalidTypePhoneProviderError"
  }

  breakdowns = [
    "error.name",
    "error.message"
  ]

  time_range = 604800
}

resource "honeycombio_query" "phone_declined_breakdown" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.phone_declined_breakdown.json
}

resource "honeycombio_query_annotation" "phone_declined_breakdown" {
  dataset  = local.api_dataset_name
  query_id = honeycombio_query.phone_declined_breakdown.id
  name     = "Auth related rest queries"
}

data "honeycombio_query_specification" "auth_rest" {
  calculation {
    op = "COUNT"
  }

  filter {
    column = "http.url"
    op     = "contains"
    value  = "/auth/"
  }

  breakdowns = [
    "code.function.params.deviceId",
    "code.function.params.ip"
  ]

  time_range = 604800
}

resource "honeycombio_query" "auth_rest" {
  dataset    = local.oathkeeper_dataset_name
  query_json = data.honeycombio_query_specification.auth_rest.json
}

resource "honeycombio_query_annotation" "auth_rest" {
  dataset  = local.oathkeeper_dataset_name
  query_id = honeycombio_query.auth_rest.id
  name     = "Auth related rest queries"
}

data "honeycombio_query_specification" "login" {
  calculation {
    op = "COUNT"
  }

  filter {
    column = "graphql.field.path"
    op     = "="
    value  = "userLogin"
  }

  filter {
    column = "graphql.field.path"
    op     = "="
    value  = "captchaRequestAuthCode"
  }

  filter {
    column = "graphql.field.path"
    op     = "="
    value  = "userLoginUpgrade"
  }

  breakdowns = [
    "http.client_ip",
    "http.user_agent",
    "graphql.field.path"
  ]

  filter_combination = "OR"

  time_range = 604800
}

resource "honeycombio_query" "login" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.login.json
}

resource "honeycombio_query_annotation" "login" {
  dataset  = local.api_dataset_name
  query_id = honeycombio_query.login.id
  name     = "Phone login and upgrade"
}

resource "honeycombio_board" "users" {
  name        = "${local.name_prefix}-users"
  description = "Breakdown of ${local.name_prefix}"

  query {
    query_id            = honeycombio_query.phone_declined_breakdown.id
    query_annotation_id = honeycombio_query_annotation.phone_declined_breakdown.id
    caption             = "Phone declined breakdown"
    query_style         = "graph"
  }

  query {
    query_id            = honeycombio_query.auth_rest.id
    query_annotation_id = honeycombio_query_annotation.auth_rest.id
    caption             = "Device account"
    query_style         = "graph"
  }

  query {
    query_id            = honeycombio_query.login.id
    query_annotation_id = honeycombio_query_annotation.login.id
    caption             = "Login"
    query_style         = "graph"
  }
}
