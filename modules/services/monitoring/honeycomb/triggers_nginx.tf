data "honeycombio_query_specification" "high_volume_503" {
  calculation {
    op = "COUNT"
  }

  time_range = 600 // 10 minutes

  filter {
    column = "http.status_code"
    op     = "="
    value  = "503"
  }

  breakdowns = ["peer.address.iponly"]
}

resource "honeycombio_query" "high_volume_503" {
  dataset    = local.ingress_dataset_name
  query_json = data.honeycombio_query_specification.high_volume_503.json
}

resource "honeycombio_trigger" "high_volume_503" {
  name        = "${local.name_prefix}-high-volume-503"
  description = <<-EOF
Alert when nginx is returning many 503 errors. This could be a ddos or ddos-like attack attempt.
Nginx is rate limiting above a few requests/seconds, and returning 503 after that.
It could also be just someone mis-using the API un-intentionally, or many users logging in,
suddently connecting from a shared connection, like a shared Wifi networks
EOF
  alert_type  = "on_true"

  query_id = honeycombio_query.high_volume_503.id
  dataset  = local.ingress_dataset_name

  frequency = 600 // 10 minutes

  threshold {
    op    = ">"
    value = 300
  }


  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}

data "honeycombio_query_specification" "high_401" {
  time_range = 7200

  calculation {
    op     = "COUNT_DISTINCT"
    column = "peer.address.iponly"
  }

  filter {
    column = "http.status_code"
    op     = "="
    value  = "401"
  }

  filter {
    column = "http.host"
    op     = "="
    value  = local.graphql_endpoint
  }

  filter_combination = "AND"

}

resource "honeycombio_query" "high_401" {
  dataset    = local.ingress_dataset_name
  query_json = data.honeycombio_query_specification.high_401.json
}

resource "honeycombio_trigger" "high_401" {
  name        = "${local.name_prefix}-high_401"
  description = <<-EOF
Alert when nginx is returning a high number of 401 errors.
It can be related to users experiencing login issue with the app.
EOF
  alert_type  = "on_true"

  query_id = honeycombio_query.high_401.id
  dataset  = local.ingress_dataset_name

  frequency = 7200 // 2 hour

  threshold {
    op    = ">"
    value = 6
  }


  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}
