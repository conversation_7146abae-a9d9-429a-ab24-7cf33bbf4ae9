variable "name_prefix" {}
variable "graphql_endpoint" {}
variable "new_honeycomb_environment" {}
variable "mattermost_channel_name" {
  description = "The name of the Slack channel to send alerts to"
  # Using an env agnostic name as those names are specified on the account/team level
  default = "mattermost - blink-(env)-notifications"
}

locals {
  name_prefix             = var.name_prefix
  graphql_endpoint        = var.graphql_endpoint
  mattermost_channel_name = var.mattermost_channel_name

  balance_notif_service_name     = "${local.name_prefix}-galoy-balance-notif"
  cronjob_service_name           = "${local.name_prefix}-galoy-cronjob"
  trigger_service_name           = "${local.name_prefix}-trigger"
  stablesats_price_service_name  = "${local.name_prefix}-stablesats-price"
  stablesats_dealer_service_name = "${local.name_prefix}-stablesats-dealer"
  bria_service_name              = "${local.name_prefix}-bria"

  essential_bria_queues = [
    "sync_all_wallets",
    "sync_wallet",
    "process_all_payout_queues",
    "process_payout_queue",
  ]

  galoy_namespace      = "${local.name_prefix}-galoy"
  bitcoin_namespace    = "${local.name_prefix}-bitcoin"
  ingress_namespace    = "${local.name_prefix}-ingress"
  smoketest_namespace  = "${var.name_prefix}-smoketest"
  monitoring_namespace = "${var.name_prefix}-monitoring"

  graphql_admin         = "${local.name_prefix}-graphql-admin"
  graphql_errors_metric = "${local.name_prefix}-graphql-errors"

  new_honeycomb_environment = var.new_honeycomb_environment

  api_dataset_name               = local.new_honeycomb_environment ? "${local.name_prefix}-api" : local.name_prefix
  bitcoin_dataset_name           = local.new_honeycomb_environment ? "${local.name_prefix}-bitcoin" : local.name_prefix
  bria_dataset_name              = local.new_honeycomb_environment ? "${local.name_prefix}-bria" : local.name_prefix
  exporter_dataset_name          = local.new_honeycomb_environment ? "${local.name_prefix}-exporter" : local.name_prefix
  galoy_cronjob_dataset_name     = local.new_honeycomb_environment ? "${local.name_prefix}-galoy-cronjob" : local.name_prefix
  galoy_dataset_name             = local.new_honeycomb_environment ? "${local.name_prefix}-galoy" : local.name_prefix
  ingress_dataset_name           = local.new_honeycomb_environment ? "${local.name_prefix}-ingress" : local.name_prefix
  oathkeeper_dataset_name        = local.new_honeycomb_environment ? "${local.name_prefix}-oathkeeper" : local.name_prefix
  stablesats_price_dataset_name  = local.new_honeycomb_environment ? "${local.name_prefix}-stablesats-price" : local.name_prefix
  stablesats_dealer_dataset_name = local.new_honeycomb_environment ? "${local.name_prefix}-stablesats-dealer" : local.name_prefix
  trigger_dataset_name           = local.new_honeycomb_environment ? "${local.name_prefix}-trigger" : local.name_prefix
}
