
data "honeycombio_query_specification" "application_errors" {
  calculation {
    op = "COUNT"
  }

  time_range = 300 // 5 minutes

  filter {
    column = "error"
    op     = "="
    value  = true
  }

  filter {
    column = "error.level"
    op     = "="
    value  = "critical"
  }

  filter {
    column = "error.name"
    op     = "!="
    value  = "CouldNotFindExpectedTransactionMetadataError"
  }

  breakdowns = ["service.name", "name", "error.name"]
}

resource "honeycombio_query" "application_errors" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.application_errors.json
}

resource "honeycombio_trigger" "application_errors" {
  name        = "${local.name_prefix}-application-errors"
  description = "Alert when some of the application services had an error"
  alert_type  = "on_true"

  query_id = honeycombio_query.application_errors.id
  dataset  = local.api_dataset_name

  frequency = 300 // 5 minutes

  threshold {
    op    = ">"
    value = 0
  }

  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}

data "honeycombio_query_specification" "missing_error_code_errors" {
  calculation {
    op = "COUNT"
  }

  time_range = 300 // 5 minutes

  filter {
    column = "graphql.error.message"
    op     = "exists"
  }

  filter {
    column = "graphql.error.code"
    op     = "does-not-exist"
  }

  filter {
    column = "service.name"
    op     = "!="
    value  = local.graphql_admin
  }

  filter {
    column = "graphql.error.path"
    op     = "exists"
  }

  breakdowns = ["graphql.error.path", "graphql.error.message"]
}

resource "honeycombio_query" "missing_error_code_errors" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.missing_error_code_errors.json
}

resource "honeycombio_trigger" "missing_error_code_errors" {
  name        = "${local.name_prefix}-missing-error-code"
  description = "Alert when an error is missing an error code which suggests it hasn't been handled."
  alert_type  = "on_true"

  query_id = honeycombio_query.missing_error_code_errors.id
  dataset  = local.api_dataset_name

  frequency = 300 // 5 minutes

  threshold {
    op    = ">"
    value = 0
  }

  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}

data "honeycombio_query_specification" "unknown_error_code_errors" {
  calculation {
    op = "COUNT"
  }

  time_range = 300 // 5 minutes

  filter {
    column = "graphql.error.message"
    op     = "starts-with"
    value  = "Unknown"
  }

  breakdowns = ["graphql.error.code", "graphql.error.message"]
}

resource "honeycombio_query" "unknown_error_code_errors" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.unknown_error_code_errors.json
}

resource "honeycombio_trigger" "unknown_error_code_errors" {
  name        = "${local.name_prefix}-unknown-error-code"
  description = <<-EOF
Alert when an error returns an 'unknown' error message to the api end user.
This is usually a signal to add a new explicit error type and make sure it's
mapped to a proper end-user-facing message.
EOF
  alert_type  = "on_true"

  query_id = honeycombio_query.unknown_error_code_errors.id
  dataset  = local.api_dataset_name

  frequency = 300 // 5 minutes

  threshold {
    op    = ">"
    value = 0
  }

  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}


data "honeycombio_query_specification" "onchain_balance_depleted" {
  calculation {
    op = "COUNT"
  }

  time_range = 300 // 5 minutes

  filter {
    column = "name"
    op     = "="
    value  = "grpc.lnrpc.Lightning/EstimateFee"
  }

  filter {
    column = "grpc.error_message"
    op     = "contains"
    value  = "insufficient funds available to construct transaction"
  }
}

resource "honeycombio_query" "onchain_balance_depleted" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.onchain_balance_depleted.json
}

resource "honeycombio_trigger" "onchain_balance_depleted" {
  name        = "${local.name_prefix}-onchain-balance-depleted"
  description = "Alert when onchain balance is depleted"
  alert_type  = "on_true"

  query_id = honeycombio_query.onchain_balance_depleted.id
  dataset  = local.api_dataset_name

  frequency = 300 // 5 minutes

  threshold {
    op    = ">"
    value = 1
  }


  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}
