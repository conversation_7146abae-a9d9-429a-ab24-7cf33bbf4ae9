data "honeycombio_query_specification" "gql_execution" {
  calculation {
    op = "COUNT"
  }

  calculation {
    op     = "P99"
    column = "duration_ms"
  }

  calculation {
    op     = "AVG"
    column = honeycombio_derived_column.err_pct.alias
  }

  filter {
    column = "graphql.source"
    op     = "exists"
  }

  time_range = 604800
}

resource "honeycombio_query" "gql_execution" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.gql_execution.json
}

resource "honeycombio_query_annotation" "gql_execution" {
  dataset  = local.api_dataset_name
  query_id = honeycombio_query.gql_execution.id
  name     = "Gql Queries"
}

data "honeycombio_query_specification" "gql_query_frequency" {
  calculation {
    op = "COUNT"
  }

  calculation {
    op     = "P95"
    column = "duration_ms"
  }

  filter {
    column = "graphql.field.path"
    op     = "exists"
  }

  breakdowns = ["graphql.field.path"]

  order {
    op    = "COUNT"
    order = "descending"
  }

  time_range = 604800
}

resource "honeycombio_query" "gql_query_frequency" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.gql_query_frequency.json
}

resource "honeycombio_query_annotation" "gql_query_frequency" {
  dataset  = local.api_dataset_name
  query_id = honeycombio_query.gql_query_frequency.id
  name     = "Gql Query Frequencies"
}

data "honeycombio_query_specification" "gql_slowest_queries" {
  calculation {
    op     = "P95"
    column = "duration_ms"
  }

  filter {
    column = "graphql.field.path"
    op     = "exists"
  }

  breakdowns = ["graphql.field.path"]

  order {
    op     = "P95"
    column = "duration_ms"
    order  = "descending"
  }

  time_range = 604800
}

resource "honeycombio_query" "gql_slowest_queries" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.gql_slowest_queries.json
}

resource "honeycombio_query_annotation" "gql_slowest_queries" {
  dataset  = local.api_dataset_name
  query_id = honeycombio_query.gql_slowest_queries.id
  name     = "Slowest Queries"
}

data "honeycombio_query_specification" "gql_errors" {
  calculation {
    op = "COUNT"
  }

  filter {
    column = "graphql.error.type"
    op     = "exists"
  }

  breakdowns = [
    "graphql.error.original.type",
    "graphql.error.original.message",
    "graphql.error.path"
  ]

  order {
    op    = "COUNT"
    order = "descending"
  }

  time_range = 604800
}

resource "honeycombio_query" "gql_errors" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.gql_errors.json
}

resource "honeycombio_query_annotation" "gql_errors" {
  dataset  = local.api_dataset_name
  query_id = honeycombio_query.gql_errors.id
  name     = "Gql Errors"
}

data "honeycombio_query_specification" "query_type" {
  calculation {
    op = "COUNT"
  }

  filter {
    column = "graphql.operation.type"
    op     = "exists"
  }

  breakdowns = [
    "graphql.operation.type"
  ]

  time_range = 604800
}

resource "honeycombio_query" "query_type" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.query_type.json
}

resource "honeycombio_query_annotation" "query_type" {
  dataset  = local.api_dataset_name
  query_id = honeycombio_query.query_type.id
  name     = "Query by type"
}

data "honeycombio_query_specification" "apollo_client" {
  calculation {
    op = "COUNT"
  }

  filter {
    column = "http.request.header.apollographql_client_name"
    op     = "exists"
  }

  breakdowns = [
    "http.request.header.apollographql_client_name",
    "http.request.header.apollographql_client_version"
  ]

  time_range = 604800
}

resource "honeycombio_query" "apollo_client" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.apollo_client.json
}

resource "honeycombio_query_annotation" "apollo_client" {
  dataset  = local.api_dataset_name
  query_id = honeycombio_query.apollo_client.id
  name     = "Query by apollo client"
}

resource "honeycombio_board" "gql" {
  name        = "${local.name_prefix}-gql"
  description = "Breakdown of ${local.name_prefix}"

  query {
    caption             = "Gql Queries"
    query_id            = honeycombio_query.gql_execution.id
    query_annotation_id = honeycombio_query_annotation.gql_execution.id
    query_style         = "graph"
  }

  query {
    caption             = "Frequency breakdown"
    query_id            = honeycombio_query.gql_query_frequency.id
    query_annotation_id = honeycombio_query_annotation.gql_query_frequency.id
    query_style         = "table"
  }

  query {
    query_id            = honeycombio_query.gql_slowest_queries.id
    query_annotation_id = honeycombio_query_annotation.gql_slowest_queries.id
    caption             = "Slowest queries"
    query_style         = "table"
  }

  query {
    query_id            = honeycombio_query.gql_errors.id
    query_annotation_id = honeycombio_query_annotation.gql_errors.id
    caption             = "Errors"
    query_style         = "table"
  }

  query {
    query_id            = honeycombio_query.query_type.id
    query_annotation_id = honeycombio_query_annotation.query_type.id
    caption             = "Query by type"
    query_style         = "graph"
  }

  query {
    query_id            = honeycombio_query.apollo_client.id
    query_annotation_id = honeycombio_query_annotation.apollo_client.id
    caption             = "Query by apollo clients"
    query_style         = "graph"
  }
}
