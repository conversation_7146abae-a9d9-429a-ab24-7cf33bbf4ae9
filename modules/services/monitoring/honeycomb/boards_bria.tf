data "honeycombio_query_specification" "bria_jobs" {
  calculation {
    op = "COUNT"
  }

  time_range = 86400 // 24h

  filter {
    column = "service.name"
    op     = "="
    value  = local.bria_service_name
  }

  filter {
    column = "job_name"
    op     = "exists"
  }

  breakdowns = ["job_name", "error.level", "error.message"]
}

resource "honeycombio_query" "bria_jobs" {
  dataset    = local.bria_dataset_name
  query_json = data.honeycombio_query_specification.bria_jobs.json
}

resource "honeycombio_query_annotation" "bria_jobs" {
  dataset  = local.bria_dataset_name
  query_id = honeycombio_query.bria_jobs.id
  name     = "Bria Job Queues"
}

data "honeycombio_query_specification" "bria_batches" {
  calculation {
    op = "COUNT"
  }

  time_range = 86400 // 24h

  dynamic "filter" {
    for_each = local.new_honeycomb_environment ? [] : [1]
    content {
      column = "service.name"
      op     = "="
      value  = local.bria_service_name
    }
  }

  filter {
    column = "name"
    op     = "="
    value  = "job.process_payout_queue"
  }

  filter {
    column = "n_unbatched_payouts"
    op     = "exists"
  }

  filter {
    column = "n_unbatched_payouts"
    op     = "!="
    value  = "0"
  }

  breakdowns = ["name", "n_unbatched_payouts", "n_cpfp_utxos", "error"]
}

resource "honeycombio_query" "bria_batches" {
  dataset    = local.bria_dataset_name
  query_json = data.honeycombio_query_specification.bria_batches.json
}

resource "honeycombio_query_annotation" "bria_batches" {
  dataset  = local.bria_dataset_name
  query_id = honeycombio_query.bria_batches.id
  name     = "Bria Batches"
}

data "honeycombio_query_specification" "bria_recent_txs" {
  calculation {
    op = "COUNT"
  }

  time_range = 28800 // 24h

  filter {
    column = "service.name"
    op     = "="
    value  = local.bria_service_name
  }

  filter {
    column = "name"
    op     = "="
    value  = "job.process_payout_queue"
  }

  filter {
    column = "n_unbatched_payouts"
    op     = "exists"
  }

  filter {
    column = "n_unbatched_payouts"
    op     = "!="
    value  = "0"
  }

  breakdowns = ["tx_id", "n_unbatched_payouts", "cpfp_fee_sats", "total_fee_sats", "total_change_sats"]
}

resource "honeycombio_query" "bria_recent_txs" {
  dataset    = local.bria_dataset_name
  query_json = data.honeycombio_query_specification.bria_recent_txs.json
}

resource "honeycombio_query_annotation" "bria_recent_txs" {
  dataset  = local.bria_dataset_name
  query_id = honeycombio_query.bria_recent_txs.id
  name     = "Bria Recent Transactions"
}

resource "honeycombio_board" "bria" {
  name        = "${local.name_prefix}-bria"
  description = "Bria related queries ${local.name_prefix}"

  query {
    caption             = "Bria Batches"
    query_id            = honeycombio_query.bria_batches.id
    query_annotation_id = honeycombio_query_annotation.bria_batches.id
    query_style         = "graph"
  }

  query {
    caption             = "Bria Recent Transactions"
    query_id            = honeycombio_query.bria_recent_txs.id
    query_annotation_id = honeycombio_query_annotation.bria_recent_txs.id
    query_style         = "graph"
  }

  query {
    caption             = "Bria Jobs"
    query_id            = honeycombio_query.bria_jobs.id
    query_annotation_id = honeycombio_query_annotation.bria_jobs.id
    query_style         = "graph"
  }
}
