data "honeycombio_query_specification" "lnd_add_invoice_latency" {
  calculation {
    op     = "AVG"
    column = "duration_ms"
  }

  time_range = 300 // 5 minutes

  filter {
    column = "name"
    op     = "="
    value  = "grpc.lnrpc.Lightning/AddInvoice"
  }
}

resource "honeycombio_query" "lnd_add_invoice_latency" {
  dataset    = local.galoy_cronjob_dataset_name
  query_json = data.honeycombio_query_specification.lnd_add_invoice_latency.json
}

resource "honeycombio_trigger" "lnd_add_invoice_latency_errors" {
  name        = "${local.name_prefix}-lnd-add-invoice-latency"
  description = "The average latency for addInvoice calls to LND has exceeded 1 second.  In the past we have seen huge spikes in the latency for these calls which required a restart of LND."
  alert_type  = "on_true"

  query_id = honeycombio_query.lnd_add_invoice_latency.id
  dataset  = local.galoy_cronjob_dataset_name

  frequency = 300 // 5 minutes

  threshold {
    op    = ">"
    value = 1000 // 1 second
  }

  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}

data "honeycombio_query_specification" "lnd_memory_usage_rate" {
  calculation {
    op     = "RATE_AVG"
    column = "k8s.pod.memory.usage"
  }

  time_range = 600 // 10 minutes

  filter {
    column = "k8s.pod.name"
    op     = "starts-with"
    value  = "lnd"
  }

  breakdowns = ["k8s.pod.name"]
}

resource "honeycombio_query" "lnd_memory_usage_rate" {
  dataset    = local.name_prefix
  query_json = data.honeycombio_query_specification.lnd_memory_usage_rate.json
}

resource "honeycombio_trigger" "lnd_memory_usage_rate_high" {
  name        = "${local.name_prefix}-lnd-memory-usage-rate-high"
  description = "The rate of change of an lnd pod memory usage exceeds 300 MiB.  In the past we have seen huge spikes in the memory usage of LND before it has become unavailable."
  alert_type  = "on_true"

  query_id = honeycombio_query.lnd_memory_usage_rate.id
  dataset  = local.name_prefix

  frequency = 300 // 5 minutes

  threshold {
    op    = ">"
    value = 300000000 // 300 MiB
  }

  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}
