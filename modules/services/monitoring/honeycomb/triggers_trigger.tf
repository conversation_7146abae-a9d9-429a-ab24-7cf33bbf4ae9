
data "honeycombio_query_specification" "trigger_errors" {
  calculation {
    op = "COUNT"
  }

  time_range = 300 // 5 min

  filter {
    column = "service.name"
    op     = "="
    value  = local.trigger_service_name
  }

  filter {
    column = "error"
    op     = "="
    value  = true
  }

  filter {
    column = "error.level"
    op     = "="
    value  = "critical"
  }

  breakdowns = ["name", "error.name"]
}

resource "honeycombio_query" "trigger_errors" {
  dataset    = local.trigger_dataset_name
  query_json = data.honeycombio_query_specification.trigger_errors.json
}

resource "honeycombio_trigger" "trigger_errors" {
  name        = "${local.name_prefix}-trigger-errors"
  description = "Alert when trigger had an error"
  alert_type  = "on_true"

  query_id = honeycombio_query.trigger_errors.id
  dataset  = local.trigger_dataset_name

  frequency = 300 // 5 min

  threshold {
    op    = ">"
    value = 0
  }

  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}

data "honeycombio_query_specification" "trigger_pending_held_invoices" {
  calculation {
    op = "COUNT"
  }

  time_range = 900 // 15 minutes

  filter {
    column = "service.name"
    op     = "contains"
    value  = "exporter"
  }

  filter {
    column = "name"
    op     = "="
    value  = "metrics"
  }

  filter {
    column = "getHeldInvoicesCount_value"
    op     = "!="
    value  = "0"
  }

  breakdowns = ["service.name", "name"]
}

resource "honeycombio_query" "trigger_pending_held_invoices" {
  dataset    = local.trigger_dataset_name
  query_json = data.honeycombio_query_specification.trigger_pending_held_invoices.json
}

resource "honeycombio_trigger" "trigger_pending_held_invoices" {
  name        = "${local.name_prefix}-application-errors"
  description = "Alert when there are pending held invoices"
  alert_type  = "on_true"

  query_id = honeycombio_query.trigger_pending_held_invoices.id
  dataset  = local.trigger_dataset_name

  frequency = 900 // 15 minutes

  threshold {
    op    = ">"
    value = 0
  }


  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}
