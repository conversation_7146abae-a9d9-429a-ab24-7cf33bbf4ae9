data "honeycombio_query_specification" "cronjob_events" {
  calculation {
    op = "COUNT"
  }

  time_range = 86400 // 24 hours

  filter {
    column = "service.name"
    op     = "="
    value  = local.cronjob_service_name
  }
}

resource "honeycombio_query" "cronjob_events" {
  dataset    = local.galoy_cronjob_dataset_name
  query_json = data.honeycombio_query_specification.cronjob_events.json
}

resource "honeycombio_trigger" "cronjob_didn_not_run" {
  name        = "${local.name_prefix}-cronjob-did-not-run"
  description = "Alert when cronjob didn't run for > 24 hours"
  alert_type  = "on_true"

  query_id = honeycombio_query.cronjob_events.id
  dataset  = local.galoy_cronjob_dataset_name

  frequency = 21600 // 6 hours

  threshold {
    op    = "<="
    value = 0
  }

  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}

data "honeycombio_query_specification" "cronjob_errors" {
  calculation {
    op = "COUNT"
  }

  time_range = 43100 // 12 hours

  filter {
    column = "service.name"
    op     = "="
    value  = local.cronjob_service_name
  }

  filter {
    column = "error"
    op     = "="
    value  = true
  }

  filter {
    column = "error.level"
    op     = "="
    value  = "critical"
  }

  breakdowns = ["name", "error.name"]
}

resource "honeycombio_query" "cronjob_errors" {
  dataset    = local.galoy_cronjob_dataset_name
  query_json = data.honeycombio_query_specification.cronjob_errors.json
}

resource "honeycombio_trigger" "cronjob_errors" {
  name        = "${local.name_prefix}-cronjob-errors"
  description = "Alert when cronjob had an error"
  alert_type  = "on_true"

  query_id = honeycombio_query.cronjob_errors.id
  dataset  = local.galoy_cronjob_dataset_name

  frequency = 21600 // 6 hours

  threshold {
    op    = ">"
    value = 0
  }

  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}
