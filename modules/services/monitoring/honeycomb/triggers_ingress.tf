data "honeycombio_query_specification" "no_ingress_activity" {
  calculation {
    op = "COUNT"
  }

  time_range = 300 // 5 minutes

  filter {
    column = "service.name"
    op     = "contains"
    value  = "ingress"
  }

}

resource "honeycombio_query" "no_ingress_activity" {
  dataset    = local.ingress_dataset_name
  query_json = data.honeycombio_query_specification.no_ingress_activity.json
}

resource "honeycombio_trigger" "no_ingress_activity" {
  name        = "${local.name_prefix}-no-ingress-activity"
  description = <<-EOF
This alert triggers when the number of ingress events is zero for 5 minutes.
EOF
  alert_type  = "on_true"

  query_id = honeycombio_query.no_ingress_activity.id
  dataset  = local.ingress_dataset_name

  frequency = 120 // 2 minutes

  threshold {
    op    = "<="
    value = 0
  }


  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}
