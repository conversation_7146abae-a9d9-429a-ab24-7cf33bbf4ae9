data "honeycombio_query_specification" "suspiciously_high_phone_prefix_rate" {
  calculation {
    op = "COUNT"
  }

  time_range = 86400 // 1 day

  filter {
    column = "graphql.operation.type"
    op     = "exists"
  }

  filter {
    column = "graphql.operation.type"
    op     = "="
    value  = "mutation"
  }

  filter {
    column = "graphql.operation.name"
    op     = "="
    value  = "captchaRequestAuthCode"
  }

  filter {
    column = "derived.phone.commonPrefix"
    op     = "exists"
  }

  breakdowns = ["derived.phone.commonPrefix"]

}

resource "honeycombio_query" "suspiciously_high_phone_prefix_rate" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.suspiciously_high_phone_prefix_rate.json
}

resource "honeycombio_trigger" "suspiciously_high_phone_prefix_rate" {
  name        = "${local.name_prefix}-high-phone-prefix-rate"
  description = <<-EOF
This alert triggers when the number of phone numbers with matching 7 character prefixes
that request an authentication code exceeds 100 numbers over a period of 1 days.
EOF
  alert_type  = "on_true"

  query_id = honeycombio_query.suspiciously_high_phone_prefix_rate.id
  dataset  = local.api_dataset_name

  frequency = 21600 // 6 hours

  threshold {
    op    = ">"
    value = 100
  }


  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}
