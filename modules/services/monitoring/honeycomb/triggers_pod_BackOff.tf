// This file contains a trigger for detecting pod BackOff issues using the new OTEL data structure
// with k8s.event.reason: BackOff

data "honeycombio_query_specification" "pod_backoff" {
  calculation {
    op = "COUNT"
  }

  breakdowns = ["k8s.object.name"]

  filter {
    column = "k8s.namespace.name"
    op     = "does-not-contain"
    value  = "testflight"
  }

  filter {
    column = "k8s.event.reason"
    op     = "="
    value  = "BackOff"
  }

  time_range = 300 // 5 minutes
}

resource "honeycombio_query" "pod_backoff" {
  dataset    = local.name_prefix
  query_json = data.honeycombio_query_specification.pod_backoff.json
}

resource "honeycombio_trigger" "pod_backoff" {
  name        = "${local.name_prefix}-pod-backoff"
  description = "Alert when Kubernetes events with reason 'BackOff' are detected (alerts only on state change)"

  alert_type = "on_change"

  frequency = 300 // 5 minutes

  query_id = honeycombio_query.pod_backoff.id
  dataset  = local.name_prefix

  threshold {
    op    = ">"
    value = 0 // Trigger on any BackOff event
  }

  recipient {
    type   = "webhook"
    target = local.mattermost_channel_name
  }
  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}
