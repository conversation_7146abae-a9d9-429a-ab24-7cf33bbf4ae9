resource "honeycombio_derived_column" "err_pct" {
  alias       = "err_pct"
  expression  = "IF(BOOL($error), 100, 0)"
  description = "error_percentile"

  dataset = local.api_dataset_name
}

resource "honeycombio_derived_column" "five_hundred_pct" {
  alias       = "500_pct"
  expression  = "IF(GTE($http.status_code, 500), 100, 0)"
  description = "Percentile of 500 errors"

  dataset = local.ingress_dataset_name
}

resource "honeycombio_derived_column" "four_hundred_pct" {
  alias       = "401_pct"
  expression  = "IF(EQUALS($http.status_code, 401), 100, 0)"
  description = "Percentile of 401 errors"

  dataset = local.ingress_dataset_name
}

resource "honeycombio_derived_column" "error_warn_pct" {
  alias       = "error_warn_pct"
  expression  = "IF(EQUALS($error.level, \"warn\"), 100, 0)"
  description = "Percentile of warn level errors"

  dataset = local.api_dataset_name
}

resource "honeycombio_derived_column" "phone_country_code" {
  alias       = "derived.phone.countryCode"
  expression  = "REG_VALUE($code.function.params.phone, `(?:\\+|00)(1|7|2[07]|3[0123469]|4[013456789]|5[12345678]|6[0123456]|8[1246]|9[0123458]|(?:2[12345689]|3[578]|42|5[09]|6[789]|8[035789]|9[679])\\d)`)"
  description = "Phone country code"

  dataset = local.api_dataset_name
}

resource "honeycombio_derived_column" "phone_7_char_prefix" {
  alias       = "derived.phone.commonPrefix"
  expression  = "REG_VALUE($graphql.variables.input.phone, `.......`)"
  description = "Seven character phone number prefix"

  dataset = local.api_dataset_name
}

resource "honeycombio_derived_column" "address_iponly" {
  alias       = "peer.address.iponly"
  expression  = "REG_VALUE($peer.address, `[^:]*`)"
  description = "Ip address with port to use for per ip addres grouping"

  dataset = local.ingress_dataset_name
}

resource "honeycombio_derived_column" "app_payments" {
  alias       = "app.payments"
  expression  = "STARTS_WITH($name, \"app.payments\")"
  description = "All the payments related use cases"

  dataset = local.api_dataset_name
}

resource "honeycombio_derived_column" "graphql_payments" {
  alias       = "graphql.payments"
  expression  = "AND(EQUALS($name, \"graphql.resolve\"),OR(CONTAINS($graphql.source, \"lnInvoiceCreateOnBehalfOfRecipient\"), CONTAINS($graphql.source, \"lnUsdInvoiceCreateOnBehalfOfRecipient\"),CONTAINS($graphql.source, \"lnNoAmountInvoiceCreateOnBehalfOfRecipient\"),CONTAINS($graphql.source, \"lnInvoiceFeeProbe\"),CONTAINS($graphql.source, \"lnUsdInvoiceFeeProbe\"),CONTAINS($graphql.source, \"lnNoAmountInvoiceFeeProbe\"),CONTAINS($graphql.source, \"lnNoAmountUsdInvoiceFeeProbe\"),CONTAINS($graphql.source, \"lnUsdInvoiceCreate\"),CONTAINS($graphql.source, \"lnNoAmountInvoiceCreate\"),CONTAINS($graphql.source, \"lnInvoicePaymentSend\"),CONTAINS($graphql.source, \"lnNoAmountInvoicePaymentSend\"),CONTAINS($graphql.source, \"lnNoAmountUsdInvoicePaymentSend\"),CONTAINS($graphql.source, \"intraLedgerPaymentSend\"),CONTAINS($graphql.source, \"intraLedgerUsdPaymentSend\"),CONTAINS($graphql.source, \"onChainAddressCreate\"),CONTAINS($graphql.source, \"onChainAddressCurrent\"),CONTAINS($graphql.source, \"onChainPaymentSend\"),CONTAINS($graphql.source, \"onChainUsdPaymentSend\"),CONTAINS($graphql.source, \"onChainUsdPaymentSendAsBtcDenominated\"),CONTAINS($graphql.source, \"lnInvoiceFeeProbe\"),CONTAINS($graphql.source, \"onChainPaymentSendAll\"),CONTAINS($graphql.source, \"lnInvoiceCreate\"),))"
  description = "Returns true when the graphql operation is related to payments and enduser.id exists"

  dataset = local.api_dataset_name
}

resource "honeycombio_derived_column" "derived_payment_success_intraledger" {
  alias       = "derived.payment.success.intraledger"
  expression  = "IF( OR( EQUALS($name, \"mutation intraLedgerPaymentSend\"), EQUALS($name, \"mutation intraLedgerUsdPaymentSend\"), ), NOT(EXISTS($error)) )"
  description = "intraledger mutations"

  dataset = local.api_dataset_name
}

resource "honeycombio_derived_column" "derived_payment_success_lightning" {
  alias       = "derived.payment.success.lightning"
  expression  = "IF( OR( EQUALS($name, \"mutation lnInvoicePaymentSend\"), EQUALS($name, \"mutation lnNoAmountInvoicePaymentSend\"), EQUALS($name, \"mutation lnNoAmountUsdInvoicePaymentSend\"), ), NOT(EXISTS($error)) )"
  description = "lightning mutations"

  dataset = local.api_dataset_name
}

resource "honeycombio_derived_column" "derived_payment_success_onchain" {
  alias       = "derived.payment.success.onchain"
  expression  = "IF( OR( EQUALS($name, \"mutation onChainPaymentSend\"), EQUALS($name, \"mutation onChainPaymentSendAll\"), EQUALS($name, \"mutation onChainUsdPaymentSend\"), EQUALS($name, \"mutation onChainUsdPaymentSendAsBtcDenominated\"), ), NOT(EXISTS($error)) )"
  description = "onchain mutations"

  dataset = local.api_dataset_name
}

resource "honeycombio_derived_column" "current_position" {
  alias       = "stablesats.internal.current_position"
  expression  = "DIV(FLOAT($current_position), -100.0)"
  description = "current_position from stablesats hedging okex job adjust funding/hedge"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "exposure_liability_ratio" {
  alias       = "stablesats.internal.exposure_liability_ratio"
  expression  = "DIV(MUL(-1.0, FLOAT($current_position)), FLOAT($target_liability))"
  description = "exposure_liability_ratio derived from stablesats hedging okex job adjust funding/hedge fields"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "exposure_leverage_ratio" {
  alias       = "stablesats.internal.exposure_leverage_ratio"
  expression  = "DIV(MUL(-1.0, FLOAT($current_position)), MUL(FLOAT($last_price_in_usd_cents), FLOAT(REG_VALUE($trading_available_balance, `total_amt_in_btc=([+-]?([0-9]*[.])?[0-9]+)`))))"
  description = "exposure_leverage_ratio derived from stablesats hedging okex job adjust funding fields"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "margin_leverage_ratio" {
  alias       = "stablesats.internal.margin_leverage_ratio"
  expression  = "DIV(FLOAT(REG_VALUE($trading_available_balance, `total_amt_in_btc=([+-]?([0-9]*[.])?[0-9]+)`)), FLOAT(REG_VALUE($trading_available_balance, `used_amt_in_btc=([+-]?([0-9]*[.])?[0-9]+)`)))"
  description = "margin_leverage_ratio derived from stablesats hedging okex job adjust funding fields"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "last_price" {
  alias       = "stablesats.internal.last_price"
  expression  = "FLOAT($last_price)"
  description = "last_price from stablesats okex-client"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "last_price_in_usd" {
  alias       = "stablesats.internal.last_price_in_usd"
  expression  = "DIV(FLOAT($last_price_in_usd_cents),100)"
  description = "last_price_in_usd_cents from stablesats hedging okex job adjust funding"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "liability" {
  alias       = "stablesats.internal.liability"
  expression  = "DIV(FLOAT($liability),100.0)"
  description = "liability from stablesats ledger balances"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "target_liability" {
  alias       = "stablesats.internal.target_liability"
  expression  = "DIV(FLOAT($target_liability), 100.0)"
  description = "target_liability from stablesats hedging okex job adjust funding/hedge"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "notional_usd" {
  alias       = "stablesats.internal.notional_usd"
  expression  = "FLOAT($notional_usd)"
  description = "notional_usd from stablesats okex-client"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "position_in_ct" {
  alias       = "stablesats.internal.position_in_ct"
  expression  = "INT($position_in_ct)"
  description = "position_in_ct from stablesats okex-client"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "signed_usd_exposure" {
  alias       = "stablesats.internal.signed_usd_exposure"
  expression  = "DIV(FLOAT(REG_VALUE($signed_usd_exposure, `([+-]?([0-9]*[.])?[0-9]+)`)), 100.0)"
  description = "signed_usd_exposure from stablesats hedging okex"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "funding_deposit_action" {
  alias       = "stablesats.internal.funding_deposit_action"
  expression  = "IF(CONTAINS($action, \"OnchainDeposit\"), FLOAT(REG_VALUE($action, `([+-]?([0-9]*[.])?[0-9]+)`)), IF(CONTAINS($action, \"OnchainWithdraw\"), MUL(-1.0, FLOAT(REG_VALUE($action, `([+-]?([0-9]*[.])?[0-9]+)`))), 0 ) )"
  description = "action from stablesats hedging okex job adjust funding"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "funding_transfer_action" {
  alias       = "stablesats.internal.funding_transfer_action"
  expression  = "IF(CONTAINS($action, \"TransferFundingToTrading\"), FLOAT(REG_VALUE($action, `([+-]?([0-9]*[.])?[0-9]+)`)), IF(CONTAINS($action, \"TransferTradingToFunding\"), MUL(-1.0, FLOAT(REG_VALUE($action, `([+-]?([0-9]*[.])?[0-9]+)`))), 0 ) )"
  description = "action from stablesats hedging okex job adjust funding"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "hedge_action" {
  alias       = "stablesats.internal.hedge_action"
  expression  = "IF(CONTAINS($action, \"Buy\"), FLOAT(REG_VALUE($action, `([+-]?([0-9]*[.])?[0-9]+)`)), IF(CONTAINS($action, \"Sell\"), MUL(-1.0, FLOAT(REG_VALUE($action, `([+-]?([0-9]*[.])?[0-9]+)`))), 0 ) )"
  description = "action from stablesats hedging okex job adjust hedge"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "funding_available_balance_total" {
  alias       = "stablesats.internal.funding_available_balance_total"
  expression  = "FLOAT(REG_VALUE($funding_available_balance, `total_amt_in_btc=([+-]?([0-9]*[.])?[0-9]+)`))"
  description = "funding_available_balance_total from stablesats hedging okex job adjust funding"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "trading_available_balance_free" {
  alias       = "stablesats.internal.trading_available_balance_free"
  expression  = "FLOAT(REG_VALUE($trading_available_balance, `free_amt_in_btc=([+-]?([0-9]*[.])?[0-9]+)`))"
  description = "trading_available_balance_free from stablesats hedging okex job adjust funding"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "trading_available_balance_used" {
  alias       = "stablesats.internal.trading_available_balance_used"
  expression  = "FLOAT(REG_VALUE($trading_available_balance, `used_amt_in_btc=([+-]?([0-9]*[.])?[0-9]+)`))"
  description = "trading_available_balance_used from stablesats hedging okex job adjust funding"

  dataset = local.stablesats_dealer_dataset_name
}

resource "honeycombio_derived_column" "trading_available_balance_total" {
  alias       = "stablesats.internal.trading_available_balance_total"
  expression  = "FLOAT(REG_VALUE($trading_available_balance, `total_amt_in_btc=([+-]?([0-9]*[.])?[0-9]+)`))"
  description = "trading_available_balance_total from stablesats hedging okex job adjust funding"

  dataset = local.stablesats_dealer_dataset_name
}
