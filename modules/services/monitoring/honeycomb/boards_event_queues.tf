data "honeycombio_query_specification" "bria_sequences" {
  calculation {
    op = "COUNT"
  }

  filter {
    column = "event.sequence"
    op     = "exists"
  }

  order {
    column = "event.sequence"
    order  = "descending"
  }

  breakdowns = [
    "event.sequence",
    "event.type"
  ]

  time_range = 10800 // 3 hours
}

resource "honeycombio_query" "bria_sequences" {
  dataset    = local.bria_dataset_name
  query_json = data.honeycombio_query_specification.bria_sequences.json
}

resource "honeycombio_query_annotation" "bria_sequences" {
  dataset  = local.bria_dataset_name
  query_id = honeycombio_query.bria_sequences.id
  name     = "Bria event sequences"
}

resource "honeycombio_board" "event_queues" {
  name        = "${local.name_prefix}-event-queues"
  description = "Visualization of event queues"

  query {
    query_id            = honeycombio_query.bria_sequences.id
    query_annotation_id = honeycombio_query_annotation.bria_sequences.id
    caption             = "Bria event sequences"
    query_style         = "combo"
  }
}
