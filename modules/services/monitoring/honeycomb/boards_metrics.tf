data "honeycombio_query_specification" "cpu_usage" {
  calculation {
    op     = "AVG"
    column = "k8s.pod.cpu.utilization"
  }

  calculation {
    op     = "HEATMAP"
    column = "k8s.pod.cpu.utilization"
  }

  order {
    op     = "AVG"
    column = "k8s.pod.cpu.utilization"
    order  = "descending"
  }

  breakdowns = ["k8s.pod.name"]
}

data "honeycombio_query_specification" "memory_usage" {
  calculation {
    op     = "AVG"
    column = "k8s.pod.memory.usage"
  }

  calculation {
    op     = "HEATMAP"
    column = "k8s.pod.memory.usage"
  }

  order {
    op     = "AVG"
    column = "k8s.pod.memory.usage"
    order  = "descending"
  }

  breakdowns = ["k8s.pod.name"]
}

data "honeycombio_query_specification" "api_metrics" {
  calculation {
    op     = "AVG"
    column = "k8s.pod.cpu.utilization"
  }

  calculation {
    op     = "AVG"
    column = "k8s.pod.memory.usage"
  }

  filter {
    column = "k8s.pod.name"
    op     = "contains"
    value  = "api"
  }
}

resource "honeycombio_query" "cpu_usage" {
  dataset    = local.name_prefix
  query_json = data.honeycombio_query_specification.cpu_usage.json
}
resource "honeycombio_query_annotation" "cpu_usage" {
  dataset  = local.name_prefix
  query_id = honeycombio_query.cpu_usage.id
  name     = "Pod CPU Usage"
}

resource "honeycombio_query" "memory_usage" {
  dataset    = local.name_prefix
  query_json = data.honeycombio_query_specification.memory_usage.json
}
resource "honeycombio_query_annotation" "memory_usage" {
  dataset  = local.name_prefix
  query_id = honeycombio_query.memory_usage.id
  name     = "Pod Memory Usage"
}

resource "honeycombio_query" "api_metrics" {
  dataset    = local.name_prefix
  query_json = data.honeycombio_query_specification.api_metrics.json
}
resource "honeycombio_query_annotation" "api_metrics" {
  dataset  = local.name_prefix
  query_id = honeycombio_query.api_metrics.id
  name     = "API metrics"
}

resource "honeycombio_board" "metrics" {
  name        = "${local.name_prefix}-metrics"
  description = "Metrics for ${local.name_prefix}"

  query {
    query_id            = honeycombio_query.cpu_usage.id
    query_annotation_id = honeycombio_query_annotation.cpu_usage.id
    caption             = "CPU Usage"
    query_style         = "table"
  }

  query {
    query_id            = honeycombio_query.memory_usage.id
    query_annotation_id = honeycombio_query_annotation.memory_usage.id
    caption             = "Memory Usage"
    query_style         = "table"
  }

  query {
    query_id            = honeycombio_query.api_metrics.id
    query_annotation_id = honeycombio_query_annotation.api_metrics.id
    caption             = "Api Metrics"
    query_style         = "table"
  }
}
