data "honeycombio_query_specification" "suspiciously_high_sms_rate" {
  calculation {
    op = "COUNT"
  }

  time_range = 10800 // 3 hours

  filter {
    column = "graphql.operation.type"
    op     = "exists"
  }

  filter {
    column = "graphql.operation.type"
    op     = "="
    value  = "mutation"
  }

  filter {
    column = "graphql.operation.name"
    op     = "="
    value  = "captchaRequestAuthCode"
  }

}

resource "honeycombio_query" "suspiciously_high_sms_rate" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.suspiciously_high_sms_rate.json
}

resource "honeycombio_trigger" "suspiciously_high_sms_rate" {
  name        = "${local.name_prefix}-high-sms-rate"
  description = <<-EOF
This alert triggers when the number of SMS messages sent for authentication
exceeds 250 messages over a period of 3 hours.
EOF
  alert_type  = "on_true"

  query_id = honeycombio_query.suspiciously_high_sms_rate.id
  dataset  = local.api_dataset_name

  frequency = 2700 // 45 minutes (the minimum is: time_range / 4 )

  threshold {
    op    = ">"
    value = 250
  }


  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}
