data "honeycombio_query_specification" "suspiciously_high_payment_rate" {
  calculation {
    op = "COUNT"
  }

  time_range = 3600 // 1h

  filter {
    column = "enduser.id"
    op     = "exists"
  }

  filter {
    column = "graphql.payments"
    op     = "="
    value  = "true"
  }

  breakdowns = ["enduser.id", "http.client_ip"]
}

resource "honeycombio_query" "suspiciously_high_payment_rate" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.suspiciously_high_payment_rate.json
}

resource "honeycombio_trigger" "suspiciously_high_payment_rate" {
  name        = "${local.name_prefix}-high-payment-rate"
  description = <<-EOF
This alert triggers when an account (identified by their id) is doing more than 60 payments over a period of 30 minutes.
It doesn't mean this is a wrongful usage of the wallet, but we have seen historically that there is a correlation
EOF
  alert_type  = "on_true"

  query_id = honeycombio_query.suspiciously_high_payment_rate.id
  dataset  = local.api_dataset_name

  frequency = 900 // 15 minutes

  threshold {
    op    = ">"
    value = 600
  }


  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}
