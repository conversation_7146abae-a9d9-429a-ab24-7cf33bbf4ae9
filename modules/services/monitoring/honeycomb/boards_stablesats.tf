data "honeycombio_query_specification" "stablesats_liability" {
  calculation {
    op     = "AVG"
    column = "stablesats.internal.liability"
  }

  filter {
    column = "name"
    op     = "="
    value  = "ledger.balances.target_liability_in_cents"
  }

  filter_combination = "AND"

  order {
    column = "stablesats.internal.liability"
    op     = "AVG"
    order  = "descending"
  }

  breakdowns = [
    "service.name",
    "name"
  ]
}

resource "honeycombio_query" "stablesats_liability" {
  dataset    = local.stablesats_dealer_dataset_name
  query_json = data.honeycombio_query_specification.stablesats_liability.json
}

resource "honeycombio_query_annotation" "stablesats_liability" {
  dataset  = local.stablesats_dealer_dataset_name
  query_id = honeycombio_query.stablesats_liability.id
  name     = "Stablesats Liability"
}


data "honeycombio_query_specification" "stablesats_exposure" {
  calculation {
    op     = "AVG"
    column = "stablesats.internal.signed_usd_exposure"
  }

  filter {
    column = "name"
    op     = "="
    value  = "hedging.okex.conditionally_spawn_adjust_funding"
  }

  filter {
    column = "name"
    op     = "="
    value  = "hedging.okex.conditionally_spawn_adjust_hedge"
  }

  filter_combination = "OR"

  order {
    column = "stablesats.internal.signed_usd_exposure"
    op     = "AVG"
    order  = "descending"
  }

  breakdowns = [
    "service.name",
    "name"
  ]
}

resource "honeycombio_query" "stablesats_exposure" {
  dataset    = local.stablesats_dealer_dataset_name
  query_json = data.honeycombio_query_specification.stablesats_exposure.json
}

resource "honeycombio_query_annotation" "stablesats_exposure" {
  dataset  = local.stablesats_dealer_dataset_name
  query_id = honeycombio_query.stablesats_exposure.id
  name     = "Stablesats Exposure"
}


data "honeycombio_query_specification" "okex_position" {
  calculation {
    op     = "AVG"
    column = "stablesats.internal.notional_usd"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.position_in_ct"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.last_price"
  }

  filter {
    column = "name"
    op     = "="
    value  = "okex_client.get_position_in_signed_usd_cents"
  }

  filter_combination = "AND"

  order {
    column = "stablesats.internal.notional_usd"
    op     = "AVG"
    order  = "descending"
  }

  breakdowns = [
    "service.name",
    "name"
  ]
}

resource "honeycombio_query" "okex_position" {
  dataset    = local.stablesats_dealer_dataset_name
  query_json = data.honeycombio_query_specification.okex_position.json
}

resource "honeycombio_query_annotation" "okex_position" {
  dataset  = local.stablesats_dealer_dataset_name
  query_id = honeycombio_query.okex_position.id
  name     = "Okex Position"
}


data "honeycombio_query_specification" "stablesats_adjust_funding" {
  calculation {
    op     = "MAX"
    column = "stablesats.internal.funding_deposit_action"
  }

  calculation {
    op     = "MAX"
    column = "stablesats.internal.funding_transfer_action"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.exposure_liability_ratio"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.exposure_leverage_ratio"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.margin_leverage_ratio"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.target_liability"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.current_position"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.last_price_in_usd"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.funding_available_balance_total"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.trading_available_balance_free"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.trading_available_balance_used"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.trading_available_balance_total"
  }

  filter {
    column = "name"
    op     = "="
    value  = "hedging.okex.job.adjust_funding"
  }

  filter_combination = "AND"

  order {
    column = "stablesats.internal.margin_leverage_ratio"
    op     = "AVG"
    order  = "descending"
  }

  breakdowns = [
    "service.name",
    "name"
  ]
}

resource "honeycombio_query" "stablesats_adjust_funding" {
  dataset    = local.stablesats_dealer_dataset_name
  query_json = data.honeycombio_query_specification.stablesats_adjust_funding.json
}

resource "honeycombio_query_annotation" "stablesats_adjust_funding" {
  dataset  = local.stablesats_dealer_dataset_name
  query_id = honeycombio_query.stablesats_adjust_funding.id
  name     = "Stablesats Adjust Funding"
}


data "honeycombio_query_specification" "stablesats_adjust_hedge" {
  calculation {
    op     = "MAX"
    column = "stablesats.internal.hedge_action"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.exposure_liability_ratio"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.target_liability"
  }

  calculation {
    op     = "AVG"
    column = "stablesats.internal.current_position"
  }

  filter {
    column = "name"
    op     = "="
    value  = "hedging.okex.job.adjust_hedge"
  }

  filter_combination = "AND"

  order {
    column = "stablesats.internal.target_liability"
    op     = "AVG"
    order  = "descending"
  }

  breakdowns = [
    "service.name",
    "name"
  ]
}

resource "honeycombio_query" "stablesats_adjust_hedge" {
  dataset    = local.stablesats_dealer_dataset_name
  query_json = data.honeycombio_query_specification.stablesats_adjust_hedge.json
}

resource "honeycombio_query_annotation" "stablesats_adjust_hedge" {
  dataset  = local.stablesats_dealer_dataset_name
  query_id = honeycombio_query.stablesats_adjust_hedge.id
  name     = "Stablesats Adjust Hedge"
}


resource "honeycombio_board" "stablesats" {
  name        = "${local.name_prefix}-stablesats"
  description = "Stablesats operational metrics for ${local.name_prefix}"

  query {
    query_id            = honeycombio_query.stablesats_liability.id
    query_annotation_id = honeycombio_query_annotation.stablesats_liability.id
    caption             = "Stablesats Liability"
    query_style         = "graph"
  }

  query {
    query_id            = honeycombio_query.stablesats_exposure.id
    query_annotation_id = honeycombio_query_annotation.stablesats_exposure.id
    caption             = "Stablesats Exposure"
    query_style         = "graph"
  }

  query {
    query_id            = honeycombio_query.okex_position.id
    query_annotation_id = honeycombio_query_annotation.okex_position.id
    caption             = "Okex Position"
    query_style         = "graph"
  }

  query {
    query_id            = honeycombio_query.stablesats_adjust_funding.id
    query_annotation_id = honeycombio_query_annotation.stablesats_adjust_funding.id
    caption             = "Stablesats Adjust Funding"
    query_style         = "graph"
  }

  query {
    query_id            = honeycombio_query.stablesats_adjust_hedge.id
    query_annotation_id = honeycombio_query_annotation.stablesats_adjust_hedge.id
    caption             = "Stablesats Adjust Hedge"
    query_style         = "graph"
  }
}
