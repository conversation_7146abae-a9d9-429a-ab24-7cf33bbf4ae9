data "honeycombio_query_specification" "nginx_errors" {
  calculation {
    op = "COUNT"
  }

  calculation {
    op     = "AVG"
    column = honeycombio_derived_column.five_hundred_pct.alias
  }

  filter {
    column = "service.name"
    op     = "="
    value  = "${local.name_prefix}-ingress"
  }

  time_range = 604800
}

resource "honeycombio_query" "nginx_errors" {
  dataset    = local.ingress_dataset_name
  query_json = data.honeycombio_query_specification.nginx_errors.json
}

resource "honeycombio_query_annotation" "nginx_errors" {
  dataset  = local.ingress_dataset_name
  query_id = honeycombio_query.nginx_errors.id
  name     = "Nginx Errors"
}

data "honeycombio_query_specification" "status_codes" {
  calculation {
    op = "COUNT"
  }

  calculation {
    op     = "P95"
    column = "duration_ms"
  }

  filter {
    column = "service.name"
    op     = "="
    value  = "${local.name_prefix}-ingress"
  }

  breakdowns = ["http.status_code"]

  time_range = 604800
}

resource "honeycombio_query" "status_codes" {
  dataset    = local.ingress_dataset_name
  query_json = data.honeycombio_query_specification.status_codes.json
}

resource "honeycombio_query_annotation" "status_codes" {
  dataset  = local.ingress_dataset_name
  query_id = honeycombio_query.status_codes.id
  name     = "Status Codes"
}

data "honeycombio_query_specification" "nginx_503_potential_ddos" {
  calculation {
    op = "COUNT"
  }

  filter {
    column = "http.status_code"
    op     = "="
    value  = "503"
  }

  breakdowns = [honeycombio_derived_column.address_iponly.alias]

  time_range = 604800
}

resource "honeycombio_query" "nginx_503_potential_ddos" {
  dataset    = local.ingress_dataset_name
  query_json = data.honeycombio_query_specification.nginx_503_potential_ddos.json
}

resource "honeycombio_query_annotation" "nginx_503_potential_ddos" {
  dataset  = local.ingress_dataset_name
  query_id = honeycombio_query.nginx_503_potential_ddos.id
  name     = "Nginx 503 Error Code. Rate limits and potential DDOS attempt"
}

data "honeycombio_query_specification" "oathkeeper_http_code" {
  calculation {
    op = "COUNT"
  }

  filter {
    column = "name"
    op     = "="
    value  = "/_external-auth-Lw-Prefix"
  }

  breakdowns = ["http.status_code"]

  time_range = 604800
}

resource "honeycombio_query" "oathkeeper_http_code" {
  dataset    = local.ingress_dataset_name
  query_json = data.honeycombio_query_specification.oathkeeper_http_code.json
}

resource "honeycombio_query_annotation" "oathkeeper_http_code" {
  dataset  = local.ingress_dataset_name
  query_id = honeycombio_query.oathkeeper_http_code.id
  name     = "Oathkeeper Status code"
}

resource "honeycombio_board" "ingress" {
  name        = "${local.name_prefix}-ingress"
  description = "Breakdown of ${local.name_prefix}"

  query {
    query_id            = honeycombio_query.nginx_errors.id
    query_annotation_id = honeycombio_query_annotation.nginx_errors.id
    caption             = "Ingress errors"
    query_style         = "graph"
  }

  query {
    query_id            = honeycombio_query.nginx_503_potential_ddos.id
    query_annotation_id = honeycombio_query_annotation.nginx_503_potential_ddos.id
    caption             = "503/DDOS/Rate limit codes"
    query_style         = "table"
  }

  query {
    query_id            = honeycombio_query.status_codes.id
    query_annotation_id = honeycombio_query_annotation.status_codes.id
    caption             = "Status codes"
    query_style         = "graph"
  }

  query {
    query_id            = honeycombio_query.oathkeeper_http_code.id
    query_annotation_id = honeycombio_query_annotation.oathkeeper_http_code.id
    caption             = "Oathkeeper Status codes"
    query_style         = "graph"
  }
}
