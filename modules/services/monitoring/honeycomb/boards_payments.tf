
data "honeycombio_query_specification" "payments_from_username" {
  calculation {
    op = "COUNT"
  }

  filter {
    column = "account.username"
    op     = "exists"
  }

  filter {
    column = "app.payments"
    op     = "="
    value  = "true"
  }

  breakdowns = ["account.username"]

  time_range = 2592000
}

resource "honeycombio_query" "payments_from_username" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.payments_from_username.json
}

resource "honeycombio_query_annotation" "payments_from_username" {
  dataset  = local.api_dataset_name
  query_id = honeycombio_query.payments_from_username.id
  name     = "Payment per username"
}

data "honeycombio_query_specification" "payment_success_rate" {
  calculation {
    op     = "AVG"
    column = "derived.payment.success.lightning"
  }

  calculation {
    op     = "AVG"
    column = "derived.payment.success.onchain"
  }

  calculation {
    op     = "AVG"
    column = "derived.payment.success.intraledger"
  }

  time_range  = 2592000
  granularity = 86400
}

resource "honeycombio_query" "payment_success_rate" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.payment_success_rate.json
}

resource "honeycombio_query_annotation" "payment_success_rate" {
  dataset  = local.api_dataset_name
  query_id = honeycombio_query.payment_success_rate.id
  name     = "Payment success rate"
}

data "honeycombio_query_specification" "lightning_errors" {
  calculation {
    op = "COUNT"
  }

  calculation {
    op     = "COUNT_DISTINCT"
    column = "enduser.id"
  }

  filter {
    column = "derived.payment.success.lightning"
    op     = "="
    value  = "false"
  }

  breakdowns = ["graphql.data.error.0.message"]

  order {
    op    = "COUNT"
    order = "descending"
  }

  time_range = 2592000
}

resource "honeycombio_query" "lightning_errors" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.lightning_errors.json
}

resource "honeycombio_query_annotation" "lightning_errors" {
  dataset  = local.api_dataset_name
  query_id = honeycombio_query.lightning_errors.id
  name     = "Errors from lightning payment"
}

data "honeycombio_query_specification" "onchain_errors" {
  calculation {
    op = "COUNT"
  }

  calculation {
    op     = "COUNT_DISTINCT"
    column = "enduser.id"
  }

  filter {
    column = "derived.payment.success.onchain"
    op     = "="
    value  = "false"
  }

  breakdowns = ["graphql.data.error.0.message"]

  order {
    op    = "COUNT"
    order = "descending"
  }

  time_range = 2592000
}

resource "honeycombio_query" "onchain_errors" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.onchain_errors.json
}

resource "honeycombio_query_annotation" "onchain_errors" {
  dataset  = local.api_dataset_name
  query_id = honeycombio_query.onchain_errors.id
  name     = "Errors from onchain payment"
}

data "honeycombio_query_specification" "intraledger_errors" {
  calculation {
    op = "COUNT"
  }

  calculation {
    op     = "COUNT_DISTINCT"
    column = "enduser.id"
  }

  filter {
    column = "derived.payment.success.intraledger"
    op     = "="
    value  = "false"
  }

  breakdowns = ["graphql.data.error.0.message"]

  order {
    op    = "COUNT"
    order = "descending"
  }

  time_range = 2592000
}

resource "honeycombio_query" "intraledger_errors" {
  dataset    = local.api_dataset_name
  query_json = data.honeycombio_query_specification.intraledger_errors.json
}

resource "honeycombio_query_annotation" "intraledger_errors" {
  dataset  = local.api_dataset_name
  query_id = honeycombio_query.intraledger_errors.id
  name     = "Errors from intraledger payment"
}


resource "honeycombio_board" "payments" {
  name        = "${local.name_prefix}-payments"
  description = "Metrics for ${local.name_prefix} payments"

  query {
    query_id            = honeycombio_query.payments_from_username.id
    query_annotation_id = honeycombio_query_annotation.payments_from_username.id
    caption             = "payments from username"
    query_style         = "table"
  }

  query {
    query_id            = honeycombio_query.payment_success_rate.id
    query_annotation_id = honeycombio_query_annotation.payment_success_rate.id
    caption             = "payments success rate"
    query_style         = "graph"
  }

  query {
    query_id            = honeycombio_query.lightning_errors.id
    query_annotation_id = honeycombio_query_annotation.lightning_errors.id
    caption             = "lightning payment errors"
    query_style         = "table"
  }

  query {
    query_id            = honeycombio_query.onchain_errors.id
    query_annotation_id = honeycombio_query_annotation.onchain_errors.id
    caption             = "onchain payment errors"
    query_style         = "table"
  }

  query {
    query_id            = honeycombio_query.intraledger_errors.id
    query_annotation_id = honeycombio_query_annotation.intraledger_errors.id
    caption             = "intraledger payment errors"
    query_style         = "table"
  }
}
