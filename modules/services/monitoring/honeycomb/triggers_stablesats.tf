data "honeycombio_query_specification" "stablesats_price_errors" {
  calculation {
    op = "COUNT"
  }

  time_range = 300 // 5 minutes

  filter {
    column = "error"
    op     = "="
    value  = true
  }

  filter {
    column = "error.level"
    op     = "="
    value  = "ERROR"
  }

  filter {
    column = "service.name"
    op     = "="
    value  = local.stablesats_price_service_name
  }

  breakdowns = ["name", "error.name", "error.message"]
}

resource "honeycombio_query" "stablesats_price_errors" {
  dataset    = local.stablesats_price_dataset_name
  query_json = data.honeycombio_query_specification.stablesats_price_errors.json
}

resource "honeycombio_trigger" "stablesats_price_errors" {
  name        = "${local.name_prefix}-stablesats-price-errors"
  description = "Alert when the stablesats price server experiences a critical error"
  alert_type  = "on_true"

  query_id = honeycombio_query.stablesats_price_errors.id
  dataset  = local.stablesats_price_dataset_name

  frequency = 300 // 5 minutes

  threshold {
    op    = ">"
    value = 0
  }

  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}

data "honeycombio_query_specification" "stablesats_dealer_errors" {
  calculation {
    op = "COUNT"
  }

  time_range = 1800 // 30 minutes

  filter {
    column = "error"
    op     = "="
    value  = true
  }

  filter {
    column = "error.level"
    op     = "="
    value  = "ERROR"
  }

  filter {
    column = "service.name"
    op     = "="
    value  = local.stablesats_dealer_service_name
  }

  breakdowns = ["name", "error.name", "error.message"]
}

resource "honeycombio_query" "stablesats_dealer_errors" {
  dataset    = local.stablesats_dealer_dataset_name
  query_json = data.honeycombio_query_specification.stablesats_dealer_errors.json
}

resource "honeycombio_trigger" "stablesats_dealer_errors" {
  name        = "${local.name_prefix}-stablesats-dealer-errors"
  description = "Alert when the stablesats dealer server experiences a critical continuous error"
  alert_type  = "on_true"

  query_id = honeycombio_query.stablesats_dealer_errors.id
  dataset  = local.stablesats_dealer_dataset_name

  frequency = 600 // 10 minutes

  threshold {
    op    = ">"
    value = 6
  }

  recipient {
    id = data.honeycombio_recipient.pagerduty.id
  }
}
