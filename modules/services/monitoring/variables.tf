variable "name_prefix" {}
variable "secrets" {
  sensitive = true
}
variable "gcp_project" {}
variable "root_domain" {}
variable "grafana_allowed_oauth_domain" {
  type = list(string)
}
variable "grafana_subdomain" { default = "grafana" }
variable "graphql_subdomain" { default = "api" }

variable "new_honeycomb_environment" {
  default = false
}
variable "pagerduty_alerts" { default = true }

locals {
  name_prefix = var.name_prefix
  root_domain = var.root_domain

  cronjob_service_name    = "${local.name_prefix}-galoy-cronjob"
  trigger_service_name    = "${local.name_prefix}-trigger"
  dealer_service_name     = "${local.name_prefix}-dealer"
  stablesats_service_name = "${local.name_prefix}-stablesats"

  galoy_namespace      = "${local.name_prefix}-galoy"
  bitcoin_namespace    = "${local.name_prefix}-bitcoin"
  ingress_namespace    = "${local.name_prefix}-ingress"
  smoketest_namespace  = "${var.name_prefix}-smoketest"
  monitoring_namespace = "${var.name_prefix}-monitoring"

  dashboard_title           = "${replace(local.name_prefix, "-", " ")} dashboard"
  gcp_project               = var.gcp_project
  slack_api_url             = jsondecode(var.secrets).slack_api_url
  slack_alerts_channel_name = "${local.name_prefix}-alerts"

  pagerduty_api_key           = jsondecode(var.secrets).pagerduty_api_key
  pagerduty_service_name      = local.name_prefix
  pagerduty_integration_email = "${local.name_prefix}-<EMAIL>"
  grafana_dns                 = "${var.grafana_subdomain}.${local.root_domain}"
  graphql_dns                 = "${var.graphql_subdomain}.${local.root_domain}"

  graphql_admin                = "${local.name_prefix}-graphql-admin"
  graphql_errors_metric        = "${local.name_prefix}-graphql-errors"
  grafana_sa_key               = jsondecode(var.secrets).grafana_sa_key
  grafana_sa_project_id        = jsondecode(local.grafana_sa_key)["project_id"]
  grafana_sa_token_uri         = jsondecode(local.grafana_sa_key)["token_uri"]
  grafana_sa_private_key       = jsondecode(local.grafana_sa_key)["private_key"]
  grafana_sa_email             = jsondecode(local.grafana_sa_key)["client_email"]
  grafana_allowed_oauth_domain = var.grafana_allowed_oauth_domain

  graphql_playground_url = "https://${local.graphql_dns}:443/graphql"
  grafana_url            = "https://${local.grafana_dns}"

  oauth_client_id     = jsondecode(var.secrets).oauth_client_id
  oauth_client_secret = jsondecode(var.secrets).oauth_client_secret

  kafka_sa_creds = jsondecode(var.secrets).kafka_sa_creds

  new_honeycomb_environment = var.new_honeycomb_environment

  pagerduty_alert_urgency = var.pagerduty_alerts ? "high" : "low"
}
