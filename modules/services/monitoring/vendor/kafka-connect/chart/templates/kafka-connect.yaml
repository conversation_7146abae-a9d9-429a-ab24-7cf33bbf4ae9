apiVersion: kafka.strimzi.io/v1beta2
kind: KafkaConnect
metadata:
  name: {{ .Values.kafkaConnectInstanceName }}
  annotations:
    strimzi.io/use-connector-resources: "true"
spec:
  version: 3.4.0
  image: "{{ .Values.image.repository }}@{{ .Values.image.digest }}"
  replicas: 1
  bootstrapServers: "kafka-kafka-plain-bootstrap:9092"
  config:
    group.id: connect-cluster
    config.providers: file
    config.providers.file.class: org.apache.kafka.common.config.provider.FileConfigProvider
    offset.storage.topic: connect-cluster-offsets
    config.storage.topic: connect-cluster-configs
    status.storage.topic: connect-cluster-status
    config.storage.replication.factor: -1
    offset.storage.replication.factor: -1
    status.storage.replication.factor: -1
  resources:
    {{ toYaml .Values.resources | nindent 6 }}
{{- if .Values.secretMounts }}
  externalConfiguration:
    volumes:
    {{- range .Values.secretMounts }}
      - name: {{ . }}
        secret:
          secretName: {{ . }}
    {{- end }}
{{- end }}
