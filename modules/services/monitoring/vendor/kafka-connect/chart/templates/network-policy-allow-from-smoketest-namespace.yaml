{{- if .Values.allowedNamespace }}
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: "{{ .Values.kafkaConnectInstanceName }}-connect-allow-from-smoketest-namespace"
  namespace: {{ .Release.Namespace }}
spec:
  podSelector:
    matchLabels:
      strimzi.io/kind: KafkaConnect
      strimzi.io/name: {{ .Values.kafkaConnectInstanceName }}-connect
  policyTypes:
  - Ingress
  ingress:
  - from:
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: {{ .Values.allowedNamespace }}
    ports:
    - protocol: TCP
      port: 8083
{{- end }}
