## The image is built with the Dockerfile in /images/kafka-connect
image:
  repository: us.gcr.io/galoy-org/kafka-connect
  digest: sha256:bd3ab2452b33b02514f7f02fc250edc9e94f4f3d23c26a48dc6ab8d0c61b6191
  git_ref: 45a6987
# Change to allow multiple kafka-connect instances
kafkaConnectInstanceName: kafka
## The pods from this namespace are allowed to access the Kafka Connect API
allowedNamespace: ""
## The contents of the secrets are mounted as a file to be used by the Kafka connectors running in the pod
## mounts to: /opt/kafka/external-configuration/<secretName>/<data.value>
secretMounts: []
resources: {}
