grafana:
  persistence:
    enabled: true
  ingress:
    enabled: false
  resources: {}
  deploymentStrategy:
    type: Recreate
  plugins:
    - https://github.com/doitintl/bigquery-grafana/archive/master.zip;doit-bigquery-datasource
  grafana.ini:
    plugins:
      allow_loading_unsigned_plugins: "doitintl-bigquery-datasource"
  datasources:
    datasources.yaml:
      apiVersion: 1
      datasources:
      - name: Prometheus
        type: prometheus
        url: http://monitoring-prometheus-server
        isDefault: true

prometheus:
  alertmanager:
    enabled: false
  ## kube-state-metrics sub-chart configurable values
  ## Please see https://github.com/helm/charts/tree/master/stable/kube-state-metrics
  ##
  kube-state-metrics:
    podAnnotations:
      prometheus.io/path: /metrics
      prometheus.io/scrape: "true"
      prometheus.io/port: "8080"

  server:
    statefulSet:
      enabled: true
    resources: {}
    retention: "365d"

  prometheus-pushgateway:
    enabled: false

  ## Prometheus server ConfigMap entries
  ##
  serverFiles:
    prometheus.yml:
      rule_files:
        - /etc/config/recording_rules.yml
        - /etc/config/alerting_rules.yml
      ## Below two files are DEPRECATED will be removed from this default values file
        - /etc/config/rules
        - /etc/config/alerts

      scrape_configs:
        - job_name: 'lnd'

          scrape_interval: 300s
          scrape_timeout: 60s

          kubernetes_sd_configs:
            - role: pod

          relabel_configs:
            - source_labels: [__meta_kubernetes_pod_name]
              regex: '.*lnd.*'
              action: keep
            - source_labels: [__meta_kubernetes_pod_label_app_kubernetes_io_instance]
              action: replace
              target_label: pod
            - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
              action: keep
              regex: true
            - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
              action: replace
              target_label: __metrics_path__
              regex: (.+)
            - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
              action: replace
              regex: ([^:]+)(?::\d+)?;(\d+)
              replacement: $1:$2
              target_label: __address__
            - source_labels: [__meta_kubernetes_namespace]
              action: replace
              target_label: namespace

        - job_name: 'kube-state-metrics'

          scrape_interval: 5s
          scrape_timeout: 4s

          kubernetes_sd_configs:
            - role: pod

          relabel_configs:
            - source_labels: [__meta_kubernetes_pod_name]
              regex: 'prometheus-kube-state-metrics-.*'
              action: keep

        - job_name: 'kubernetes-service-endpoints'

          kubernetes_sd_configs:
            - role: endpoints

          relabel_configs:
            - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
              action: keep
              regex: true
            - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
              action: replace
              target_label: __metrics_path__
              regex: (.+)
            - source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
              action: replace
              target_label: __address__
              regex: ([^:]+)(?::\d+)?;(\d+)
              replacement: $1:$2
            - action: labelmap
              regex: __meta_kubernetes_service_label_(.+)
            - source_labels: [__meta_kubernetes_namespace]
              action: replace
              target_label: namespace
            - source_labels: [__meta_kubernetes_service_name]
              action: replace
              target_label: service
            - source_labels: [__meta_kubernetes_pod_node_name]
              action: replace
              target_label: kubernetes_node
            - source_labels: [__meta_kubernetes_pod_name]
              regex: 'prometheus-kube-state-metrics-.*'
              action: drop

        - job_name: 'kubernetes-pods'

          scrape_interval: 10m
          scrape_timeout: 120s

          kubernetes_sd_configs:
            - role: pod

          relabel_configs:
            - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
              action: keep
              regex: true
            - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
              action: replace
              target_label: __metrics_path__
              regex: (.+)
            - source_labels: [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
              action: replace
              regex: ([^:]+)(?::\d+)?;(\d+)
              replacement: $1:$2
              target_label: __address__
            - action: labelmap
              regex: __meta_kubernetes_pod_label_(.+)
            - source_labels: [__meta_kubernetes_namespace]
              action: replace
              target_label: namespace
            - source_labels: [__meta_kubernetes_pod_name]
              action: replace
              target_label: pod
            - source_labels: [__meta_kubernetes_pod_name]
              regex: 'prometheus-kube-state-metrics-.*'
              action: drop
            - source_labels: [__meta_kubernetes_pod_name]
              regex: 'lnd.*'
              action: drop
            - source_labels: [__meta_kubernetes_pod_name]
              regex: 'dealer.*'
              action: drop

  # adds additional scrape configs to prometheus.yml
  # must be a string so you have to add a | after extraScrapeConfigs:
  extraScrapeConfigs: ""
