{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 7, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "lnd_graph_nodes_count{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "num_graph_nodes", "refId": "A"}, {"expr": "lnd_graph_edges_count{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "num_graph_edges", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Public Graph Size", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 8, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "lnd_chain_block_height", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "lnd_graph_chan_capacity_sat{namespace=\"$namespace\",pod=\"$node\"}/1e8", "format": "time_series", "intervalFactor": 1, "legendFormat": "total_chans_btc", "refId": "A"}, {"expr": "lnd_chain_block_height{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "lnd_chain_block_height", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Total Channel Capacity", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "currencyBTC", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"decimals": 0, "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "lnd_graph_nodes_count{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "num_nodes", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [{"alias": "lnd_chain_block_height", "yaxis": 2}], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "lnd_graph_edges_count{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "channel_count", "refId": "A"}, {"expr": "lnd_chain_block_height{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "lnd_chain_block_height", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Channel Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": 0, "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"decimals": 0, "format": "none", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "lnd_graph_max_htlc_msat_avg{namespace=\"$namespace\",pod=\"$node\"} / 1000", "format": "time_series", "intervalFactor": 1, "legendFormat": "max_htlc_avg", "refId": "A"}, {"expr": "lnd_graph_max_htlc_msat_median{namespace=\"$namespace\",pod=\"$node\"} / 1000", "format": "time_series", "intervalFactor": 1, "legendFormat": "max_htlc_median", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Max HTLC ", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "currencyBTC", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "lnd_graph_min_htlc_msat_avg{namespace=\"$namespace\",pod=\"$node\"} / 1000", "format": "time_series", "intervalFactor": 1, "legendFormat": "min_htlc_sat", "refId": "A"}, {"expr": "lnd_graph_min_htlc_msat_median{namespace=\"$namespace\",pod=\"$node\"} / 1000", "format": "time_series", "intervalFactor": 1, "legendFormat": "median_min_htlc", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Min HTLC", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 12, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "lnd_graph_timelock_delta_median{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "time_lock_delta_median{namespace=\"$namespace\",pod=\"$node\"}", "refId": "A"}, {"expr": "lnd_graph_timelock_delta_avg{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "time_lock_delta_avg", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "CLTV Delta", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 10, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "lnd_graph_outdegree_max{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "max_out_degree", "refId": "A"}, {"expr": "lnd_graph_outdegree_avg{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "avg_out_degree", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Node Out Degree", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}, "id": 14, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "(lnd_graph_fee_rate_msat_avg{namespace=\"$namespace\",pod=\"$node\"} / 1000000)", "format": "time_series", "intervalFactor": 1, "legendFormat": "avg_fee_rate", "refId": "A"}, {"expr": "lnd_graph_fee_rate_msat_median{namespace=\"$namespace\",pod=\"$node\"} / 1000000", "format": "time_series", "intervalFactor": 1, "legendFormat": "median_fee_rate", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Average Channel Fee Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "percentunit", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "schemaVersion": 18, "style": "dark", "tags": ["lightning-network"], "templating": {"list": [{"current": {"tags": [], "text": "default", "value": "default"}, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "datasource": "$datasource", "definition": "label_values(namespace)", "hide": 0, "includeAll": false, "label": "namespace", "multi": false, "name": "namespace", "options": [], "query": "label_values(namespace)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "$datasource", "definition": "label_values(lnd_chain_block_timestamp{namespace=\"$namespace\"}, pod)", "hide": 0, "includeAll": false, "label": "node", "multi": false, "name": "node", "options": [], "query": "label_values(lnd_chain_block_timestamp{namespace=\"$namespace\"}, pod)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Network Stats", "uid": "vTkye0NZz", "version": 5}