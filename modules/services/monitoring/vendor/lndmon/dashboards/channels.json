{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 0}, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "lnd_channels_pending_total{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{state}}", "refId": "A"}, {"expr": "lnd_channels_active_total{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "active", "refId": "B"}, {"expr": "lnd_channels_inactive_total{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "inactive", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Channel Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 0}, "id": 2, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "lnd_channels_pending_balance_sat{namespace=\"$namespace\",pod=\"$node\"} > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "pending_open_sat", "refId": "A"}, {"expr": "lnd_channels_open_balance_sat{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "channels_sat", "refId": "B"}, {"expr": "lnd_wallet_balance_unconfirmed_sat{namespace=\"$namespace\",pod=\"$node\"} > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "unconf_balance_sat", "refId": "C"}, {"expr": "lnd_wallet_balance_confirmed_sat{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "conf_balance_sat", "refId": "D"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Total Node Funds", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "currencyBTC", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 9}, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "sum(lnd_channels_bandwidth_outgoing_sat{namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "outgoing_channel_bandwidth_sat", "refId": "A"}, {"expr": "sum(lnd_channels_bandwidth_incoming_sat{namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "incoming_channel_bandwidth", "refId": "B"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Channel Bandwidth", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "currencyBTC", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 9}, "id": 12, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "lnd_channels_pending_htlc_count{namespace=\"$namespace\",pod=\"$node\"} > 0", "format": "time_series", "intervalFactor": 1, "legendFormat": "cid: {{ chan_id }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Pending Channel HTLCs", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 18}, "id": 18, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "topk(5, lnd_channels_sent_sat{namespace=\"$namespace\",pod=\"$node\"} + lnd_channels_received_sat{namespace=\"$namespace\",pod=\"$node\"} > 0)", "format": "time_series", "intervalFactor": 1, "legendFormat": "cid: {{ chan_id }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Most Active Channels (Sat Transit)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "currencyBTC", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 18}, "id": 14, "legend": {"alignAsTable": true, "avg": false, "current": true, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "topk(5, lnd_channels_updates_count{namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "cid: {{ chan_id }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Most Active Channels (Updates)", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Total fees in sats across all active commitments ", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 26}, "id": 16, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(lnd_channels_commit_fee{namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "total_commit_fees", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Total Chan Commit Fees", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 26}, "id": 20, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "max(lnd_channels_fee_per_kw{namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "max_fee_per_kw", "refId": "A"}, {"expr": "min(lnd_channels_fee_per_kw{namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "min_fee_per_kw", "refId": "B"}, {"expr": "avg(lnd_channels_fee_per_kw{namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "avg_fee_per_kw", "refId": "C"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Channel Commitment Fee Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "$datasource", "fill": 1, "gridPos": {"h": 9, "w": 24, "x": 0, "y": 34}, "id": 10, "legend": {"avg": false, "current": false, "hideZero": true, "max": false, "min": false, "rightSide": true, "show": false, "total": false, "values": false}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "lnd_channels_bandwidth_incoming_sat{namespace=\"$namespace\",pod=\"$node\"} > 20000", "format": "time_series", "instant": true, "intervalFactor": 1, "legendFormat": "cid: {{ chan_id }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Incoming Bandwidth by Channel", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "series", "name": null, "show": true, "values": ["total"]}, "yaxes": [{"format": "currencyBTC", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Uptime percentage of remote peer that we have the channel with.", "fill": 1, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 43}, "id": 22, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"expr": "lnd_channel_uptime_percentage{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "cid: {{ chan_id }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Channel Uptime Percentage", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "percentunit", "label": null, "logBase": 1, "max": "1", "min": "0", "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "Prometheus", "fill": 1, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 50}, "id": 8, "legend": {"avg": false, "current": false, "max": false, "min": false, "rightSide": true, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "lnd_channels_bandwidth_outgoing_sat{namespace=\"$namespace\",pod=\"$node\"} > 20000", "format": "time_series", "intervalFactor": 1, "legendFormat": "chan_id: {{ chan_id }}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Outgoing Channel Bandwidth by <PERSON>", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "currencyBTC", "label": null, "logBase": 2, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "Uptime percentage of remote peer that we have the channel with.", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 58}, "id": 26, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "lnd_closed_channels_total{namespace=\"$namespace\",pod=\"$node\"}", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{close_type}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Closed Channels", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "schemaVersion": 18, "style": "dark", "tags": ["lightning-network"], "templating": {"list": [{"current": {"tags": [], "text": "default", "value": "default"}, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "datasource": "$datasource", "definition": "label_values(namespace)", "hide": 0, "includeAll": false, "label": "namespace", "multi": false, "name": "namespace", "options": [], "query": "label_values(namespace)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "$datasource", "definition": "label_values(lnd_chain_block_timestamp{namespace=\"$namespace\"}, pod)", "hide": 0, "includeAll": false, "label": "node", "multi": false, "name": "node", "options": [], "query": "label_values(lnd_chain_block_timestamp{namespace=\"$namespace\"}, pod)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Node State", "uid": "sJUFc-NWk", "version": 4}