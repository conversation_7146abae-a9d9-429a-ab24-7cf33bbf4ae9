{"annotations": {"list": [{"builtIn": 1, "datasource": "-- <PERSON><PERSON> --", "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "editable": true, "gnetId": null, "graphTooltip": 0, "id": 6, "iteration": 1611909070020, "links": [], "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "The rate at which HTLCs for ${HtlcType}s are settled and failed. Note that these values are recorded since lnd's last restart, and are not persisted. ", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "increase(sum by(outcome)(lnd_htlcs_resolved_htlcs{type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})[5m:1s])", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{outcome}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "HTLC Resolution Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "short", "label": "Resolution Rate", "logBase": 1, "max": null, "min": "0", "show": true}, {"decimals": null, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "The percentage of ${HtlcType}s that are successfully settled. Note that this rate is tracked since lnd's last restart, and is not persisted over time. ", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 3, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(lnd_htlcs_resolved_htlcs{outcome=\"settled\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})/ sum(lnd_htlcs_resolved_htlcs{type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "hide": false, "intervalFactor": 1, "legendFormat": "settled", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Success Rate", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"decimals": null, "format": "percentunit", "label": "Success Percentage", "logBase": 1, "max": "1", "min": "0", "show": true}, {"decimals": null, "format": "percentunit", "label": null, "logBase": 1, "max": "1", "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "The number of HTLCs for ${HtlcType}s that each channel has settled as the incoming channel. ", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 6, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "rightSide": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (chan_in)(lnd_htlcs_resolved_htlcs{outcome=\"settled\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{chan_in}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Incoming Channel - Settled", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "series", "name": null, "show": true, "values": ["max"]}, "yaxes": [{"decimals": 0, "format": "short", "label": "HTLC Count", "logBase": 1, "max": null, "min": "0", "show": true}, {"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "The number of HTLCs for ${HtlcType}s that each channel has settled as the outgoing channel. ", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 7, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (chan_out)(lnd_htlcs_resolved_htlcs{outcome=\"settled\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{chan_out}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Outgoing Channel - Settled", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "series", "name": null, "show": true, "values": ["max"]}, "yaxes": [{"decimals": 0, "format": "short", "label": "HTLC Count", "logBase": 1, "max": null, "min": "0", "show": true}, {"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "The number of HTLCs for ${HtlcType}s that each channel has failed as the incoming channel. ", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 8, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (chan_in)(lnd_htlcs_resolved_htlcs{outcome=\"failed\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{chan_in}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Incoming Channel - Failed", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "series", "name": null, "show": true, "values": ["max"]}, "yaxes": [{"decimals": 0, "format": "short", "label": "HTLC Count", "logBase": 1, "max": null, "min": "0", "show": true}, {"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "The number of HTLCs for ${HtlcType}s that each channel has failed as the outgoing channel. ", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 9, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (chan_out)(lnd_htlcs_resolved_htlcs{outcome=\"failed\", type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{chan_out}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Outgoing Channel - Failed", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "series", "name": null, "show": true, "values": ["max"]}, "yaxes": [{"decimals": 0, "format": "short", "label": "HTLC Count", "logBase": 1, "max": null, "min": "0", "show": true}, {"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "A frequency distribution of the time ${HtlcType}s have taken to resolve. Note that this value is not tracked for receives.", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 11, "interval": "", "legend": {"avg": false, "current": false, "max": false, "min": false, "show": false, "total": false, "values": false}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(lnd_htlcs_resolution_time_bucket{le=\"1\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "0-1 seconds", "refId": "A"}, {"expr": "sum(lnd_htlcs_resolution_time_bucket{le=\"10\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"}) - ignoring(le) sum(lnd_htlcs_resolution_time_bucket{le=\"1\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "1-10 seconds", "refId": "B"}, {"expr": "sum(lnd_htlcs_resolution_time_bucket{le=\"60\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"}) - ignoring(le) sum(lnd_htlcs_resolution_time_bucket{le=\"10\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "11-60 seconds", "refId": "C"}, {"expr": "sum(lnd_htlcs_resolution_time_bucket{le=\"120\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"}) - ignoring(le) sum(lnd_htlcs_resolution_time_bucket{le=\"60\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "1-2 minutes", "refId": "D"}, {"expr": "sum(lnd_htlcs_resolution_time_bucket{le=\"300\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"}) - ignoring(le) sum(lnd_htlcs_resolution_time_bucket{le=\"120\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "2-5 minutes", "refId": "E"}, {"expr": "sum(lnd_htlcs_resolution_time_bucket{le=\"600\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"}) - ignoring(le) sum(lnd_htlcs_resolution_time_bucket{le=\"300\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "5-10 minutes", "refId": "F"}, {"expr": "sum(lnd_htlcs_resolution_time_bucket{le=\"3600\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"}) - ignoring(le) sum(lnd_htlcs_resolution_time_bucket{le=\"600\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "10-60 minutes", "refId": "G"}, {"expr": "sum(lnd_htlcs_resolution_time_bucket{le=\"18000\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"}) - ignoring(le) sum(lnd_htlcs_resolution_time_bucket{le=\"3600\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "1-5 hours", "refId": "H"}, {"expr": "sum(lnd_htlcs_resolution_time_bucket{le=\"86400\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"}) - ignoring(le) sum(lnd_htlcs_resolution_time_bucket{le=\"18000\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "5-24 hours", "refId": "I"}, {"expr": "sum(lnd_htlcs_resolution_time_bucket{le=\"604800\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"}) - ignoring(le) sum(lnd_htlcs_resolution_time_bucket{le=\"86400\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "1-7 days", "refId": "J"}, {"expr": "sum(lnd_htlcs_resolution_time_bucket{le=\"++Inf\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"}) - ignoring(le) sum(lnd_htlcs_resolution_time_bucket{le=\"604800\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "> 7 days", "refId": "K"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Htlc Resolution Time", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "series", "name": null, "show": true, "values": ["max"]}, "yaxes": [{"format": "short", "label": "HTLC Count", "logBase": 1, "max": null, "min": null, "show": true}, {"format": "short", "label": null, "logBase": 1, "max": null, "min": null, "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "The average time taken to settle or fail HTLCs for ${HtlcType}s. Note that this value is not tracked for receives.", "fill": 1, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 13, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum(lnd_htlcs_resolution_time_sum{type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})/sum(lnd_htlcs_resolution_time_count{type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "average", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Average HTLC Resolution Time", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "time", "name": null, "show": true, "values": []}, "yaxes": [{"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}, {"format": "s", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}, {"aliasColors": {}, "bars": true, "dashLength": 10, "dashes": false, "datasource": "$datasource", "description": "The reasons provided for htlcs involved in ${HtlcType}s on lnd's channels failing. Note that \"failed_back\" indicates that the payment failed further down the route, and all other failure reasons indicate that your node failed the htlc.", "fill": 1, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 32}, "id": 15, "legend": {"alignAsTable": true, "avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": false, "linewidth": 1, "links": [], "nullPointMode": "null", "options": {}, "percentage": false, "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"expr": "sum by (failure_reason)(lnd_htlcs_resolved_htlcs{outcome=\"failed\",type=\"$HtlcType\",namespace=\"$namespace\",pod=\"$node\"})", "format": "time_series", "intervalFactor": 1, "legendFormat": "{{failure_reason}}", "refId": "A"}], "thresholds": [], "timeFrom": null, "timeRegions": [], "timeShift": null, "title": "Failure Reasons", "tooltip": {"shared": false, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"buckets": null, "mode": "series", "name": null, "show": true, "values": ["max"]}, "yaxes": [{"decimals": 0, "format": "short", "label": "HTLC Count", "logBase": 1, "max": null, "min": "0", "show": true}, {"decimals": 0, "format": "short", "label": null, "logBase": 1, "max": null, "min": "0", "show": true}], "yaxis": {"align": false, "alignLevel": null}}], "refresh": false, "schemaVersion": 18, "style": "dark", "tags": ["lightning-network"], "templating": {"list": [{"current": {"tags": [], "text": "default", "value": "default"}, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "datasource", "options": [], "query": "prometheus", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"allValue": null, "datasource": "$datasource", "definition": "label_values(namespace)", "hide": 0, "includeAll": false, "label": "namespace", "multi": false, "name": "namespace", "options": [], "query": "label_values(namespace)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 5, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "datasource": "$datasource", "definition": "label_values(lnd_chain_block_timestamp{namespace=\"$namespace\"}, pod)", "hide": 0, "includeAll": false, "label": "node", "multi": false, "name": "node", "options": [], "query": "label_values(lnd_chain_block_timestamp{namespace=\"$namespace\"}, pod)", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "tagValuesQuery": "", "tags": [], "tagsQuery": "", "type": "query", "useTags": false}, {"allValue": null, "current": {"tags": [], "text": "forward", "value": "forward"}, "hide": 0, "includeAll": false, "label": null, "multi": false, "name": "HtlcType", "options": [{"selected": true, "text": "forward", "value": "forward"}, {"selected": false, "text": "send", "value": "send"}, {"selected": false, "text": "receive", "value": "receive"}], "query": "forward,send,receive", "skipUrlSync": false, "type": "custom"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"], "time_options": ["5m", "15m", "1h", "6h", "12h", "24h", "2d", "7d", "30d"]}, "timezone": "", "title": "Routing", "uid": "a-7ouFhGz", "version": 2}