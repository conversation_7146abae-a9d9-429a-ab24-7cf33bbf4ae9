resource "google_logging_project_sink" "bitcoind_lnd_logs" {
  name    = "bitcoind-lnd-logs"
  project = local.gcp_project

  # Can export to pubsub, cloud storage, or bigquery
  destination = "logging.googleapis.com/projects/${local.gcp_project}/locations/global/buckets/_Default"

  filter = "resource.type = k8s_container AND resource.labels.pod_name = (lnd2-0 OR lnd1-0 OR bitcoind-0)"

  unique_writer_identity = true
}

resource "google_logging_project_sink" "kratos_logs" {
  name    = "kratos-logs"
  project = local.gcp_project

  # Can export to pubsub, cloud storage, or bigquery
  destination = "logging.googleapis.com/projects/${local.gcp_project}/locations/global/buckets/_Default"

  # TODO: Add kratos courier logs also
  filter = "resource.type = k8s_container AND labels.k8s-pod/app_kubernetes_io/name = kratos"

  unique_writer_identity = true
}

resource "google_logging_project_sink" "kafka_connect_logs" {
  name    = "kafka-connect-logs"
  project = local.gcp_project

  # Can export to pubsub, cloud storage, or bigquery
  destination = "logging.googleapis.com/projects/${local.gcp_project}/locations/global/buckets/_Default"

  filter = "resource.type = k8s_container AND labels.k8s-pod/app_kubernetes_io/name = kafka-connect"

  unique_writer_identity = true
}
