# connector source: https://github.com/confluentinc/kafka-connect-bigquery
# connector specific config options: https://docs.confluent.io/kafka-connectors/bigquery/current/kafka_connect_bigquery_config.html
# general config options: https://docs.confluent.io/kafka-connectors/http/current/connector_config.html

locals {
  topics = [
    "mongodb_galoy_accounts",
    "mongodb_galoy_wallets",
    "mongodb_galoy_medici_balances",
    "mongodb_galoy_medici_journals",
    "mongodb_galoy_medici_transaction_metadatas",
    "mongodb_galoy_medici_transactions",
    "pg_stablesats_public_galoy_transactions",
    "pg_stablesats_public_okex_orders",
    "pg_stablesats_public_okex_transfers",
    "pg_stablesats_public_sqlx_ledger_balances",
    "pg_stablesats_public_user_trades"
  ]
}

resource "kubernetes_manifest" "kafka_sink_bigquery" {
  manifest = {
    "apiVersion" = "kafka.strimzi.io/v1beta2"
    "kind"       = "KafkaConnector"
    "metadata" = {
      "name"      = "kafka-sink-bigquery"
      "namespace" = local.kafka_namespace
      "labels" = {
        "strimzi.io/cluster" = "kafka"
      }
      "annotations" = {}
    }
    spec = {
      class    = "com.wepay.kafka.connect.bigquery.BigQuerySinkConnector"
      tasksMax = 1
      config = {
        "name"                                 = "kafka-sink-bigquery"
        "tasks.max"                            = 1
        "topics"                               = join(",", local.topics)
        "project"                              = "galoy-reporting"
        "datasets"                             = ".*=${local.dataset_id}"
        "keyfile"                              = "/opt/kafka/external-configuration/kafka-sa-creds/keyfile",
        "keySource"                            = "FILE"
        "sanitizeTopics"                       = true
        "sanitizeFieldNames"                   = true
        "autoCreateTables"                     = false
        "allowNewBigQueryFields"               = true
        "allowBigQueryRequiredFieldRelaxation" = true
        "allowSchemaUnionization"              = false
        "upsertEnabled"                        = true
        "deleteEnabled"                        = true
        "bigQueryRetry"                        = 5
        "bigQueryRetryWait"                    = 15000
        "config.action.reload"                 = "restart"
        "timestamp"                            = "UTC"
        "key.converter"                        = "org.apache.kafka.connect.json.JsonConverter"
        "key.converter.schemas.enable"         = true
        "value.converter"                      = "org.apache.kafka.connect.json.JsonConverter"
        "value.converter.schemas.enable"       = true

        "behavior.on.error"                               = "log"
        "errors.log.enable"                               = true
        "errors.log.include.messages"                     = true
        "errors.deadletterqueue.context.headers.enable"   = true
        "errors.deadletterqueue.topic.name"               = "dlq_bigquery_sink"
        "errors.deadletterqueue.topic.replication.factor" = 1
      }
    }
  }
}
