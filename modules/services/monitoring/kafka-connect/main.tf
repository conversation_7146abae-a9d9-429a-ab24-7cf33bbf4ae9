variable "name_prefix" {}
variable "kafka_sa_creds" {
  sensitive = true
}

locals {
  environment         = var.name_prefix
  kafka_namespace     = "${local.environment}-kafka"
  smoketest_namespace = "${local.environment}-smoketest"
  concourse_namespace = "${local.environment}-concourse"
  dataset_id          = replace("${local.environment}_kafka_raw", "-", "_")
  kafka_sa_creds      = var.kafka_sa_creds
}

resource "kubernetes_secret" "kafka_sa_creds" {
  metadata {
    name      = "kafka-sa-creds"
    namespace = local.kafka_namespace
  }

  data = {
    keyfile = local.kafka_sa_creds
  }
}

resource "helm_release" "kafka_connect" {
  name       = "kafka-connect"
  chart      = "${path.module}/../vendor/kafka-connect/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = local.kafka_namespace

  dependency_update = true

  values = [
    templatefile("${path.module}/kafka-connect-values.yml.tmpl", {
      trigger_ref : file("${path.module}/../vendor/kafka-connect/git-ref/ref")
      allowed_namespace : local.concourse_namespace
    }),
    file("${path.module}/../../../../gcp/${local.environment}/monitoring/kafka-connect-scaling.yml")
  ]
}

resource "kubernetes_secret" "kafka_connect_smoketest" {
  metadata {
    name      = "kafka-connect-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    kafka_connect_api_host = "kafka-connect-api.${local.kafka_namespace}.svc.cluster.local"
    kafka_connect_api_port = 8083
  }
}

terraform {
  required_providers {
    kafka = {
      source  = "Mongey/kafka"
      version = "0.5.2"
    }
  }
}
