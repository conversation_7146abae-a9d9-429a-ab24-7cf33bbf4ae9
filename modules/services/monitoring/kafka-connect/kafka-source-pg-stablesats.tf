locals {
  stablesats_namespace = "${var.name_prefix}-stablesats"
  tables = [
    "public.galoy_transactions",
    "public.okex_orders",
    "public.okex_transfers",
    "public.sqlx_ledger_balances",
    "public.user_trades"
  ]
}

data "kubernetes_secret" "stablesats_replicator" {
  metadata {
    name      = "stablesats-replicator"
    namespace = local.stablesats_namespace
  }
}

resource "kafka_topic" "kafka_source_postgres" {
  for_each = toset(local.tables)

  name               = replace("pg_stablesats_${each.value}", ".", "_")
  partitions         = 1
  replication_factor = 3
}

resource "kubernetes_manifest" "kafka-source-postgres" {
  manifest = {
    apiVersion = "kafka.strimzi.io/v1beta2"
    kind       = "KafkaConnector"
    metadata = {
      name      = "kafka-source-postgres"
      namespace = local.kafka_namespace
      labels = {
        "strimzi.io/cluster" = "kafka"
      }
    }
    spec = {
      class    = "io.debezium.connector.postgresql.PostgresConnector"
      tasksMax = 1
      config = {
        "database.hostname" : data.kubernetes_secret.stablesats_replicator.data["pg-host"]
        "database.port" : 5432
        "database.user" : "stablesats-replicator"
        "database.password" : data.kubernetes_secret.stablesats_replicator.data["pg-replicator-password"]
        "database.dbname" : "stablesats"
        "topic.prefix" : "pg_stablesats"
        "topic.delimiter" : "_"
        "table.include.list" : join(",", local.tables)
        "decimal.handling.mode" : "double"
        "transforms" : "unwrap"
        "transforms.unwrap.type" : "io.debezium.transforms.ExtractNewRecordState"
        "transforms.unwrap.drop.tombstones" : "false"
        "transforms.unwrap.delete.handling.mode" : "none"
        "transforms.unwrap.add.source.fields" : "table,lsn"
      }
    }
  }
}
