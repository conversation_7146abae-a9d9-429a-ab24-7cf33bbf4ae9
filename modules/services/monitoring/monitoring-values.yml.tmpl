# trigger_ref: ${trigger_ref}

grafana:
  extraSecretMounts:
  - name: google-oauth-secret-mount
    secretName: google-oauth-secret
    defaultMode: 0440
    mountPath: /etc/secrets/google_oauth
    readOnly: true

  grafana.ini:
    server:
      root_url: "https://${grafana_dns}"
    plugins:
      allow_loading_unsigned_plugins: "doitintl-bigquery-datasource"
    auth.google:
      enabled: true
      client_id: $__file{/etc/secrets/google_oauth/client_id}
      client_secret: $__file{/etc/secrets/google_oauth/client_secret}
      scopes: https://www.googleapis.com/auth/userinfo.profile https://www.googleapis.com/auth/userinfo.email
      auth_url: https://accounts.google.com/o/oauth2/auth
      token_url: https://accounts.google.com/o/oauth2/token
      allowed_domains: ${allowed_oauth_domain}
      allow_sign_up: true

  admin:
    existingSecret: grafana-creds
    userKey: admin-user
    passwordKey: admin-password

  ingress:
    enabled: true
    annotations:
      cert-manager.io/cluster-issuer: letsencrypt-issuer
    ingressClassName: nginx
    hosts:
      - ${grafana_dns}
    tls:
    - hosts:
      - ${grafana_dns}
      secretName: grafana-tls

prometheus:
  server:
    retention: "7d"
