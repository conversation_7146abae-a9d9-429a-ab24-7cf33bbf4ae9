{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 8, "links": [], "liveNow": false, "panels": [{"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "hiddenSeries": false, "id": 38, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "avg by (container) (galoy_bria_hot{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "Bria Hot Wallet", "refId": "G"}, {"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "avg by (container) (galoy_bria_cold_storage{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "Bria Cold Storage", "refId": "H"}, {"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "avg by (container) (galoy_lnd_onchain{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "Onchain", "refId": "C"}, {"datasource": {"type": "prometheus"}, "expr": "avg by (container) (galoy_lnd_offchain{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "Offchain", "refId": "D"}, {"datasource": {"type": "prometheus"}, "expr": "avg by (container) (galoy_lnd_openingchannelbalance{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "Opening Channel Balance", "refId": "E"}, {"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "avg by (container) (galoy_lnd_closingchannelbalance{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "Closing Channel Balance", "refId": "F"}], "thresholds": [], "timeRegions": [], "title": "bria + lnd + bitcoind", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:638", "format": "short", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:639", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fieldConfig": {"defaults": {"unit": "short"}, "overrides": []}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "hiddenSeries": false, "id": 47, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "avg by (container) (galoy_activeChannels{namespace=\"${galoy_namespace}\"})", "hide": false, "interval": "", "legendFormat": "activeChannels", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "avg by (container) (galoy_offlineChannels{namespace=\"${galoy_namespace}\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "offlineChannels", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "avg by (container) (galoy_publicChannels{namespace=\"${galoy_namespace}\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "publicChannels", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "avg by (container) (galoy_privateChannels{namespace=\"${galoy_namespace}\"})", "hide": false, "instant": false, "interval": "", "legendFormat": "privateChannels", "range": true, "refId": "D"}], "thresholds": [], "timeRegions": [], "title": "channels", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:638", "format": "short", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:639", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 12}, "hiddenSeries": false, "id": 6, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "avg by (container) (galoy_totalPendingHtlcCount{namespace=\"${galoy_namespace}\"})", "interval": "", "intervalFactor": 1, "legendFormat": "Total", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "avg by (container) (galoy_incomingPendingHtlcCount{namespace=\"${galoy_namespace}\"})", "interval": "", "intervalFactor": 1, "legendFormat": "Incoming", "range": true, "refId": "B"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "avg by (container) (galoy_outgoingPendingHtlcCount{namespace=\"${galoy_namespace}\"})", "interval": "", "intervalFactor": 1, "legendFormat": "Outgoing", "range": true, "refId": "C"}], "thresholds": [], "timeRegions": [], "title": "Pending HTLC count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:638", "format": "short", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:639", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "hiddenSeries": false, "id": 40, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": false, "expr": "avg by (container) (galoy_inboundBalance{namespace=\"${galoy_namespace}\"})", "hide": false, "interval": "", "legendFormat": "inbound", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "exemplar": true, "expr": "avg by (container) (galoy_lnd_offchain{namespace=\"${galoy_namespace}\"})", "hide": true, "interval": "", "legendFormat": "__auto", "range": true, "refId": "C"}, {"datasource": {"type": "prometheus"}, "editorMode": "code", "expr": "avg by (container) (galoy_outboundBalance{namespace=\"${galoy_namespace}\"})", "hide": false, "legendFormat": "outbound", "range": true, "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "lightning capacity", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:638", "format": "short", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:639", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 20}, "hiddenSeries": false, "id": 4, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "- avg by (container) (galoy_lightning{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "- avg by (container) (galoy_bitcoin{namespace=\"${galoy_namespace}\"})", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Accounting - BTC", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:587", "format": "short", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:588", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "hiddenSeries": false, "id": 10, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg(galoy_assetsEqLiabilities{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "assets eq liabilities", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"format": "short", "logBase": 1, "show": true}, {"format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 28}, "hiddenSeries": false, "id": 36, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg by (container) (galoy_lnd_onchain{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "Onchain", "refId": "C"}, {"datasource": {"type": "prometheus"}, "expr": "avg by (container) (galoy_lnd_offchain{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "Offchain", "refId": "D"}, {"datasource": {"type": "prometheus"}, "expr": "avg by (container) (galoy_lnd_openingchannelbalance{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "Opening Channel Balance", "refId": "E"}, {"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "avg by (container) (galoy_lnd_closingchannelbalance{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "Closing Channel Balance", "refId": "F"}], "thresholds": [], "timeRegions": [], "title": "lnd / hot wallet", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:638", "format": "short", "logBase": 1, "min": "0", "show": true}, {"$$hashKey": "object:639", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 32}, "hiddenSeries": false, "id": 2, "interval": "1m", "legend": {"avg": false, "current": true, "max": false, "min": false, "show": true, "total": false, "values": true}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": true, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "avg by (container) (galoy_userCount{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "users", "refId": "B"}, {"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "avg by (container) (galoy_business{namespace=\"${galoy_namespace}\"})", "hide": false, "interval": "", "legendFormat": "business", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "User Count", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:161", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:162", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 36}, "hiddenSeries": false, "id": 26, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": true, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "- avg(galoy_lightning{namespace=\"${galoy_namespace}\"}) * avg(USD_price{namespace=\"${galoy_namespace}\", label=\"median\"}) / *********", "interval": "", "legendFormat": "", "refId": "B"}, {"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "- avg(galoy_bitcoin{namespace=\"${galoy_namespace}\"}) * avg(USD_price{namespace=\"${galoy_namespace}\", label=\"median\"}) / *********", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "Lightning - USD accounting", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:587", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:588", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "description": "", "fill": 1, "fillGradient": 0, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 38}, "hiddenSeries": false, "id": 25, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "avg(galoy_funder_balance{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "funder", "refId": "B"}, {"datasource": {"type": "prometheus"}, "exemplar": true, "expr": "avg(galoy_bankowner_balance{namespace=\"${galoy_namespace}\"})", "hide": false, "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "roles_balance", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:131", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:132", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 44}, "hiddenSeries": false, "id": 37, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "avg (galoy_lndBalanceSync{namespace=\"${galoy_namespace}\"})", "interval": "", "legendFormat": "", "refId": "A"}], "thresholds": [], "timeRegions": [], "title": "LndBalanceSync", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:250", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:251", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}, {"aliasColors": {}, "bars": false, "dashLength": 10, "dashes": false, "datasource": {"type": "prometheus"}, "fill": 1, "fillGradient": 0, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 52}, "hiddenSeries": false, "id": 27, "legend": {"avg": false, "current": false, "max": false, "min": false, "show": true, "total": false, "values": false}, "lines": true, "linewidth": 1, "nullPointMode": "connected", "options": {"alertThreshold": true}, "percentage": false, "pluginVersion": "9.1.6", "pointradius": 2, "points": false, "renderer": "flot", "seriesOverrides": [], "spaceLength": 10, "stack": false, "steppedLine": false, "targets": [{"datasource": {"type": "prometheus"}, "expr": "USD_price{namespace=\"${galoy_namespace}\"}", "interval": "", "legendFormat": "", "refId": "B"}], "thresholds": [], "timeRegions": [], "title": "SAT/USD", "tooltip": {"shared": true, "sort": 0, "value_type": "individual"}, "type": "graph", "xaxis": {"mode": "time", "show": true, "values": []}, "yaxes": [{"$$hashKey": "object:587", "format": "short", "logBase": 1, "show": true}, {"$$hashKey": "object:588", "format": "short", "logBase": 1, "show": true}], "yaxis": {"align": false}}], "refresh": "", "schemaVersion": 37, "style": "dark", "tags": [], "templating": {"list": []}, "time": {"from": "now-7d", "to": "now"}, "timepicker": {"refresh_intervals": ["10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "${title}", "uid": "s4CeIudMk", "version": 18, "weekStart": ""}