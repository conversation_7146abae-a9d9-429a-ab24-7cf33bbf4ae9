provider "pagerduty" {
  token = local.pagerduty_api_key
}

data "pagerduty_escalation_policy" "default" {
  name = "default"
}

resource "pagerduty_service" "environment" {
  name                    = local.pagerduty_service_name
  acknowledgement_timeout = 1800
  escalation_policy       = data.pagerduty_escalation_policy.default.id
  alert_creation          = "create_alerts_and_incidents"

  incident_urgency_rule {
    type    = "constant"
    urgency = local.pagerduty_alert_urgency
  }
}

data "pagerduty_ruleset" "default" {
  name = "Default Global"
}

resource "pagerduty_ruleset_rule" "environment" {
  ruleset = data.pagerduty_ruleset.default.id
  conditions {
    operator = "and"
    subconditions {
      operator = "contains"
      parameter {
        value = local.name_prefix
        path  = "payload.source"
      }
    }
  }

  actions {
    route {
      value = pagerduty_service.environment.id
    }
  }
}

resource "pagerduty_service_integration" "email_holistics" {
  name              = "Email alerts from holistics"
  type              = "generic_email_inbound_integration"
  integration_email = local.pagerduty_integration_email
  service           = pagerduty_service.environment.id
}
