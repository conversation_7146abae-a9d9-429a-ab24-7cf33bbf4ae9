module "cala_enterprise_pg" {
  source = "../../infra/vendor/tf/postgresql/gcp"

  destroyable = true

  gcp_project             = local.gcp_project
  region                  = local.gcp_region
  vpc_name                = local.vpc_name
  instance_name           = local.cala_enterprise_pg_instance_name
  highly_available        = local.ha_pg
  databases               = local.cala_enterprise_databases
  tier                    = local.pg_tier
  database_version        = "POSTGRES_15"
  enable_detailed_logging = false
}

resource "kubernetes_secret" "cala_enterprise" {
  metadata {
    name      = "cala-enterprise"
    namespace = kubernetes_namespace.cala.metadata[0].name
  }

  data = {
    pg-con : module.cala_enterprise_pg.creds["cala"].conn
  }
}

resource "helm_release" "cala_enterprise" {
  name      = "cala-enterprise"
  chart     = "${path.module}/vendor/cala-enterprise/chart"
  namespace = kubernetes_namespace.cala.metadata[0].name

  values = [
    templatefile("${path.module}/cala-enterprise-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/cala-enterprise/git-ref/ref")
      host : local.cala_enterprise_host
      service_name : local.cala_enterprise_service_name
      otel_exporter_otel_endpoint : local.otel_exporter_otel_endpoint
      cala_internal_ip : local.cala_internal_ip
    })
  ]

  depends_on = [kubernetes_secret.cala_enterprise]

  dependency_update = true
}
