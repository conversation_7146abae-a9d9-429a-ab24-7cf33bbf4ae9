fullnameOverride: "cala-enterprise"
nameOverride: ""
cala:
  ingress:
    enabled: false
  resources: {}
  tracing:
    serviceName: cala-dev
    otelExporterOtlpEndpoint: "http://localhost:4317"
  app:
    jobExecution:
      pollInterval: 20
  db:
    poolSize: 20
  server:
    service:
      type: ClusterIP
      port: 2252
      staticIP: ""
      annotations: {}
  labels: {}
  image:
    repository: gcr.io/galoy-org/cala-enterprise
    digest: "sha256:4f92a69f62963c7ffbc5966681ef7bf6ed54c607e18479e33c3457b572f3264e" # METADATA:: repository=https://github.com/GaloyMoney/cala-enterprise;commit_ref=9428c83;app=cala-enterprise;
  replicas: 2
  annotations:
  secrets:
    create: true
    pgCon: ""
    annotations:
  extraEnvSecrets: {}
  extraEnvs:
    - name: BFX_LOCAL_TESTING
      value: true
  cmd: cala-enterprise
postgresql:
  enabled: true
  auth:
    enablePostgresUser: false
    username: cala
    password: cala
    database: cala
resources: {}
