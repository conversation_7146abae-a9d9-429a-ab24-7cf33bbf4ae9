{{- if .Values.cala.secrets.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "cala.fullname" . }}
  labels:
    app: {{ template "cala.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
  {{- if .Values.cala.secrets.annotations }}
  annotations:
{{ toYaml .Values.cala.secrets.annotations | indent 4 }}
  {{- end }}
type: Opaque
data:
  pg-con: {{ .Values.cala.secrets.pgCon | trim | b64enc | trim }}
{{- end }}
