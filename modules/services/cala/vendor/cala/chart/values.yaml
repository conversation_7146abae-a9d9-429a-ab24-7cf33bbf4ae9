fullnameOverride: ""
nameOverride: ""
cala:
  ingress:
    enabled: false
  resources: {}
  tracing:
    serviceName: cala-dev
    otelExporterOtlpEndpoint: "http://localhost:4317"
  app:
    jobExecution:
      pollInterval: 20
  db:
    poolSize: 20
  server:
    service:
      type: ClusterIP
      port: 2252
      staticIP: ""
      annotations: {}
  labels: {}
  image:
    repository: us.gcr.io/galoy-org/cala
    digest: "sha256:a9eb1a7bbe28ac8f54dad805862dd57ef888da17f077357db7cf45464be2c15b" # METADATA:: repository=https://github.com/GaloyMoney/cala;commit_ref=a783e53;app=cala;
  replicas: 2
  annotations:
  secrets:
    create: true
    pgCon: ""
    annotations:
  extraEnvSecrets: {}
  extraEnvs: {}
  cmd: cala-server
postgresql:
  enabled: true
  auth:
    enablePostgresUser: false
    username: cala
    password: cala
    database: cala
resources: {}
