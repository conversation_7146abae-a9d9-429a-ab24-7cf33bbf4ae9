variable "name_prefix" {}
variable "gcp_project" {}
variable "gcp_region" {}
variable "ha_pg" { default = true }
variable "pg_tier" { default = "db-custom-2-3840" }
variable "root_domain" {}

locals {
  name_prefix = var.name_prefix
  gcp_project = var.gcp_project
  gcp_region  = var.gcp_region
  ha_pg       = var.ha_pg
  pg_tier     = var.pg_tier
  vpc_name    = "${local.name_prefix}-vpc"

  cala_host            = "cala.${var.root_domain}"
  cala_enterprise_host = "cala-enterprise.${var.root_domain}"
  cala_internal_ip     = "********"

  cala_pg_instance_name = "${local.name_prefix}-cala"
  cala_databases        = ["cala"]

  cala_enterprise_pg_instance_name = "${local.name_prefix}-cala-enterprise"
  cala_enterprise_databases        = ["cala"]

  otel_namespace               = "${local.name_prefix}-otel"
  cala_service_name            = "${local.name_prefix}-cala"
  cala_enterprise_service_name = "${local.name_prefix}-cala-enterprise"
  otel_exporter_otel_endpoint  = "opentelemetry-collector.${local.otel_namespace}.svc.cluster.local:4317"
}
