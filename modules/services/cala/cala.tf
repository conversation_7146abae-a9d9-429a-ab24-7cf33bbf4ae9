module "cala_pg" {
  source = "../../infra/vendor/tf/postgresql/gcp"

  destroyable = true

  gcp_project             = local.gcp_project
  region                  = local.gcp_region
  vpc_name                = local.vpc_name
  instance_name           = local.cala_pg_instance_name
  highly_available        = local.ha_pg
  databases               = local.cala_databases
  tier                    = local.pg_tier
  database_version        = "POSTGRES_15"
  enable_detailed_logging = false
}

resource "kubernetes_secret" "cala" {
  metadata {
    name      = "cala"
    namespace = kubernetes_namespace.cala.metadata[0].name
  }

  data = {
    pg-con : module.cala_pg.creds["cala"].conn
  }
}

resource "helm_release" "cala" {
  name      = "cala"
  chart     = "${path.module}/vendor/cala/chart"
  namespace = kubernetes_namespace.cala.metadata[0].name

  values = [
    templatefile("${path.module}/cala-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/cala/git-ref/ref")
      host : local.cala_host
      service_name : local.cala_service_name
      otel_exporter_otel_endpoint : local.otel_exporter_otel_endpoint
    })
  ]

  depends_on = [kubernetes_secret.cala]

  dependency_update = true
}
