# trigger_ref: ${trigger_ref}

cala:
  server:
    service:
      type: LoadBalancer
      staticIP: "${cala_internal_ip}"
      annotations:
        networking.gke.io/load-balancer-type: "Internal"
  ingress:
    enabled: true
    hosts:
    - "${host}"
  secrets:
    create: false
  tracing:
    otelExporterOtlpEndpoint: http://${otel_exporter_otel_endpoint}
    serviceName: ${service_name}
    serviceInstanceId: ${service_name}
  extraEnvSecrets: {}

postgresql:
  enabled: false
