resource "kubernetes_namespace" "kubemonkey" {
  metadata {
    name = local.kubemonkey_namespace
  }
}

resource "helm_release" "kubemonkey" {
  name      = "kubemonkey"
  chart     = "${path.module}/vendor/galoy-deps/chart"
  namespace = kubernetes_namespace.kubemonkey.metadata[0].name

  values = [
    templatefile("${path.module}/kubemonkey-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/galoy-deps/git-ref/ref")
      time_zone : local.kubemonkey_time_zone
      whitelisted_namespaces : local.kubemonkey_whitelisted_namespaces
      notification_url : local.kubemonkey_notification_url
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/galoy-deps/kubemonkey-scaling.yml")
  ]

  dependency_update = true

  depends_on = [
    helm_release.cert_manager
  ]
}
