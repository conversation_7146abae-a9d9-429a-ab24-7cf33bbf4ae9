# trigger_ref: ${trigger_ref}

cert-manager:
  enabled: false
kubemonkey:
  enabled: false
opentelemetry-collector:
  enabled: false
strimzi-kafka-operator:
  enabled: false
ingress-nginx:
  controller:
    config:
      jaeger-service-name: ${service_name}
      jaeger-collector-host: ${jaeger_host}
      use-geoip2: true
      http-snippet: |
        map $geoip2_city_country_code $blocked_country {
          default 0;

%{ for country in unsupported_countries ~}
          ${country} 1;
%{ endfor ~}
        }
      server-snippet: |
        if ($blocked_country) {
          return 403;
        }
    service:
      type: LoadBalancer
    extraInitContainers:
      - name: fetch-geoip-db
        image: gcr.io/google.com/cloudsdktool/cloud-sdk:alpine
        imagePullPolicy: IfNotPresent
        command: ["/bin/sh", "-c"]
        args:
          - |
            #!/bin/sh
            gcloud auth activate-service-account --key-file=/creds.json
            if [ $? -eq 0 ]
            then
              echo "Service account activated successfully."
            else
              echo "Service account activation failed." >&2
              exit 1
            fi

            gsutil -m cp -r gs://geoip-db/* .
            # copy to the mounted volume at /tmp/geoip-city
            cp GeoLite2-City.mmdb /tmp/geoip-city/GeoLite2-City.mmdb
            # copy to the mounted volume at /tmp/geoip-asn
            cp GeoLite2-ASN.mmdb /tmp/geoip-asn/GeoLite2-ASN.mmdb
        volumeMounts:
          - name: geoip-city
            mountPath: /tmp/geoip-city
          - name: geoip-asn
            mountPath: /tmp/geoip-asn
          - name: creds
            mountPath: /creds.json
            subPath: creds.json
    extraVolumes:
      - name: geoip-city
        emptyDir: {}
      - name: geoip-asn
        emptyDir: {}
      - name: creds
        secret:
          secretName: geoip-db-bucket-sa-creds
          items:
          - key: creds.json
            path: creds.json
    extraVolumeMounts:
      - name: geoip-city
        mountPath: /etc/nginx/geoip/GeoLite2-City.mmdb
        subPath: GeoLite2-City.mmdb
      - name: geoip-asn
        mountPath: /etc/nginx/geoip/GeoLite2-ASN.mmdb
        subPath: GeoLite2-ASN.mmdb
