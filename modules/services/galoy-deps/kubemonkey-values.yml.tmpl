# trigger_ref: ${trigger_ref}

strimzi-kafka-operator:
  enabled: false
opentelemetry-collector:
  enabled: false
cert-manager:
  enabled: false
ingress-nginx:
  enabled: false
kubemonkey:
  config:
    dryRun: false
    timeZone: ${time_zone}
    whitelistedNamespaces:
%{ for ns in whitelisted_namespaces ~}
    - ${ns}
%{ endfor ~}
    notifications:
      enabled: true
      attacks: |
        endpoint = "${notification_url}"
        message = '{"text":"Attacked `{$name}` of `{$namespace}` on {$date} at {$time}. {$error}"}'
        headers = ["Content-Type:application/json"]
