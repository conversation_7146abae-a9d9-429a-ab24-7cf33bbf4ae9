resource "kubernetes_namespace" "kafka" {
  count = local.deploy_kafka ? 1 : 0
  metadata {
    name = local.kafka_namespace
  }
}

resource "kubernetes_manifest" "strimzi_kafka_crds" {
  for_each = local.kafka_crds_files
  manifest = yamldecode(file("${path.module}/${each.value}"))
}

resource "helm_release" "kafka" {
  count     = local.deploy_kafka ? 1 : 0
  name      = "kafka"
  chart     = "${path.module}/vendor/galoy-deps/chart"
  namespace = kubernetes_namespace.kafka[0].metadata[0].name

  values = [
    templatefile("${path.module}/kafka-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/galoy-deps/git-ref/ref")
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/galoy-deps/kafka-scaling.yml")
  ]

  dependency_update = true

  skip_crds = true

  depends_on = [
    kubernetes_manifest.strimzi_kafka_crds
  ]
}

resource "kubernetes_role" "smoketest" {
  count = local.deploy_kafka ? 1 : 0
  metadata {
    name      = local.smoketest_name
    namespace = kubernetes_namespace.kafka[0].metadata[0].name
  }

  rule {
    api_groups = [""]
    resources  = ["pods"]
    verbs      = ["get", "list"]
  }
}

resource "kubernetes_role_binding" "smoketest" {
  count = local.deploy_kafka ? 1 : 0
  metadata {
    name      = local.smoketest_name
    namespace = kubernetes_namespace.kafka[0].metadata[0].name
  }

  role_ref {
    api_group = "rbac.authorization.k8s.io"
    kind      = "Role"
    name      = kubernetes_role.smoketest[0].metadata[0].name
  }

  subject {
    kind      = "ServiceAccount"
    name      = local.smoketest_name
    namespace = local.smoketest_namespace
  }
}

resource "kubernetes_secret" "smoketest" {
  count = local.deploy_kafka ? 1 : 0
  metadata {
    name      = "galoy-deps-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    kafka_broker_endpoint = "kafka-kafka-brokers.${local.kafka_namespace}.svc.cluster.local"
    kafka_broker_port     = 9092
    smoketest_kubeconfig  = local.smoketest_kubeconfig
    smoketest_topic       = "smoketest-topic"
    kafka_namespace       = local.kafka_namespace
  }
}
