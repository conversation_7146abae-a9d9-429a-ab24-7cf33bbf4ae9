resource "kubernetes_namespace" "otel" {
  metadata {
    name = local.otel_namespace
  }
}

resource "kubernetes_secret" "honeycomb" {
  metadata {
    name      = "honeycomb-creds"
    namespace = kubernetes_namespace.otel.metadata[0].name
  }
  data = {
    api_key = local.honeycomb_api_key
    dataset = local.honeycomb_dataset
  }
}

resource "helm_release" "otel" {
  name      = "opentelemetry-collector"
  chart     = "${path.module}/vendor/galoy-deps/chart"
  namespace = kubernetes_namespace.otel.metadata[0].name

  values = [
    templatefile("${path.module}/otel-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/galoy-deps/git-ref/ref")
      trace_sample_pct : local.trace_sample_pct
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/galoy-deps/otel-scaling.yml")
  ]

  dependency_update = true

  depends_on = [
    helm_release.kafka
  ]
}
