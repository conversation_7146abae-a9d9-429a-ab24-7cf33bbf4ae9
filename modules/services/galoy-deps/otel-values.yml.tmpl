# trigger_ref: ${trigger_ref}

strimzi-kafka-operator:
  enabled: false
cert-manager:
  enabled: false
kubemonkey:
  enabled: false
ingress-nginx:
  enabled: false
opentelemetry-collector:
  extraEnvs:
  - name: HONEYCOMB_API_KEY
    valueFrom:
      secretKeyRef:
        name: "honeycomb-creds"
        key: "api_key"
  - name: HONEYCOMB_DATASET
    valueFrom:
      secretKeyRef:
        name: "honeycomb-creds"
        key: "dataset"
  - name: K8S_NODE_NAME
    valueFrom:
      fieldRef:
        fieldPath: spec.nodeName
  config:
    processors:
      filter/core:
          error_mode: ignore
          traces:
            span:
            - IsMatch(name, ".*btcPriceList.*") and attributes["error"] == nil
            - IsMatch(name, ".*realtimePrice.*") and attributes["error"] == nil
            - attributes["code.namespace"] == "app.prices" and attributes["error"] == nil
            - attributes["db.system"] == "mongodb" and (end_time - start_time) < Duration("100ms") and attributes["error"] == nil
      filter/bria:
          error_mode: ignore
          traces:
            span:
            - attributes["error.message"] == "Negative balance values detected in wallet summary"
      tail_sampling:
        policies:
        - name: status_code
          type: status_code
          status_code:
            status_codes: [ERROR]
        - name: probabilistic
          type: probabilistic
          probabilistic:
            sampling_percentage: ${trace_sample_pct}
    service:
      pipelines:
        traces:
          processors:
            - filter/ottl
            - filter/core
            - filter/bria
            - memory_limiter
            - resourcedetection
            - attributes
            - k8sattributes
            - tail_sampling
            - batch
