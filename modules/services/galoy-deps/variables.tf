variable "name_prefix" {}

variable "smoketest_kubeconfig" {}

variable "secrets" {
  sensitive = true
}
variable "kubemonkey_time_zone" { default = "Etc/UTC" }
variable "kubemonkey_notification_url" {
  default   = ""
  sensitive = true
}

variable "trace_sample_pct" {
  default = 100
}
variable "letsencrypt_issuer_email" {}

variable "unsupported_countries" {
  default = []
}

variable "deploy_kafka" {
  default = true
}

locals {
  name_prefix = var.name_prefix

  smoketest_kubeconfig = var.smoketest_kubeconfig
  smoketest_name       = "smoketest"
  kafka_namespace      = "${local.name_prefix}-kafka"
  smoketest_namespace  = "${local.name_prefix}-smoketest"
  ingress_namespace    = "${local.name_prefix}-ingress"

  kubemonkey_namespace        = "${local.name_prefix}-kubemonkey"
  kubemonkey_time_zone        = var.kubemonkey_time_zone
  kubemonkey_notification_url = var.kubemonkey_notification_url != "" ? var.kubemonkey_notification_url : jsondecode(var.secrets).kubemonkey_notification_url
  kubemonkey_whitelisted_namespaces = [
    "${var.name_prefix}-galoy",
    "${var.name_prefix}-monitoring",
    "${var.name_prefix}-addons",
    "${var.name_prefix}-stablesats",
  ]

  trace_sample_pct         = var.trace_sample_pct
  otel_namespace           = "${local.name_prefix}-otel"
  geoip_db_bucket_sa_creds = jsondecode(var.secrets).geoip_db_bucket_sa_creds
  honeycomb_api_key        = jsondecode(var.secrets).honeycomb_api_key
  honeycomb_dataset        = local.name_prefix
  unsupported_countries    = var.unsupported_countries

  letsencrypt_issuer_email = var.letsencrypt_issuer_email
  jaeger_host              = "opentelemetry-collector.${local.otel_namespace}.svc.cluster.local"

  deploy_kafka     = var.deploy_kafka
  kafka_crds_files = local.deploy_kafka ? fileset(path.module, "vendor/strimzi-kafka-operator/crds/*.yaml") : []
}
