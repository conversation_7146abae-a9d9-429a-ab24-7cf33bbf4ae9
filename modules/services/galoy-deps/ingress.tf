resource "kubernetes_namespace" "ingress" {
  metadata {
    name = local.ingress_namespace
    labels = {
      type = "ingress-nginx"
    }
  }
}

resource "kubernetes_secret" "geoip_db_bucket_sa_creds" {
  metadata {
    name      = "geoip-db-bucket-sa-creds"
    namespace = local.ingress_namespace
  }
  data = {
    "creds.json" = local.geoip_db_bucket_sa_creds
  }
}

resource "helm_release" "ingress_nginx" {
  namespace = kubernetes_namespace.ingress.metadata[0].name
  name      = "ingress-nginx"
  chart     = "${path.module}/vendor/galoy-deps/chart"

  values = [
    templatefile("${path.module}/ingress-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/galoy-deps/git-ref/ref")
      jaeger_host           = local.jaeger_host
      service_name          = local.ingress_namespace
      unsupported_countries = local.unsupported_countries
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/galoy-deps/ingress-scaling.yml")
  ]

  dependency_update = true

  depends_on = [
    helm_release.otel
  ]
}

resource "helm_release" "cert_manager" {
  namespace = kubernetes_namespace.ingress.metadata[0].name
  name      = "cert-manager"
  chart     = "${path.module}/vendor/galoy-deps/chart"

  values = [
    templatefile("${path.module}/cert-manager-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/galoy-deps/git-ref/ref")
    })
  ]

  depends_on = [
    helm_release.ingress_nginx
  ]

  dependency_update = true
}

resource "kubernetes_manifest" "issuer" {
  manifest = {
    apiVersion = "cert-manager.io/v1"
    kind       = "ClusterIssuer"
    metadata = {
      name = "letsencrypt-issuer"
    }
    spec = {
      acme = {
        server = "https://acme-v02.api.letsencrypt.org/directory"
        email  = local.letsencrypt_issuer_email
        privateKeySecretRef = {
          name = "letsencrypt-issuer"
        }
        solvers = [
          { http01 = { ingress = { class = "nginx" } } }
        ]
      }
    }
  }

  depends_on = [
    helm_release.cert_manager,
  ]
}

