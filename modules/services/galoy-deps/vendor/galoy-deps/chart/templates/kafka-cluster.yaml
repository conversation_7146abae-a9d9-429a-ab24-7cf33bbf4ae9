{{- if (index .Values "strimzi-kafka-operator").enabled -}}
apiVersion: kafka.strimzi.io/v1beta2
kind: Kafka
metadata:
  name: kafka
spec:
  kafka:
    version: 3.6.0
    replicas: 3
    listeners:
      - name: plain
        port: 9092
        type: {{ index .Values "strimzi-kafka-operator" "kafka" "listener" "type" }}
        tls: false
    config:
      # https://github.com/strimzi/strimzi-kafka-operator/blob/main/documentation/api/io.strimzi.api.kafka.model.KafkaClusterSpec.adoc
      # https://github.com/strimzi/strimzi-kafka-operator/blob/main/documentation/modules/managing/con-broker-config-properties.adoc
      auto.create.topics.enable: false
      offsets.topic.replication.factor: 3
      transaction.state.log.replication.factor: 3
      transaction.state.log.min.isr: 1
      default.replication.factor: 3
      min.insync.replicas: 2
      log.retention.hours: 72 # 3 days
      log.segment.bytes: 100000000 # 100 MB
      log.retention.check.interval.ms: 300000 # 5 minutes
    storage:
      type: ephemeral
    resources:
      {{ toYaml (index .Values "strimzi-kafka-operator" "kafka" "resources") | nindent 6 }}
  zookeeper:
    replicas: 3
    storage:
      type: ephemeral
    resources:
      {{ toYaml (index .Values "strimzi-kafka-operator" "zookeeper" "resources") | nindent 6 }}
  entityOperator:
    userOperator: {}
{{- end -}}
