apiVersion: v2
name: galoy-deps
description: A Helm chart for Kubernetes
# A chart can be either an 'application' or a 'library' chart.
#
# Application charts are a collection of templates that can be packaged into versioned archives
# to be deployed.
#
# Library charts provide useful utilities or functions for the chart developer. They're included as
# a dependency of application charts to inject those utilities and functions into the rendering
# pipeline. Library charts do not define any templates and therefore cannot be deployed.
type: application
# This is the chart version. This version number should be incremented each time you make changes
# to the chart and its templates, including the app version.
# Versions are expected to follow Semantic Versioning (https://semver.org/)
version: 0.10.20-dev
# This is the version number of the application being deployed. This version number should be
# incremented each time you make changes to the application. Versions are not expected to
# follow Semantic Versioning. They should reflect the version the application is using.
# It is recommended to use it with quotes.
appVersion: "0.1.0"
dependencies:
  - name: "cert-manager"
    repository: "https://charts.jetstack.io"
    version: v1.17.1
    condition: cert-manager.enabled
  - name: "ingress-nginx"
    repository: "https://kubernetes.github.io/ingress-nginx"
    version: 4.7.1
    condition: ingress-nginx.enabled
  - name: strimzi-kafka-operator
    repository: https://strimzi.io/charts/
    version: 0.39.0
    condition: strimzi-kafka-operator.enabled
  - name: kube-monkey
    alias: kubemonkey
    repository: https://asobti.github.io/kube-monkey/charts/repo
    version: 1.5.2
    condition: kubemonkey.enabled
  - name: opentelemetry-collector
    repository: https://open-telemetry.github.io/opentelemetry-helm-charts
    version: 0.115.0
    condition: opentelemetry-collector.enabled
