apiVersion: apiextensions.k8s.io/v1
kind: CustomResourceDefinition
metadata:
  name: kafkausers.kafka.strimzi.io
  labels:
    app: strimzi
    strimzi.io/crd-install: "true"
    component: kafkausers.kafka.strimzi.io-crd
spec:
  group: kafka.strimzi.io
  names:
    kind: KafkaUser
    listKind: KafkaUserList
    singular: kafkauser
    plural: kafkausers
    shortNames:
      - ku
    categories:
      - strimzi
  scope: Namespaced
  conversion:
    strategy: None
  versions:
    - name: v1beta2
      served: true
      storage: true
      subresources:
        status: {}
      additionalPrinterColumns:
        - name: Cluster
          description: The name of the Kafka cluster this user belongs to
          jsonPath: .metadata.labels.strimzi\.io/cluster
          type: string
        - name: Authentication
          description: How the user is authenticated
          jsonPath: .spec.authentication.type
          type: string
        - name: Authorization
          description: How the user is authorised
          jsonPath: .spec.authorization.type
          type: string
        - name: Ready
          description: The state of the custom resource
          jsonPath: ".status.conditions[?(@.type==\"Ready\")].status"
          type: string
      schema:
        openAPIV3Schema:
          type: object
          properties:
            spec:
              type: object
              properties:
                authentication:
                  type: object
                  properties:
                    password:
                      type: object
                      properties:
                        valueFrom:
                          type: object
                          properties:
                            secretKeyRef:
                              type: object
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              description: Selects a key of a Secret in the resource's namespace.
                          description: Secret from which the password should be read.
                      required:
                        - valueFrom
                      description: "Specify the password for the user. If not set, a new password is generated by the User Operator."
                    type:
                      type: string
                      enum:
                        - tls
                        - tls-external
                        - scram-sha-512
                      description: Authentication type.
                  required:
                    - type
                  description: "Authentication mechanism enabled for this Kafka user. The supported authentication mechanisms are `scram-sha-512`, `tls`, and `tls-external`. \n\n* `scram-sha-512` generates a secret with SASL SCRAM-SHA-512 credentials.\n* `tls` generates a secret with user certificate for mutual TLS authentication.\n* `tls-external` does not generate a user certificate.   But prepares the user for using mutual TLS authentication using a user certificate generated outside the User Operator.\n  ACLs and quotas set for this user are configured in the `CN=<username>` format.\n\nAuthentication is optional. If authentication is not configured, no credentials are generated. ACLs and quotas set for the user are configured in the `<username>` format suitable for SASL authentication."
                authorization:
                  type: object
                  properties:
                    acls:
                      type: array
                      items:
                        type: object
                        properties:
                          host:
                            type: string
                            description: The host from which the action described in the ACL rule is allowed or denied.
                          operation:
                            type: string
                            enum:
                              - Read
                              - Write
                              - Create
                              - Delete
                              - Alter
                              - Describe
                              - ClusterAction
                              - AlterConfigs
                              - DescribeConfigs
                              - IdempotentWrite
                              - All
                            description: "Operation which will be allowed or denied. Supported operations are: Read, Write, Create, Delete, Alter, Describe, ClusterAction, AlterConfigs, DescribeConfigs, IdempotentWrite and All."
                          operations:
                            type: array
                            items:
                              type: string
                              enum:
                                - Read
                                - Write
                                - Create
                                - Delete
                                - Alter
                                - Describe
                                - ClusterAction
                                - AlterConfigs
                                - DescribeConfigs
                                - IdempotentWrite
                                - All
                            description: "List of operations which will be allowed or denied. Supported operations are: Read, Write, Create, Delete, Alter, Describe, ClusterAction, AlterConfigs, DescribeConfigs, IdempotentWrite and All."
                          resource:
                            type: object
                            properties:
                              name:
                                type: string
                                description: Name of resource for which given ACL rule applies. Can be combined with `patternType` field to use prefix pattern.
                              patternType:
                                type: string
                                enum:
                                  - literal
                                  - prefix
                                description: "Describes the pattern used in the resource field. The supported types are `literal` and `prefix`. With `literal` pattern type, the resource field will be used as a definition of a full name. With `prefix` pattern type, the resource name will be used only as a prefix. Default value is `literal`."
                              type:
                                type: string
                                enum:
                                  - topic
                                  - group
                                  - cluster
                                  - transactionalId
                                description: "Resource type. The available resource types are `topic`, `group`, `cluster`, and `transactionalId`."
                            required:
                              - type
                            description: Indicates the resource for which given ACL rule applies.
                          type:
                            type: string
                            enum:
                              - allow
                              - deny
                            description: The type of the rule. Currently the only supported type is `allow`. ACL rules with type `allow` are used to allow user to execute the specified operations. Default value is `allow`.
                        required:
                          - resource
                      description: List of ACL rules which should be applied to this user.
                    type:
                      type: string
                      enum:
                        - simple
                      description: Authorization type. Currently the only supported type is `simple`. `simple` authorization type uses the Kafka Admin API for managing the ACL rules.
                  required:
                    - acls
                    - type
                  description: Authorization rules for this Kafka user.
                quotas:
                  type: object
                  properties:
                    consumerByteRate:
                      type: integer
                      minimum: 0
                      description: A quota on the maximum bytes per-second that each client group can fetch from a broker before the clients in the group are throttled. Defined on a per-broker basis.
                    controllerMutationRate:
                      type: number
                      minimum: 0
                      description: "A quota on the rate at which mutations are accepted for the create topics request, the create partitions request and the delete topics request. The rate is accumulated by the number of partitions created or deleted."
                    producerByteRate:
                      type: integer
                      minimum: 0
                      description: A quota on the maximum bytes per-second that each client group can publish to a broker before the clients in the group are throttled. Defined on a per-broker basis.
                    requestPercentage:
                      type: integer
                      minimum: 0
                      description: A quota on the maximum CPU utilization of each client group as a percentage of network and I/O threads.
                  description: Quotas on requests to control the broker resources used by clients. Network bandwidth and request rate quotas can be enforced.Kafka documentation for Kafka User quotas can be found at http://kafka.apache.org/documentation/#design_quotas.
                template:
                  type: object
                  properties:
                    secret:
                      type: object
                      properties:
                        metadata:
                          type: object
                          properties:
                            labels:
                              x-kubernetes-preserve-unknown-fields: true
                              type: object
                              description: Labels added to the Kubernetes resource.
                            annotations:
                              x-kubernetes-preserve-unknown-fields: true
                              type: object
                              description: Annotations added to the Kubernetes resource.
                          description: Metadata applied to the resource.
                      description: Template for KafkaUser resources. The template allows users to specify how the `Secret` with password or TLS certificates is generated.
                  description: Template to specify how Kafka User `Secrets` are generated.
              description: The specification of the user.
            status:
              type: object
              properties:
                conditions:
                  type: array
                  items:
                    type: object
                    properties:
                      type:
                        type: string
                        description: "The unique identifier of a condition, used to distinguish between other conditions in the resource."
                      status:
                        type: string
                        description: "The status of the condition, either True, False or Unknown."
                      lastTransitionTime:
                        type: string
                        description: "Last time the condition of a type changed from one status to another. The required format is 'yyyy-MM-ddTHH:mm:ssZ', in the UTC time zone."
                      reason:
                        type: string
                        description: The reason for the condition's last transition (a single word in CamelCase).
                      message:
                        type: string
                        description: Human-readable message indicating details about the condition's last transition.
                  description: List of status conditions.
                observedGeneration:
                  type: integer
                  description: The generation of the CRD that was last reconciled by the operator.
                username:
                  type: string
                  description: Username.
                secret:
                  type: string
                  description: The name of `Secret` where the credentials are stored.
              description: The status of the Kafka User.
    - name: v1beta1
      served: true
      storage: false
      subresources:
        status: {}
      additionalPrinterColumns:
        - name: Cluster
          description: The name of the Kafka cluster this user belongs to
          jsonPath: .metadata.labels.strimzi\.io/cluster
          type: string
        - name: Authentication
          description: How the user is authenticated
          jsonPath: .spec.authentication.type
          type: string
        - name: Authorization
          description: How the user is authorised
          jsonPath: .spec.authorization.type
          type: string
        - name: Ready
          description: The state of the custom resource
          jsonPath: ".status.conditions[?(@.type==\"Ready\")].status"
          type: string
      schema:
        openAPIV3Schema:
          type: object
          properties:
            spec:
              type: object
              properties:
                authentication:
                  type: object
                  properties:
                    password:
                      type: object
                      properties:
                        valueFrom:
                          type: object
                          properties:
                            secretKeyRef:
                              type: object
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              description: Selects a key of a Secret in the resource's namespace.
                          description: Secret from which the password should be read.
                      required:
                        - valueFrom
                      description: "Specify the password for the user. If not set, a new password is generated by the User Operator."
                    type:
                      type: string
                      enum:
                        - tls
                        - tls-external
                        - scram-sha-512
                      description: Authentication type.
                  required:
                    - type
                  description: "Authentication mechanism enabled for this Kafka user. The supported authentication mechanisms are `scram-sha-512`, `tls`, and `tls-external`. \n\n* `scram-sha-512` generates a secret with SASL SCRAM-SHA-512 credentials.\n* `tls` generates a secret with user certificate for mutual TLS authentication.\n* `tls-external` does not generate a user certificate.   But prepares the user for using mutual TLS authentication using a user certificate generated outside the User Operator.\n  ACLs and quotas set for this user are configured in the `CN=<username>` format.\n\nAuthentication is optional. If authentication is not configured, no credentials are generated. ACLs and quotas set for the user are configured in the `<username>` format suitable for SASL authentication."
                authorization:
                  type: object
                  properties:
                    acls:
                      type: array
                      items:
                        type: object
                        properties:
                          host:
                            type: string
                            description: The host from which the action described in the ACL rule is allowed or denied.
                          operation:
                            type: string
                            enum:
                              - Read
                              - Write
                              - Create
                              - Delete
                              - Alter
                              - Describe
                              - ClusterAction
                              - AlterConfigs
                              - DescribeConfigs
                              - IdempotentWrite
                              - All
                            description: "Operation which will be allowed or denied. Supported operations are: Read, Write, Create, Delete, Alter, Describe, ClusterAction, AlterConfigs, DescribeConfigs, IdempotentWrite and All."
                          operations:
                            type: array
                            items:
                              type: string
                              enum:
                                - Read
                                - Write
                                - Create
                                - Delete
                                - Alter
                                - Describe
                                - ClusterAction
                                - AlterConfigs
                                - DescribeConfigs
                                - IdempotentWrite
                                - All
                            description: "List of operations which will be allowed or denied. Supported operations are: Read, Write, Create, Delete, Alter, Describe, ClusterAction, AlterConfigs, DescribeConfigs, IdempotentWrite and All."
                          resource:
                            type: object
                            properties:
                              name:
                                type: string
                                description: Name of resource for which given ACL rule applies. Can be combined with `patternType` field to use prefix pattern.
                              patternType:
                                type: string
                                enum:
                                  - literal
                                  - prefix
                                description: "Describes the pattern used in the resource field. The supported types are `literal` and `prefix`. With `literal` pattern type, the resource field will be used as a definition of a full name. With `prefix` pattern type, the resource name will be used only as a prefix. Default value is `literal`."
                              type:
                                type: string
                                enum:
                                  - topic
                                  - group
                                  - cluster
                                  - transactionalId
                                description: "Resource type. The available resource types are `topic`, `group`, `cluster`, and `transactionalId`."
                            required:
                              - type
                            description: Indicates the resource for which given ACL rule applies.
                          type:
                            type: string
                            enum:
                              - allow
                              - deny
                            description: The type of the rule. Currently the only supported type is `allow`. ACL rules with type `allow` are used to allow user to execute the specified operations. Default value is `allow`.
                        required:
                          - resource
                      description: List of ACL rules which should be applied to this user.
                    type:
                      type: string
                      enum:
                        - simple
                      description: Authorization type. Currently the only supported type is `simple`. `simple` authorization type uses the Kafka Admin API for managing the ACL rules.
                  required:
                    - acls
                    - type
                  description: Authorization rules for this Kafka user.
                quotas:
                  type: object
                  properties:
                    consumerByteRate:
                      type: integer
                      minimum: 0
                      description: A quota on the maximum bytes per-second that each client group can fetch from a broker before the clients in the group are throttled. Defined on a per-broker basis.
                    controllerMutationRate:
                      type: number
                      minimum: 0
                      description: "A quota on the rate at which mutations are accepted for the create topics request, the create partitions request and the delete topics request. The rate is accumulated by the number of partitions created or deleted."
                    producerByteRate:
                      type: integer
                      minimum: 0
                      description: A quota on the maximum bytes per-second that each client group can publish to a broker before the clients in the group are throttled. Defined on a per-broker basis.
                    requestPercentage:
                      type: integer
                      minimum: 0
                      description: A quota on the maximum CPU utilization of each client group as a percentage of network and I/O threads.
                  description: Quotas on requests to control the broker resources used by clients. Network bandwidth and request rate quotas can be enforced.Kafka documentation for Kafka User quotas can be found at http://kafka.apache.org/documentation/#design_quotas.
                template:
                  type: object
                  properties:
                    secret:
                      type: object
                      properties:
                        metadata:
                          type: object
                          properties:
                            labels:
                              x-kubernetes-preserve-unknown-fields: true
                              type: object
                              description: Labels added to the Kubernetes resource.
                            annotations:
                              x-kubernetes-preserve-unknown-fields: true
                              type: object
                              description: Annotations added to the Kubernetes resource.
                          description: Metadata applied to the resource.
                      description: Template for KafkaUser resources. The template allows users to specify how the `Secret` with password or TLS certificates is generated.
                  description: Template to specify how Kafka User `Secrets` are generated.
              description: The specification of the user.
            status:
              type: object
              properties:
                conditions:
                  type: array
                  items:
                    type: object
                    properties:
                      type:
                        type: string
                        description: "The unique identifier of a condition, used to distinguish between other conditions in the resource."
                      status:
                        type: string
                        description: "The status of the condition, either True, False or Unknown."
                      lastTransitionTime:
                        type: string
                        description: "Last time the condition of a type changed from one status to another. The required format is 'yyyy-MM-ddTHH:mm:ssZ', in the UTC time zone."
                      reason:
                        type: string
                        description: The reason for the condition's last transition (a single word in CamelCase).
                      message:
                        type: string
                        description: Human-readable message indicating details about the condition's last transition.
                  description: List of status conditions.
                observedGeneration:
                  type: integer
                  description: The generation of the CRD that was last reconciled by the operator.
                username:
                  type: string
                  description: Username.
                secret:
                  type: string
                  description: The name of `Secret` where the credentials are stored.
              description: The status of the Kafka User.
    - name: v1alpha1
      served: true
      storage: false
      subresources:
        status: {}
      additionalPrinterColumns:
        - name: Cluster
          description: The name of the Kafka cluster this user belongs to
          jsonPath: .metadata.labels.strimzi\.io/cluster
          type: string
        - name: Authentication
          description: How the user is authenticated
          jsonPath: .spec.authentication.type
          type: string
        - name: Authorization
          description: How the user is authorised
          jsonPath: .spec.authorization.type
          type: string
        - name: Ready
          description: The state of the custom resource
          jsonPath: ".status.conditions[?(@.type==\"Ready\")].status"
          type: string
      schema:
        openAPIV3Schema:
          type: object
          properties:
            spec:
              type: object
              properties:
                authentication:
                  type: object
                  properties:
                    password:
                      type: object
                      properties:
                        valueFrom:
                          type: object
                          properties:
                            secretKeyRef:
                              type: object
                              properties:
                                key:
                                  type: string
                                name:
                                  type: string
                                optional:
                                  type: boolean
                              description: Selects a key of a Secret in the resource's namespace.
                          description: Secret from which the password should be read.
                      required:
                        - valueFrom
                      description: "Specify the password for the user. If not set, a new password is generated by the User Operator."
                    type:
                      type: string
                      enum:
                        - tls
                        - tls-external
                        - scram-sha-512
                      description: Authentication type.
                  required:
                    - type
                  description: "Authentication mechanism enabled for this Kafka user. The supported authentication mechanisms are `scram-sha-512`, `tls`, and `tls-external`. \n\n* `scram-sha-512` generates a secret with SASL SCRAM-SHA-512 credentials.\n* `tls` generates a secret with user certificate for mutual TLS authentication.\n* `tls-external` does not generate a user certificate.   But prepares the user for using mutual TLS authentication using a user certificate generated outside the User Operator.\n  ACLs and quotas set for this user are configured in the `CN=<username>` format.\n\nAuthentication is optional. If authentication is not configured, no credentials are generated. ACLs and quotas set for the user are configured in the `<username>` format suitable for SASL authentication."
                authorization:
                  type: object
                  properties:
                    acls:
                      type: array
                      items:
                        type: object
                        properties:
                          host:
                            type: string
                            description: The host from which the action described in the ACL rule is allowed or denied.
                          operation:
                            type: string
                            enum:
                              - Read
                              - Write
                              - Create
                              - Delete
                              - Alter
                              - Describe
                              - ClusterAction
                              - AlterConfigs
                              - DescribeConfigs
                              - IdempotentWrite
                              - All
                            description: "Operation which will be allowed or denied. Supported operations are: Read, Write, Create, Delete, Alter, Describe, ClusterAction, AlterConfigs, DescribeConfigs, IdempotentWrite and All."
                          operations:
                            type: array
                            items:
                              type: string
                              enum:
                                - Read
                                - Write
                                - Create
                                - Delete
                                - Alter
                                - Describe
                                - ClusterAction
                                - AlterConfigs
                                - DescribeConfigs
                                - IdempotentWrite
                                - All
                            description: "List of operations which will be allowed or denied. Supported operations are: Read, Write, Create, Delete, Alter, Describe, ClusterAction, AlterConfigs, DescribeConfigs, IdempotentWrite and All."
                          resource:
                            type: object
                            properties:
                              name:
                                type: string
                                description: Name of resource for which given ACL rule applies. Can be combined with `patternType` field to use prefix pattern.
                              patternType:
                                type: string
                                enum:
                                  - literal
                                  - prefix
                                description: "Describes the pattern used in the resource field. The supported types are `literal` and `prefix`. With `literal` pattern type, the resource field will be used as a definition of a full name. With `prefix` pattern type, the resource name will be used only as a prefix. Default value is `literal`."
                              type:
                                type: string
                                enum:
                                  - topic
                                  - group
                                  - cluster
                                  - transactionalId
                                description: "Resource type. The available resource types are `topic`, `group`, `cluster`, and `transactionalId`."
                            required:
                              - type
                            description: Indicates the resource for which given ACL rule applies.
                          type:
                            type: string
                            enum:
                              - allow
                              - deny
                            description: The type of the rule. Currently the only supported type is `allow`. ACL rules with type `allow` are used to allow user to execute the specified operations. Default value is `allow`.
                        required:
                          - resource
                      description: List of ACL rules which should be applied to this user.
                    type:
                      type: string
                      enum:
                        - simple
                      description: Authorization type. Currently the only supported type is `simple`. `simple` authorization type uses the Kafka Admin API for managing the ACL rules.
                  required:
                    - acls
                    - type
                  description: Authorization rules for this Kafka user.
                quotas:
                  type: object
                  properties:
                    consumerByteRate:
                      type: integer
                      minimum: 0
                      description: A quota on the maximum bytes per-second that each client group can fetch from a broker before the clients in the group are throttled. Defined on a per-broker basis.
                    controllerMutationRate:
                      type: number
                      minimum: 0
                      description: "A quota on the rate at which mutations are accepted for the create topics request, the create partitions request and the delete topics request. The rate is accumulated by the number of partitions created or deleted."
                    producerByteRate:
                      type: integer
                      minimum: 0
                      description: A quota on the maximum bytes per-second that each client group can publish to a broker before the clients in the group are throttled. Defined on a per-broker basis.
                    requestPercentage:
                      type: integer
                      minimum: 0
                      description: A quota on the maximum CPU utilization of each client group as a percentage of network and I/O threads.
                  description: Quotas on requests to control the broker resources used by clients. Network bandwidth and request rate quotas can be enforced.Kafka documentation for Kafka User quotas can be found at http://kafka.apache.org/documentation/#design_quotas.
                template:
                  type: object
                  properties:
                    secret:
                      type: object
                      properties:
                        metadata:
                          type: object
                          properties:
                            labels:
                              x-kubernetes-preserve-unknown-fields: true
                              type: object
                              description: Labels added to the Kubernetes resource.
                            annotations:
                              x-kubernetes-preserve-unknown-fields: true
                              type: object
                              description: Annotations added to the Kubernetes resource.
                          description: Metadata applied to the resource.
                      description: Template for KafkaUser resources. The template allows users to specify how the `Secret` with password or TLS certificates is generated.
                  description: Template to specify how Kafka User `Secrets` are generated.
              description: The specification of the user.
            status:
              type: object
              properties:
                conditions:
                  type: array
                  items:
                    type: object
                    properties:
                      type:
                        type: string
                        description: "The unique identifier of a condition, used to distinguish between other conditions in the resource."
                      status:
                        type: string
                        description: "The status of the condition, either True, False or Unknown."
                      lastTransitionTime:
                        type: string
                        description: "Last time the condition of a type changed from one status to another. The required format is 'yyyy-MM-ddTHH:mm:ssZ', in the UTC time zone."
                      reason:
                        type: string
                        description: The reason for the condition's last transition (a single word in CamelCase).
                      message:
                        type: string
                        description: Human-readable message indicating details about the condition's last transition.
                  description: List of status conditions.
                observedGeneration:
                  type: integer
                  description: The generation of the CRD that was last reconciled by the operator.
                username:
                  type: string
                  description: Username.
                secret:
                  type: string
                  description: The name of `Secret` where the credentials are stored.
              description: The status of the Kafka User.
