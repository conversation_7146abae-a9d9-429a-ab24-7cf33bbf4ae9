variable "worker_key" { sensitive = true }
variable "worker_key_pub" {}
variable "host_key_pub" {}
variable "name_prefix" {}

locals {
  name_prefix             = var.name_prefix
  concourse_release_name  = "${local.name_prefix}-concourse"
  worker_tag              = var.name_prefix
  concourse_chart_version = "17.1.1"
  worker_key              = var.worker_key
  worker_key_pub          = var.worker_key_pub
  host_key_pub            = var.host_key_pub
}

resource "kubernetes_namespace" "workers" {
  metadata {
    name = "${local.name_prefix}-concourse"
    labels = {
      type = "concourse-workers"
    }
  }
}

resource "helm_release" "workers" {
  name       = local.concourse_release_name
  repository = "https://concourse-charts.storage.googleapis.com/"
  version    = local.concourse_chart_version
  chart      = "concourse"
  namespace  = kubernetes_namespace.workers.metadata[0].name

  values = [
    templatefile("${path.module}/workers-values.yml.tmpl", {
      worker_tag : local.worker_tag
      concourse_release_name : local.concourse_release_name
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/workers/workers-scaling.yml")
  ]

  depends_on = [
    kubernetes_secret.concourse_worker,
  ]
}

resource "kubernetes_secret" "concourse_worker" {
  metadata {
    name      = "${local.concourse_release_name}-worker"
    namespace = kubernetes_namespace.workers.metadata[0].name
  }
  data = {
    worker-key     = local.worker_key
    worker-key-pub = local.worker_key_pub
    host-key-pub   = local.host_key_pub
  }
}
