resource "random_id" "key_suffix" {
  byte_length = 4
}

resource "google_apikeys_key" "map" {
  name         = "map-${random_id.key_suffix.hex}"
  display_name = "Map API Key"
  project      = local.gcp_project

  restrictions {
    browser_key_restrictions {
      allowed_referrers = local.map_hosts
    }

    api_targets {
      service = "maps-backend.googleapis.com"
    }
  }
}

resource "kubernetes_secret" "map_smoketest" {
  metadata {
    name      = "map-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    map_endpoint = local.map_hosts[0]
    map_port     = 80
  }
}

resource "kubernetes_secret" "map" {
  metadata {
    name      = "map"
    namespace = kubernetes_namespace.addons.metadata[0].name
  }
  data = {
    map-api-key = google_apikeys_key.map.key_string
  }
}

resource "helm_release" "map" {
  name       = "map"
  chart      = "${path.module}/vendor/map/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = kubernetes_namespace.addons.metadata[0].name

  values = [
    templatefile("${path.module}/map-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/map/git-ref/ref")
      hosts : jsonencode(local.map_hosts)
      core_url : local.graphql_url_internal
      otel_exporter_otlp_endpoint : local.otel_exporter_otlp_endpoint
      tracing_service_name : local.map_tracing_service_name
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/addons/map-scaling.yml")
  ]
}

resource "kubernetes_ingress_v1" "maps_redirect" {
  metadata {
    name      = "maps-redirect"
    namespace = local.addons_namespace
    annotations = {
      "cert-manager.io/cluster-issuer"                 = "letsencrypt-issuer"
      "nginx.ingress.kubernetes.io/permanent-redirect" = "https://${element(local.map_hosts, length(local.map_hosts) - 1)}/"
    }
  }

  spec {
    ingress_class_name = "nginx"
    rule {
      host = "maps.${local.tld_domain}"
    }
    rule {
      host = "www.maps.${local.tld_domain}"
    }
    tls {
      hosts       = ["maps.${local.tld_domain}", "www.maps.${local.tld_domain}"]
      secret_name = "maps.${local.tld_domain}-tls"
    }
  }
}
