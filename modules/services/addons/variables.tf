variable "name_prefix" {}
variable "gcp_project" {}
variable "gcp_region" {}
variable "secrets" {
  sensitive = true
}
variable "ha_pg" { default = true }
variable "pg_tier" { default = "db-g1-small" }

variable "legacy_website_domain" { default = "" }
variable "tld_domain" { default = "" }
variable "nip05_domain" { default = "" }

variable "root_domain" {}

variable "admin_panel_subdomain" {
  default = "admin"
}
variable "galoy_api_subdomain" {
  default = "api"
}
variable "map_subdomain" {
  default = "map"
}
variable "galoy_admin_api_subdomain" {
  default = "admin-api"
}
variable "galoy_pay_subdomain" {
  default = "pay"
}
variable "dashboard_subdomain" {
  default = "dashboard"
}
variable "voucher_subdomain" {
  default = "voucher"
}

variable "extra_pay_domains" { default = [] }

variable "extra_map_domains" { default = [] }

variable "bitcoin_network" {}

variable "grafana_subdomain" { default = "grafana" }

variable "graphql_api_port" {
  default = "4002"
}

variable "primary_offchain_lnd" {
  default = "lnd1"

  validation {
    condition     = contains(["lnd1", "lnd2"], var.primary_offchain_lnd)
    error_message = "Allowed values for primary offchain lnd are \"lnd1\" or \"lnd2\""
  }
}
variable "nostr_public_key" { default = "" }

variable "admin_panel_authorized_emails" {}

locals {
  name_prefix          = var.name_prefix
  gcp_project          = var.gcp_project
  addons_namespace     = "${local.name_prefix}-addons"
  smoketest_namespace  = "${local.name_prefix}-smoketest"
  monitoring_namespace = "${local.name_prefix}-monitoring"
  otel_namespace       = "${local.name_prefix}-otel"
  galoy_namespace      = "${local.name_prefix}-galoy"
  bitcoin_namespace    = "${local.name_prefix}-bitcoin"
  primary_offchain_lnd = var.primary_offchain_lnd
  gcp_region           = var.gcp_region
  vpc_name             = "${local.name_prefix}-vpc"
  ha_pg                = var.ha_pg
  pg_tier              = var.pg_tier

  big_query_viewers = ["serviceAccount:<EMAIL>"]
  hydra_public_api  = "https://oauth.${var.tld_domain}"

  admin_panel_host                       = "${var.admin_panel_subdomain}.${var.root_domain}"
  admin_panel_google_oauth_client_id     = jsondecode(var.secrets).admin_panel_google_oauth_client_id
  admin_panel_google_oauth_client_secret = jsondecode(var.secrets).admin_panel_google_oauth_client_secret
  admin_panel_authorized_emails          = var.admin_panel_authorized_emails
  admin_panel_tracing_service_name       = "${local.name_prefix}-admin-panel"

  tld_domain               = var.tld_domain
  nip05_domain             = var.nip05_domain
  galoy_pay_hosts          = concat(["${var.galoy_pay_subdomain}.${var.root_domain}"], var.extra_pay_domains)
  galoy_pay_endpoint       = "https://${var.galoy_pay_subdomain}.${var.root_domain}/"
  pay_domain               = var.root_domain
  pay_tracing_service_name = "${local.name_prefix}-pay"
  nostr_private_key        = jsondecode(var.secrets).nostr_private_key
  nostr_public_key         = var.nostr_public_key
  legacy_website_domain    = var.legacy_website_domain
  websocket_host           = "ws.${var.root_domain}"

  graphql_admin_url_internal = "http://galoy-oathkeeper-proxy.${local.galoy_namespace}.svc.cluster.local:4455/admin/graphql"
  graphql_hostname           = "${var.galoy_api_subdomain}.${var.root_domain}"
  graphql_url                = "https://${local.graphql_hostname}/graphql"
  graphql_url_internal       = "http://galoy-oathkeeper-proxy.${local.galoy_namespace}.svc.cluster.local:4455/graphql"
  graphql_websocket_url      = "wss://${local.websocket_host}/graphql"

  active_wallet = "REMOTE_WALLET_V2"

  bitcoin_network = var.bitcoin_network
  grafana_url     = "https://${var.grafana_subdomain}.${var.root_domain}"

  prometheus_url = "http://monitoring-prometheus-server.${local.monitoring_namespace}.svc.cluster.local"

  otel_exporter_otlp_endpoint = "http://opentelemetry-collector.${local.otel_namespace}.svc.cluster.local:4318"

  api_dashboard_tracing_service_name = "${local.name_prefix}-api-dashboard"
  api_dashboard_hosts                = ["${var.dashboard_subdomain}.${local.tld_domain}", "${var.dashboard_subdomain}.${var.root_domain}"]
  api_dashboard_hydra_redirect_uri   = "https://${local.api_dashboard_hosts[0]}/api/auth/callback/blink"

  map_hosts                = concat(["${var.map_subdomain}.${var.root_domain}"], var.extra_map_domains)
  map_tracing_service_name = "${local.name_prefix}-map"

  voucher_pg_instance_name     = "${local.name_prefix}-voucher"
  voucher_databases            = ["voucher"]
  voucher_escrow_api_key       = jsondecode(var.secrets).voucher_escrow_api_key
  voucher_tracing_service_name = "${local.name_prefix}-voucher"
  voucher_hosts                = ["${var.voucher_subdomain}.${local.tld_domain}", "${var.voucher_subdomain}.${var.root_domain}"]
  voucher_hydra_redirect_uri   = "https://${local.voucher_hosts[0]}/api/auth/callback/blink"
  voucher_platform_fees_in_ppm = 10000
}
