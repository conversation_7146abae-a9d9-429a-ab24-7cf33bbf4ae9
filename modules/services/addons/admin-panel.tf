resource "kubernetes_secret" "admin_panel_smoketest" {
  metadata {
    name      = "admin-panel-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    admin_panel_endpoint = local.admin_panel_host
    admin_panel_port     = 80
  }
}

resource "random_password" "next_auth_secret" {
  length  = 32
  special = false
}

resource "kubernetes_secret" "admin_panel" {
  metadata {
    name      = "admin-panel"
    namespace = kubernetes_namespace.addons.metadata[0].name
  }
  data = {
    google-oauth-client-id     = local.admin_panel_google_oauth_client_id
    google-oauth-client-secret = local.admin_panel_google_oauth_client_secret
    next-auth-secret           = random_password.next_auth_secret.result
  }
}

resource "helm_release" "admin_panel" {
  name       = "admin-panel"
  chart      = "${path.module}/vendor/admin-panel/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = kubernetes_namespace.addons.metadata[0].name

  values = [
    templatefile("${path.module}/admin-panel-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/admin-panel/git-ref/ref")
      host : local.admin_panel_host
      admin_core_api : local.graphql_admin_url_internal
      next_auth_url : "https://${local.admin_panel_host}"
      otel_exporter_otlp_endpoint : local.otel_exporter_otlp_endpoint
      tracing_service_name : local.admin_panel_tracing_service_name
      authorized_emails : join(",", local.admin_panel_authorized_emails)
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/addons/admin-panel-scaling.yml")
  ]
}
