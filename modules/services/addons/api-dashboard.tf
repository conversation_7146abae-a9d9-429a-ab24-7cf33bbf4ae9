resource "hydra_oauth2_client" "api_dashboard" {
  client_name                = "Blink Api Dashboard"
  grant_types                = ["authorization_code"]
  response_types             = ["code", "id_token"]
  token_endpoint_auth_method = "client_secret_basic"
  scopes                     = ["read", "write"]
  redirect_uris              = [local.api_dashboard_hydra_redirect_uri]
  skip_consent               = true
}

resource "kubernetes_secret" "api_dashboard_smoketest" {
  metadata {
    name      = "api-dashboard-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    api_dashboard_endpoint = local.api_dashboard_hosts[0]
    api_dashboard_port     = 80
  }
}

resource "random_password" "api_dashboard_next_auth_secret" {
  length  = 32
  special = false
}

resource "kubernetes_secret" "api_dashboard" {
  metadata {
    name      = "api-dashboard"
    namespace = kubernetes_namespace.addons.metadata[0].name
  }
  data = {
    next-auth-secret = random_password.api_dashboard_next_auth_secret.result
    client-secret    = hydra_oauth2_client.api_dashboard.client_secret
  }
}

resource "helm_release" "api_dashboard" {
  name       = "api-dashboard"
  chart      = "${path.module}/vendor/api-dashboard/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = kubernetes_namespace.addons.metadata[0].name

  values = [
    templatefile("${path.module}/api-dashboard-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/api-dashboard/git-ref/ref")
      hosts : jsonencode(local.api_dashboard_hosts)
      next_auth_url : "https://${local.api_dashboard_hosts[0]}"
      core_api : local.graphql_url_internal
      client_id = hydra_oauth2_client.api_dashboard.client_id
      hydra_public_api : local.hydra_public_api
      otel_exporter_otlp_endpoint : local.otel_exporter_otlp_endpoint
      tracing_service_name : local.api_dashboard_tracing_service_name
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/addons/api-dashboard-scaling.yml")
  ]
}
