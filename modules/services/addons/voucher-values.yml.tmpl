# trigger_ref: ${trigger_ref}

secrets:
  create: false

ingress:
  enabled: true

ingress:
  enabled: true
  hosts: ${hosts}

voucher:
  hydraPublic: ${hydra_public_api}
  coreUrl: ${core_api}
  voucherUrl: ${voucher_url}
  nextAuthUrl: ${next_auth_url}
  clientId: ${client_id}
  otelExporterOtlpEndpoint: ${otel_exporter_otlp_endpoint}
  tracingServiceName: ${tracing_service_name}
  platformFeesInPpm: ${platform_fees_in_ppm}

postgresql:
  enabled: false
