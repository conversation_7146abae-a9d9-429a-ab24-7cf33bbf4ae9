module "voucher_pg" {
  source = "../../infra/vendor/tf/postgresql/gcp"

  gcp_project       = local.gcp_project
  region            = local.gcp_region
  vpc_name          = local.vpc_name
  instance_name     = local.voucher_pg_instance_name
  highly_available  = local.ha_pg
  databases         = local.voucher_databases
  tier              = local.pg_tier
  big_query_viewers = local.big_query_viewers
  database_version  = "POSTGRES_15"
}

resource "hydra_oauth2_client" "voucher" {
  client_name                = "Blink Voucher"
  grant_types                = ["authorization_code"]
  response_types             = ["code", "id_token"]
  token_endpoint_auth_method = "client_secret_basic"
  scopes                     = ["read", "write"]
  redirect_uris              = [local.voucher_hydra_redirect_uri]
  skip_consent               = true
}

resource "kubernetes_secret" "voucher_smoketest" {
  metadata {
    name      = "voucher-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    voucher_endpoint = local.voucher_hosts[0]
    voucher_port     = 80
  }
}

resource "random_password" "voucher_next_auth_secret" {
  length  = 32
  special = false
}

resource "kubernetes_secret" "voucher" {
  metadata {
    name      = "voucher"
    namespace = kubernetes_namespace.addons.metadata[0].name
  }
  data = {
    next-auth-secret = random_password.voucher_next_auth_secret.result
    client-secret    = hydra_oauth2_client.voucher.client_secret
    escrow-api-key   = local.voucher_escrow_api_key
    pg-con           = module.voucher_pg.creds["voucher"].conn
  }
}

resource "helm_release" "voucher" {
  name       = "voucher"
  chart      = "${path.module}/vendor/voucher/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = kubernetes_namespace.addons.metadata[0].name

  values = [
    templatefile("${path.module}/voucher-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/voucher/git-ref/ref")
      hosts : jsonencode(local.voucher_hosts)
      next_auth_url : "https://${local.voucher_hosts[0]}"
      core_api : local.graphql_url
      client_id : hydra_oauth2_client.voucher.client_id
      hydra_public_api : local.hydra_public_api
      otel_exporter_otlp_endpoint : local.otel_exporter_otlp_endpoint
      tracing_service_name : local.voucher_tracing_service_name
      voucher_url : "https://${local.voucher_hosts[0]}"
      platform_fees_in_ppm : local.voucher_platform_fees_in_ppm
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/addons/voucher-scaling.yml")
  ]

  depends_on        = [kubernetes_secret.voucher]
  dependency_update = true
}
