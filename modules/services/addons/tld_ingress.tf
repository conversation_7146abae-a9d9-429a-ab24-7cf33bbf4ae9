resource "kubernetes_ingress_v1" "main" {
  count = var.tld_domain != "" ? 1 : 0

  metadata {
    name      = "tld-ingress-main"
    namespace = local.addons_namespace
    annotations = {
      "cert-manager.io/cluster-issuer" = "letsencrypt-issuer"
    }
  }

  spec {
    ingress_class_name = "nginx"
    rule {
      host = local.tld_domain
    }
    tls {
      hosts       = [local.tld_domain]
      secret_name = "${local.tld_domain}-tls"
    }
  }
}

resource "kubernetes_ingress_v1" "lnurl" {
  count = var.tld_domain != "" ? 1 : 0

  metadata {
    name      = "tld-ingress-lnurl"
    namespace = local.addons_namespace
    annotations = {
      "nginx.ingress.kubernetes.io/enable-cors"            = "true"
      "nginx.ingress.kubernetes.io/cors-allow-methods"     = "GET"
      "nginx.ingress.kubernetes.io/cors-allow-origin"      = "*"
      "nginx.ingress.kubernetes.io/cors-allow-headers"     = "*"
      "nginx.ingress.kubernetes.io/limit-burst-multiplier" = "5"
      "nginx.ingress.kubernetes.io/limit-connections"      = "120"
      "nginx.ingress.kubernetes.io/limit-rpm"              = "60"
    }
  }

  spec {
    ingress_class_name = "nginx"
    rule {
      host = local.tld_domain

      http {
        path {
          path      = "/.well-known/lnurlp"
          path_type = "Prefix"
          backend {
            service {
              name = "galoy-pay"
              port {
                number = 80
              }
            }
          }
        }
      }
    }
  }
}

resource "kubernetes_ingress_v1" "lnurl_callback" {
  count = var.tld_domain != "" ? 1 : 0

  metadata {
    name      = "tld-ingress-lnurl-callback"
    namespace = local.addons_namespace
    annotations = {
      "nginx.ingress.kubernetes.io/enable-cors"            = "true"
      "nginx.ingress.kubernetes.io/cors-allow-methods"     = "GET"
      "nginx.ingress.kubernetes.io/cors-allow-origin"      = "*"
      "nginx.ingress.kubernetes.io/cors-allow-headers"     = "*"
      "nginx.ingress.kubernetes.io/limit-burst-multiplier" = "5"
      "nginx.ingress.kubernetes.io/limit-connections"      = "120"
      "nginx.ingress.kubernetes.io/limit-rpm"              = "60"
    }
  }

  spec {
    ingress_class_name = "nginx"
    rule {
      host = local.tld_domain

      http {
        path {
          path      = "/lnurlp"
          path_type = "Prefix"
          backend {
            service {
              name = "galoy-pay"
              port {
                number = 80
              }
            }
          }
        }
      }
    }
  }
}

resource "kubernetes_service_v1" "nip05" {
  count = local.nip05_domain != "" ? 1 : 0

  metadata {
    name      = "nip05"
    namespace = local.addons_namespace
  }

  spec {
    type          = "ExternalName"
    external_name = var.nip05_domain
  }
}

resource "kubernetes_ingress_v1" "nip05" {
  count = var.nip05_domain != "" ? 1 : 0

  metadata {
    name      = "tld-ingress-nip05"
    namespace = local.addons_namespace
    annotations = {
      "nginx.ingress.kubernetes.io/backend-protocol" = "HTTPS"
      "nginx.ingress.kubernetes.io/upstream-vhost"   = local.nip05_domain
    }
  }

  spec {
    ingress_class_name = "nginx"
    rule {
      host = local.tld_domain
      http {
        path {
          path      = "/.well-known/nostr.json"
          path_type = "Prefix"
          backend {
            service {
              name = kubernetes_service_v1.nip05[0].metadata[0].name
              port {
                number = 443
              }
            }
          }
        }
      }
    }
  }
}

resource "kubernetes_service_v1" "website" {
  count = var.tld_domain != "" ? 1 : 0

  metadata {
    name      = "website"
    namespace = local.addons_namespace
  }

  spec {
    type          = "ExternalName"
    external_name = "www.${var.tld_domain}"
  }
}

resource "kubernetes_ingress_v1" "website" {
  count = var.tld_domain != "" ? 1 : 0

  metadata {
    name      = "tld-ingress-website"
    namespace = local.addons_namespace
    annotations = {
      "nginx.ingress.kubernetes.io/permanent-redirect" = "https://www.${var.tld_domain}$request_uri"
    }
  }

  spec {
    ingress_class_name = "nginx"
    rule {
      host = local.tld_domain
      http {
        path {
          path      = "/"
          path_type = "Prefix"
          backend {
            service {
              name = kubernetes_service_v1.website[0].metadata[0].name
              port {
                number = 443
              }
            }
          }
        }
      }
    }
  }
}

resource "kubernetes_ingress_v1" "website_app_store_redirect" {
  metadata {
    name      = "website-app-store-redirect"
    namespace = local.addons_namespace
    annotations = {
      "cert-manager.io/cluster-issuer"                 = "letsencrypt-issuer"
      "nginx.ingress.kubernetes.io/permanent-redirect" = "https://www.blink.sv/get"
    }
  }

  spec {
    ingress_class_name = "nginx"
    rule {
      host = "get.${local.tld_domain}"
    }
    rule {
      host = "www.get.${local.tld_domain}"
    }
    tls {
      hosts       = ["get.${local.tld_domain}", "www.get.${local.tld_domain}"]
      secret_name = "get.${local.tld_domain}-tls"
    }
  }
}
