{{- if .Values.secrets.create -}}
apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.redis.auth.existingSecret }}
  labels:
    app: {{ template "galoyNostr.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  {{ .Values.redis.auth.existingSecretPasswordKey }}: {{ .Values.secrets.redisPassword | toString | b64enc }}

---

apiVersion: v1
kind: Secret
metadata:
  name: {{ .Values.nostr.existingSecret }}
  labels:
    app: {{ template "galoyNostr.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  key: {{ .Values.secrets.nostrPrivateKey | toString | b64enc }}

{{- end }}
