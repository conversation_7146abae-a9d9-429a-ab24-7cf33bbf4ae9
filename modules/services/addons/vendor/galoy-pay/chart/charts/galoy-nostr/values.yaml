image:
  repository: krtk6160/galoy-nostr
  digest: "sha256:cc82a694f81870c0becdc1b243ea9d4fca8b4ce497e70f4733d7b0539b462f19"
  git_ref: "01c8066" # Not used by helm
ingress:
  enabled: false
lnd1:
  dns: "lnd1.galoy-dev-bitcoin.svc.cluster.local"
redis:
  ## Redis replica config params
  redis0Dns: "galoy-redis-node-0.galoy-redis-headless"
  redis1Dns: "galoy-redis-node-1.galoy-redis-headless"
  redis2Dns: "galoy-redis-node-2.galoy-redis-headless"
  auth:
    existingSecret: galoy-redis-pw
    existingSecretPasswordKey: redis-password
nostr:
  existingSecret: galoy-nostr-private-key
secrets:
  create: false
  redisPassword: password
  nostrPrivateKey: private-key
