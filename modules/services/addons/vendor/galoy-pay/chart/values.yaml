image:
  repository: us.gcr.io/galoy-org/galoy-pay
  digest: "sha256:606c74a9da5925f62d961075bd2b5a140d1b45616dc7b42554f37ab718f85233" # METADATA:: repository=https://github.com/blinkbitcoin/blink;commit_ref=41666a5;app=pay;monorepo_subdir=apps/pay;
ingress:
  enabled: false
service:
  port: 80
  type: ClusterIP
payUrl: https://pay.domain.com
payDomain: domain.com
coreGqlUrlIntranet: http://service-name.namespace.svc.cluster.local
nostrPubkey: "pubkey"
clientId: "dummyValue"
hydraPublicUrl: "http://galoy-hydra-public.galoy-dev-galoy.svc.cluster.local:4444"
nextAuthUrl: "https://pay.domain.com"
otelExporterOtlpEndpoint: http://localhost:4318
tracingServiceName: "pay"
secrets:
  create: true
  nextAuthSecret: "dummy"
  clientSecret: "dummy"
galoy-nostr:
  enabled: true
  fullnameOverride: galoy-nostr
  image:
    repository: us.gcr.io/galoy-org/blink-nostr
    digest: "sha256:f2b642e234fdb5ebe2d4492aab370c12e6088291e82ac425ce77fa9489afbc15"
redis:
  redis0Dns: "galoy-redis-node-0.galoy-redis-headless"
  redis1Dns: "galoy-redis-node-1.galoy-redis-headless"
  redis2Dns: "galoy-redis-node-2.galoy-redis-headless"
  auth:
    existingSecret: galoy-redis-pw
    existingSecretPasswordKey: redis-password
resources: {}
