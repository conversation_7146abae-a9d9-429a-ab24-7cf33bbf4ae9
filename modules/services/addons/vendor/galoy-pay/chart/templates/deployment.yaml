apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "galoyPay.fullname" . }}
  labels:
    app: {{ template "galoyPay.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    kube-monkey/identifier: {{ template "galoyPay.fullname" . }}
    kube-monkey/enabled: enabled
    kube-monkey/kill-mode: fixed
    kube-monkey/kill-value: "1"
    kube-monkey/mtbf: "3"
spec:
  selector:
    matchLabels:
      app: {{ template "galoyPay.fullname" . }}
      release: {{ .Release.Name }}
  replicas: 1
  template:
    metadata:
      labels:
        app: {{ template "galoyPay.fullname" . }}
        release: "{{ .Release.Name }}"
        kube-monkey/identifier: {{ template "galoyPay.fullname" . }}
        kube-monkey/enabled: enabled
{{- with .Values.labels }}
{{ toYaml . | trim | indent 8 }}
{{- end }}
    spec:
      containers:
      - name: pay
        image: "{{ .Values.image.repository }}@{{ .Values.image.digest }}"
        ports:
        - containerPort: 3000
        env:
        - name: PAY_URL
          value: "{{ .Values.payUrl }}"
        - name: PAY_DOMAIN
          value: "{{ .Values.payDomain }}"
        - name: CORE_GQL_URL_INTRANET
          value: "{{ .Values.coreGqlUrlIntranet }}"
        - name: CLIENT_ID
          value: {{ .Values.clientId }}
        - name: CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ template "galoyPay.fullname" . }}
              key: "client-secret"
        - name: HYDRA_PUBLIC
          value: {{ .Values.hydraPublicUrl }}
        - name: NEXTAUTH_URL
          value: {{ .Values.nextAuthUrl }}
        - name: NEXTAUTH_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ template "galoyPay.fullname" . }}
              key: "next-auth-secret"
        - name: NOSTR_PUBKEY
          value: "{{ .Values.nostrPubkey }}"
        - name: REDIS_MASTER_NAME
          value: "mymaster"
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: galoy-redis-pw
              key: "redis-password"
        - name: REDIS_0_DNS
          value: {{.Values.redis.redis0Dns}}
        - name: REDIS_1_DNS
          value: {{.Values.redis.redis1Dns}}
        - name: REDIS_2_DNS
          value: {{.Values.redis.redis2Dns}}
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: {{ .Values.otelExporterOtlpEndpoint }}
        - name: TRACING_SERVICE_NAME
          value: {{ .Values.tracingServiceName }}
        resources:
          {{ toYaml .Values.resources | nindent 10 }}
