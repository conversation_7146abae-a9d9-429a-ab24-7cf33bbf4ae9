{{- if .Values.secrets.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "adminPanel.fullname" . }}
  labels:
    app: {{ template "adminPanel.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  google-oauth-client-id: {{ .Values.secrets.googleOauthClientId | b64enc | quote }}
  google-oauth-client-secret: {{ .Values.secrets.googleOauthClientSecret | b64enc | quote }}
  next-auth-secret: {{ .Values.secrets.nextAuthSecret | b64enc | quote }}
{{- end }}
