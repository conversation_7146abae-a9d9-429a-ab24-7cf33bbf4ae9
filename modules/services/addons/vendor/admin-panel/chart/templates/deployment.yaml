apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "adminPanel.fullname" . }}
  labels:
    app: {{ template "adminPanel.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    kube-monkey/identifier: {{ template "adminPanel.fullname" . }}
    kube-monkey/enabled: enabled
    kube-monkey/kill-mode: fixed
    kube-monkey/kill-value: "1"
    kube-monkey/mtbf: "3"
spec:
  selector:
    matchLabels:
      app: {{ template "adminPanel.fullname" . }}
      release: {{ .Release.Name }}
  replicas: 1
  template:
    metadata:
      labels:
        app: {{ template "adminPanel.fullname" . }}
        release: "{{ .Release.Name }}"
        kube-monkey/identifier: {{ template "adminPanel.fullname" . }}
        kube-monkey/enabled: enabled
{{- with .Values.labels }}
{{ toYaml . | trim | indent 8 }}
{{- end }}
    spec:
      containers:
      - name: admin-panel
        image: "{{ .Values.image.repository }}@{{ .Values.image.digest }}"
        ports:
        - containerPort: {{ .Values.service.port }}
        env:
        - name: GOOGLE_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: {{ template "adminPanel.fullname" . }}
              key: google-oauth-client-id
        - name: GOOGLE_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ template "adminPanel.fullname" . }}
              key: google-oauth-client-secret
        - name: NEXTAUTH_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ template "adminPanel.fullname" . }}
              key: next-auth-secret
        - name: AUTHORIZED_EMAILS
          value: {{ .Values.adminPanel.authorizedEmails }}
        - name: NEXTAUTH_URL
          value: {{ .Values.adminPanel.nextAuthUrl }}
        - name: ADMIN_CORE_API
          value: {{ .Values.adminPanel.adminCoreApi }}
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: {{ .Values.otelExporterOtlpEndpoint }}
        - name: TRACING_SERVICE_NAME
          value: {{ .Values.tracingServiceName }}
        resources:
          {{ toYaml .Values.resources | nindent 10 }}
