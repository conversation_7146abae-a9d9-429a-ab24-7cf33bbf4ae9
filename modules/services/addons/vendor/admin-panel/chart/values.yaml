image:
  repository: us.gcr.io/galoy-org/galoy-admin-panel
  digest: "sha256:a88c31a1e9d17fc9bafc79ef7eb8b171657a46a5d3f70b1840a6a5185ed18f9c" # METADATA:: repository=https://github.com/blinkbitcoin/blink;commit_ref=d5c8414;app=admin-panel;monorepo_subdir=apps/admin-panel;
  git_ref: "ef99dc0" # Not used by helm
ingress:
  enabled: false
service:
  port: 3000
  type: ClusterIP
adminPanel:
  adminCoreApi: http://admin-api.galoy-dev-galoy.svc.cluster.local:4001/admin/graphql
  nextAuthUrl: http://localhost:3000
  authorizedEmails: "<EMAIL>,<EMAIL>"
resources: {}
secrets:
  create: true
  googleOauthClientId: ""
  googleOauthClientSecret: ""
  nextAuthSecret: ""
otelExporterOtlpEndpoint: http://localhost:4318
tracingServiceName: "admin-panel"
