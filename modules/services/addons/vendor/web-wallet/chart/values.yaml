image:
  repository: us.gcr.io/galoy-org/web-wallet
  digest: "sha256:53a370c83f0f16868e41a3029be222f8c8624b5e6482182bd4b097cf25ef9674"
  git_ref: "2b7d532" # Not used by helm
mobileLayout:
  enabled: false
  image:
    repository: us.gcr.io/galoy-org/web-wallet-mobile-layout
    digest: "sha256:53a370c83f0f16868e41a3029be222f8c8624b5e6482182bd4b097cf25ef9674"
ingress:
  enabled: false
service:
  port: 80
  type: ClusterIP
webWallet:
  jaegerHost: localhost
  tracingPrefix: "galoy"
  bitcoinNetwork: regtest
  authEndpoint: api/login
  graphqlUrl: http://localhost:4002/graphql
  graphqlSubscriptionUrl: ws://localhost:4002/graphql
  supportEmail: <EMAIL>
  walletName: galoy
  galoyPayEndpoint: http://localhost:4000
  galoyAuthEndpoint: http://localhost:4002/auth
  kratos:
    enabled: false
    browserUrl: http://localhost:4433
    apiUrl: http://localhost:4433
secrets:
  ## Create the secret resource from the following values. Set this to
  ## false to manage these secrets outside Helm.
  ## Checkout templates/secrets.yaml to get the appropriate template
  ## Set this to true and set secrets.sessionKeys to the
  ## value of the session key to have a secret be created
  create: false
  ## cookieSession keys
  sessionKeys: "session-keys"
resources: {}
