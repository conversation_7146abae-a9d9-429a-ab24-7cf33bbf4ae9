apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "webWallet.fullname" . }}
  labels:
    app: {{ template "webWallet.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
spec:
  selector:
    matchLabels:
      app: {{ template "webWallet.fullname" . }}
      release: {{ .Release.Name }}
  replicas: 1
  template:
    metadata:
      labels:
        app: {{ template "webWallet.fullname" . }}
        release: "{{ .Release.Name }}"
{{- with .Values.labels }}
{{ toYaml . | trim | indent 8 }}
{{- end }}
    spec:
      containers:
      - name: web-wallet
      {{- if eq .Values.mobileLayout.enabled true }}
        image: "{{ .Values.mobileLayout.image.repository }}@{{ .Values.mobileLayout.image.digest }}"
      {{- else }}
        image: "{{ .Values.image.repository }}@{{ .Values.image.digest }}"
      {{- end }}
        ports:
        - containerPort: 3000
        env:
        - name: NODE_ENV
          value: production
        - name: JAEGER_HOST
          value: {{ $.Values.webWallet.jaegerHost }}
        - name: TRACING_SERVICE_NAME
          value: {{ $.Values.webWallet.tracingPrefix }}-web-wallet
        - name: SESSION_KEYS
          valueFrom:
            secretKeyRef:
              name: {{ template "webWallet.fullname" . }}
              key: session-keys
        - name: HOST
          value: 0.0.0.0
        - name: PORT
          value: "3000"
        - name: NETWORK
          value: {{ .Values.webWallet.bitcoinNetwork }}
        - name: SUPPORT_EMAIL
          value: {{ .Values.webWallet.supportEmail }}
        - name: GRAPHQL_URL
          value: {{ .Values.webWallet.graphqlUrl }}
        - name: GRAPHQL_SUBSCRIPTION_URL
          value: {{ .Values.webWallet.graphqlSubscriptionUrl }}
        - name: AUTH_ENDPOINT
          value: {{ .Values.webWallet.authEndpoint }}
        - name: WALLET_NAME
          value: {{ .Values.webWallet.walletName }}
        - name: SHARE_URL
          value: {{ .Values.webWallet.galoyPayEndpoint }}
        - name: GALOY_AUTH_ENDPOINT
          value: {{ .Values.webWallet.galoyAuthEndpoint }}
        {{- if eq .Values.webWallet.kratos.enabled true }}
        - name: KRATOS_API_URL
          value: {{ .Values.webWallet.kratos.apiUrl }}
        - name: KRATOS_BROWSER_URL
          value: {{ .Values.webWallet.kratos.browserUrl }}
        - name: KRATOS_FEATURE_FLAG
          value: "true"
        {{- end }}
        resources:
          {{ toYaml .Values.resources | nindent 10 }}
{{- if .Values.webWallet.additionalEnvVars }}
{{ toYaml .Values.webWallet.additionalEnvVars | indent 8 }}
{{- end }}
