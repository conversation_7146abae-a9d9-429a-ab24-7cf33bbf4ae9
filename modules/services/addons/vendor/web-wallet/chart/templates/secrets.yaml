{{- if .Values.secrets.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "webWallet.fullname" . }}
  labels:
    app: {{ template "webWallet.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
  {{- if .Values.secrets.annotations }}
  annotations:
{{ toYaml .Values.secrets.annotations | indent 4 }}
  {{- end }}
type: Opaque
data:
  session-keys: {{ required "secrets.sessionKeys needs to be set when secrets.create=true" .Values.secrets.sessionKeys | b64enc | quote }}
{{- end }}
