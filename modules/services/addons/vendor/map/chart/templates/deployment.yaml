apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "map.fullname" . }}
  labels:
    app: {{ template "map.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    kube-monkey/identifier: {{ template "map.fullname" . }}
    kube-monkey/enabled: enabled
    kube-monkey/kill-mode: fixed
    kube-monkey/kill-value: "1"
    kube-monkey/mtbf: "3"
spec:
  selector:
    matchLabels:
      app: {{ template "map.fullname" . }}
      release: {{ .Release.Name }}
  replicas: 1
  template:
    metadata:
      labels:
        app: {{ template "map.fullname" . }}
        release: "{{ .Release.Name }}"
        kube-monkey/identifier: {{ template "map.fullname" . }}
        kube-monkey/enabled: enabled
{{- with .Values.labels }}
{{ toYaml . | trim | indent 8 }}
{{- end }}
    spec:
      containers:
      - name: map
        image: "{{ .Values.image.repository }}@{{ .Values.image.digest }}"
        ports:
        - containerPort: {{ .Values.service.port }}
        env:
        - name: CORE_URL
          value: {{ .Values.map.coreUrl }}
        - name: NEXT_PUBLIC_MAP_API_KEY
          valueFrom:
            secretKeyRef:
              name: {{ template "map.fullname" . }}
              key: "map-api-key"
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: {{ .Values.map.otelExporterOtlpEndpoint }}
        - name: TRACING_SERVICE_NAME
          value: {{ .Values.map.tracingServiceName }}
        resources:
          {{ toYaml .Values.resources | nindent 10 }}
