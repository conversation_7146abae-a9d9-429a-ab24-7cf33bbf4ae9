apiVersion: v1
kind: Service
metadata:
  name: {{ include "map.fullname" . }}
  labels:
    app: {{ template "map.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.port }}
      protocol: TCP
      name: http
  selector:
    app: {{ template "map.fullname" . }}
