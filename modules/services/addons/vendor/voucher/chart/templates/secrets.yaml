{{- if .Values.secrets.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "voucher.fullname" . }}
  labels:
    app: {{ template "voucher.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  next-auth-secret: {{ .Values.secrets.nextAuthSecret | b64enc | quote }}
  client-secret: {{ .Values.secrets.clientSecret | b64enc | quote }}
  escrow-api-key: {{ .Values.secrets.escrowApiKey | b64enc | quote }}
  pg-con: {{ .Values.secrets.pgCon | b64enc | quote }}
{{- end }}
