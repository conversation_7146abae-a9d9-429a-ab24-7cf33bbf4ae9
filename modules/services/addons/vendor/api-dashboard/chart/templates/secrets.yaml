{{- if .Values.secrets.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "apiDashboard.fullname" . }}
  labels:
    app: {{ template "apiDashboard.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
type: Opaque
data:
  next-auth-secret: {{ .Values.secrets.nextAuthSecret }}
  client-secret: {{ .Values.secrets.clientSecret }}
{{- end }}
