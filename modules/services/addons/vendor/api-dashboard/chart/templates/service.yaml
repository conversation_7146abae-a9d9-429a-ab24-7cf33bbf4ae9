apiVersion: v1
kind: Service
metadata:
  name: {{ include "apiDashboard.fullname" . }}
  labels:
    app: {{ template "apiDashboard.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: {{ .Values.service.port }}
      protocol: TCP
      name: http
  selector:
    app: {{ template "apiDashboard.fullname" . }}
