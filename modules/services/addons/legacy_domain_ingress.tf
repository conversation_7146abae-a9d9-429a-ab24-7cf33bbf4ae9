resource "kubernetes_ingress_v1" "legacy_website_domain_redirect" {
  count = var.legacy_website_domain != "" && var.tld_domain != "" ? 1 : 0

  metadata {
    name      = "legacy-website-domain-redirect"
    namespace = local.addons_namespace
    annotations = {
      "cert-manager.io/cluster-issuer" = "letsencrypt-issuer"
      "nginx.ingress.kubernetes.io/permanent-redirect" : "https://www.${local.tld_domain}/"
    }
  }

  spec {
    ingress_class_name = "nginx"
    rule {
      host = local.legacy_website_domain
    }
    rule {
      host = "www.${local.legacy_website_domain}"
    }
    tls {
      hosts       = [local.legacy_website_domain, "www.${local.legacy_website_domain}"]
      secret_name = "${local.legacy_website_domain}-tls"
    }
  }
}
