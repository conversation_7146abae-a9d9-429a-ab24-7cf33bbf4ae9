# trigger_ref: ${trigger_ref}

ingress:
  enabled: true
  hosts:
%{ for host in hosts ~}
    - "${host}"
%{ endfor ~}

payUrl: ${pay_url}
payDomain: ${pay_domain}
coreGqlUrlIntranet: ${core_gql_url_intranet}
hydraPublicUrl: ${hydra_public_api}
nextAuthUrl: ${next_auth_url}
clientId: ${client_id}
otelExporterOtlpEndpoint: ${otel_exporter_otlp_endpoint}
tracingServiceName: ${tracing_service_name}

nostrPubkey: ${nostr_pubkey}

galoy-nostr:
  lnd1:
    dns: ${lnd_dns}
  redis:
    redis0Dns: galoy-redis-node-0.galoy-redis-headless.${redis_namespace}.svc.cluster.local
    redis1Dns: galoy-redis-node-1.galoy-redis-headless.${redis_namespace}.svc.cluster.local
    redis2Dns: galoy-redis-node-2.galoy-redis-headless.${redis_namespace}.svc.cluster.local

redis:
  redis0Dns: galoy-redis-node-0.galoy-redis-headless.${redis_namespace}.svc.cluster.local
  redis1Dns: galoy-redis-node-1.galoy-redis-headless.${redis_namespace}.svc.cluster.local
  redis2Dns: galoy-redis-node-2.galoy-redis-headless.${redis_namespace}.svc.cluster.local

secrets:
  create: false
