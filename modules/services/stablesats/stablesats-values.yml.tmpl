# trigger_ref: ${trigger_ref}

secrets:
  create: false
stablesats:
  db:
    poolSize: ${db_pool_size}
  galoy:
    api: "${graphql_uri}"
  exchanges:
    okex:
      client:
        simulated: ${play_money_hedging}
  tracing:
    host: ${otel_host}
    priceServiceName: ${tracing_price_service_name}
    dealerServiceName: ${tracing_dealer_service_name}
  priceServer:
    fees:
      baseFeeRate: ${base_fee_rate}
      immediateFeeRate: ${immediate_fee_rate}
      delayedFeeRate: ${delayed_fee_rate}
  bria:
    url: "http://${bria_host}:2742"
    walletName: ${bria_wallet_name}
    payoutQueueName: ${bria_queue_name}
    onchainAddressExternalId: ${bria_address_id}

postgresql:
  enabled: false
