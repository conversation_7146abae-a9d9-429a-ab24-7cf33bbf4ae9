variable "name_prefix" {}
variable "secrets" {
  sensitive = true
}
variable "gcp_project" {}
variable "gcp_region" {}
variable "ha_pg" { default = true }
variable "db_pool_size" { default = 30 }
variable "root_domain" {}
variable "play_money_hedging" { default = false }
variable "galoy_api_subdomain" {
  default = "api"
}
variable "pg_tier" { default = "db-custom-1-3840" }

locals {
  name_prefix          = var.name_prefix
  gcp_project          = var.gcp_project
  gcp_region           = var.gcp_region
  vpc_name             = "${local.name_prefix}-vpc"
  ha_pg                = var.ha_pg
  pg_tier              = var.pg_tier
  galoy_namespace      = "${local.name_prefix}-galoy"
  bitcoin_namespace    = "${local.name_prefix}-bitcoin"
  stablesats_namespace = "${local.name_prefix}-stablesats"
  smoketest_namespace  = "${local.name_prefix}-smoketest"
  otel_namespace       = "${local.name_prefix}-otel"

  graphql_hostname = "${var.galoy_api_subdomain}.${var.root_domain}"
  graphql_uri      = "https://${local.graphql_hostname}/graphql"

  otel_host                   = "opentelemetry-collector.${local.otel_namespace}.svc.cluster.local"
  tracing_price_service_name  = "${local.name_prefix}-stablesats-price"
  tracing_dealer_service_name = "${local.name_prefix}-stablesats-dealer"

  play_money_hedging = var.play_money_hedging

  pg_instance_name = "${local.name_prefix}-stablesats"

  databases     = ["stablesats"]
  db_pool_size  = var.db_pool_size
  okex_key      = jsondecode(var.secrets).okex.key
  okex_secret   = jsondecode(var.secrets).okex.secret
  okex_password = jsondecode(var.secrets).okex.password

  pg_replication = true

  big_query_viewers = ["serviceAccount:<EMAIL>"]

  base_fee_rate      = 0.0010
  immediate_fee_rate = 0.0010
  delayed_fee_rate   = 0.0010

  bria_host = "bria-api.${local.bitcoin_namespace}.svc.cluster.local"
}
