resource "kubernetes_namespace" "stablesats" {
  metadata {
    name = local.stablesats_namespace
  }
}

data "kubernetes_secret" "dealer_creds" {
  metadata {
    name      = "dealer-creds"
    namespace = local.galoy_namespace
  }
}

data "kubernetes_secret" "bria_creds" {
  metadata {
    name      = "stablesats-bria-creds"
    namespace = local.bitcoin_namespace
  }
}

module "postgresql" {
  source = "../../infra/vendor/tf/postgresql/gcp"

  gcp_project       = local.gcp_project
  region            = local.gcp_region
  vpc_name          = local.vpc_name
  instance_name     = local.pg_instance_name
  highly_available  = local.ha_pg
  databases         = local.databases
  big_query_viewers = local.big_query_viewers
  tier              = local.pg_tier
  replication       = local.pg_replication
}

resource "kubernetes_secret" "stablesats" {
  metadata {
    name      = "stablesats"
    namespace = kubernetes_namespace.stablesats.metadata[0].name
  }

  data = {
    pg-con : module.postgresql.creds["stablesats"].conn
    galoy-phone-code : data.kubernetes_secret.dealer_creds.data["code"]
    okex-secret-key : local.okex_secret
    okex-passphrase : local.okex_password
    bria-profile-api-key : data.kubernetes_secret.bria_creds.data["api-key"]
  }
}

resource "kubernetes_secret" "stablesats_replicator" {
  metadata {
    name      = "stablesats-replicator"
    namespace = kubernetes_namespace.stablesats.metadata[0].name
  }

  data = {
    pg-host : module.postgresql.creds["stablesats"].host
    pg-replicator-password : module.postgresql.replicator["stablesats"].password
  }
}

resource "kubernetes_secret" "smoketest" {
  metadata {
    name      = "stablesats-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    price_server_grpc_host = "stablesats-price.${kubernetes_namespace.stablesats.metadata[0].name}.svc.cluster.local"
    price_server_grpc_port = 3325
  }
}

resource "helm_release" "stablesats" {
  name      = "stablesats"
  chart     = "${path.module}/vendor/stablesats/chart"
  namespace = kubernetes_namespace.stablesats.metadata[0].name

  values = [
    templatefile("${path.module}/stablesats-sensitive-values.yml.tmpl", {
      galoy_phone_number : data.kubernetes_secret.dealer_creds.data["phone"]
      okex_api_key : local.okex_key
    }),
    templatefile("${path.module}/stablesats-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/stablesats/git-ref/ref")
      graphql_uri : local.graphql_uri
      play_money_hedging : local.play_money_hedging
      db_pool_size : local.db_pool_size
      otel_host : local.otel_host
      tracing_price_service_name : local.tracing_price_service_name
      tracing_dealer_service_name : local.tracing_dealer_service_name
      base_fee_rate : local.base_fee_rate
      immediate_fee_rate : local.immediate_fee_rate
      delayed_fee_rate : local.delayed_fee_rate
      bria_host : local.bria_host
      bria_wallet_name : data.kubernetes_secret.bria_creds.data["hot-wallet-name"]
      bria_queue_name : data.kubernetes_secret.bria_creds.data["queue-name"]
      bria_address_id : data.kubernetes_secret.bria_creds.data["address-external-id"]
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/stablesats/stablesats-scaling.yml")
  ]

  dependency_update = true
  depends_on = [
    kubernetes_secret.stablesats
  ]
}
