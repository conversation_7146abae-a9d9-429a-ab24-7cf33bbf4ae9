apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "stablesats.fullname" . }}-price
  labels:
    app: {{ template "stablesats.fullname" . }}-price
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    kube-monkey/identifier: {{ template "stablesats.fullname" . }}-price
    kube-monkey/enabled: enabled
    kube-monkey/kill-mode: fixed
    kube-monkey/kill-value: "1"
    kube-monkey/mtbf: "3"
spec:
  selector:
    matchLabels:
      app: {{ template "stablesats.fullname" . }}-price
      release: {{ .Release.Name }}
  replicas: {{ .Values.stablesats.priceDeployment.replicas }}
  template:
    metadata:
      labels:
        app: {{ template "stablesats.fullname" . }}-price
        release: "{{ .Release.Name }}"
        kube-monkey/identifier: {{ template "stablesats.fullname" . }}-price
        kube-monkey/enabled: enabled
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/stablesats-price-cm.yaml") . | sha256sum }}
      {{- with .Values.stablesats.annotations }}
        {{ toYaml . | nindent 8 }}
      {{- end }}
    spec:
      containers:
      - name: {{ .Chart.Name }}
        image: "{{ .Values.stablesats.image.repository }}@{{ .Values.stablesats.image.digest }}"
        ports:
        - containerPort: {{ .Values.stablesats.priceServer.port }}
        args:
        - stablesats
        - run
        volumeMounts:
        - name: "config"
          mountPath: "/stablesats.yml"
          subPath: "stablesats.yml"
        startupProbe:
          httpGet:
            path: /health/startup
            port: 8080
          periodSeconds: 2
          failureThreshold: 30
          timeoutSeconds: 1
        livenessProbe:
          httpGet:
            path: /health/live
            port: 8080
          periodSeconds: 10
          failureThreshold: 6
          timeoutSeconds: 1
        readinessProbe:
          httpGet:
            path: /health/ready
            port: 8080
          periodSeconds: 10
          failureThreshold: 6
          timeoutSeconds: 1
        env:
        - name: CRASH_REPORT_CONFIG
          value: '{{ .Values.stablesats.crash_report_config_danger }}'
        - name: STABLESATS_CONFIG
          value: "/stablesats.yml"
        - name: KUBE_NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        resources:
          {{- toYaml .Values.stablesats.priceDeployment.resources | nindent 10 }}
      volumes:
      - name: config
        configMap:
          name: {{ include "stablesats.fullname" . }}-price
