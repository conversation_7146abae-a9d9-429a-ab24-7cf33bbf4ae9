apiVersion: v1
kind: Service
metadata:
  name: {{ template "stablesats.fullname" . }}-price
  labels:
    app: {{ template "stablesats.fullname" . }}-price
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
spec:
  type: ClusterIP
  ports:
  - port: {{ .Values.stablesats.priceServer.port }}
    targetPort: {{ .Values.stablesats.priceServer.port }}
    protocol: TCP
    name: http
  selector:
    app: {{ template "stablesats.fullname" . }}-price
