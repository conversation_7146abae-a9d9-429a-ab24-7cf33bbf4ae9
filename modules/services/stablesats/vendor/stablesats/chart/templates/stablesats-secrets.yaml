{{- if .Values.secrets.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "stablesats.fullname" . }}
  labels:
    app: {{ template "stablesats.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
  {{- if .Values.secrets.annotations }}
  annotations:
{{ toYaml .Values.secrets.annotations | indent 4 }}
  {{- end }}
type: Opaque
data:
  pg-con: {{ .Values.secrets.pgCon | trim | b64enc | trim }}
  okex-secret-key: {{ .Values.secrets.okexSecretKey | trim | b64enc | trim }}
  okex-passphrase: {{ .Values.secrets.okexPassphrase | trim | b64enc | trim }}
  galoy-phone-code: {{ .Values.secrets.galoyPhoneCode | trim | b64enc | trim }}
  bria-profile-api-key: {{ .Values.secrets.briaProfileApiKey | trim | b64enc | trim }}
{{- end }}
