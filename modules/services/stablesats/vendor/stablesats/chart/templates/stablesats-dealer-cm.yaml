apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "stablesats.fullname" . }}-dealer
data:
  stablesats.yml: |-
    db:
      pool_size: {{ .Values.stablesats.db.poolSize }}
      migrate_on_startup: true
    exchanges:
      okex:
        weight: 1.0
        config:
          poll_frequency: {{ .Values.stablesats.exchanges.okex.pollFrequency }}
          client:
            api_key: {{ .Values.stablesats.exchanges.okex.client.apiKey }}
            simulated: {{ .Values.stablesats.exchanges.okex.client.simulated }}
    price_server:
      enabled: false
    user_trades:
      enabled: {{ .Values.stablesats.userTrades.enabled }}
      config:
        balance_publish_frequency: {{ .Values.stablesats.userTrades.balancePublishFrequency }}
        galoy_poll_frequency: {{ .Values.stablesats.userTrades.galoyPollFrequency }}
    hedging:
      enabled: {{ .Values.stablesats.hedging.enabled }}
      config:
        health:
          unhealthy_msg_interval_position: {{ .Values.stablesats.hedging.health.unhealthyMsgIntervalPosition }}
          unhealthy_msg_interval_liability: {{ .Values.stablesats.hedging.health.unhealthyMsgIntervalLiability }}
          unhealthy_msg_interval_price: {{ .Values.stablesats.hedging.health.unhealthyMsgIntervalPrice }}
    galoy:
      api: {{ .Values.stablesats.galoy.api }}
      phone_number: {{ .Values.stablesats.galoy.phoneNumber }}
    tracing:
      service_name: {{ .Values.stablesats.tracing.dealerServiceName }}
      host: {{ .Values.stablesats.tracing.host }}
      port: {{ .Values.stablesats.tracing.port }}
    bria:
      url: {{ .Values.stablesats.bria.url }}
      wallet_name: {{ .Values.stablesats.bria.walletName }}
      payout_queue_name: {{ .Values.stablesats.bria.payoutQueueName }}
      onchain_address_external_id: {{ .Values.stablesats.bria.onchainAddressExternalId }}
