secrets:
  create: true
  pgCon: ""
  okexSecretKey: ""
  okexPassphrase: ""
  galoyPhoneCode: ""
  briaProfileApiKey: ""
stablesats:
  priceDeployment:
    replicas: 2
    resources: {}
  dealerDeployment:
    replicas: 1
    resources: {}
  image:
    repository: us.gcr.io/galoy-org/stablesats-rs
    digest: "sha256:2e1fc621f41a3e7b7df6682df82538bb78fb3b6ed36f4c06f2ce898f3b10c6a1" # METADATA:: repository=https://github.com/GaloyMoney/stablesats-rs;commit_ref=eb4bf8d;app=stablesats;
  crash_report_config_danger: false
  db:
    poolSize: 20
  userTrades:
    enabled: true
    balancePublishFrequency: 5
    galoyPollFrequency: 10
  hedging:
    enabled: true
    health:
      unhealthyMsgIntervalPosition: 30
      unhealthyMsgIntervalLiability: 30
      unhealthyMsgIntervalPrice: 30
  priceServer:
    port: 3325
    fees:
      baseFeeRate: 0.0010
      immediateFeeRate: 0.0015
      delayedFeeRate: 0.0010
    priceCache:
      staleAfter: 40
    health:
      unhealthyMsgIntervalPrice: 30
  galoy:
    api: "https://api.staging.blink.sv/graphql"
    phoneNumber: "+50365055530"
  exchanges:
    okex:
      pollFrequency: 10
      client:
        apiKey: ""
        simulated: true
  tracing:
    host: "localhost"
    port: 4318
    priceServiceName: "stablesats-price"
    dealerServiceName: "stablesats-dealer"
  bria:
    url: "http://bria-api.default.svc.cluster.local:2742"
    walletName: "dev-wallet"
    payoutQueueName: "dev-queue"
    onchainAddressExternalId: "dev-onchain-address"
postgresql:
  enabled: true
  auth:
    enablePostgresUser: false
    username: stablesats
    password: stablesats
    database: stablesats
