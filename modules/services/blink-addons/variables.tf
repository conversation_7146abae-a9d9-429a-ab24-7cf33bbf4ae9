variable "name_prefix" {}
variable "gcp_project" {}
variable "gcp_region" {}

variable "secrets" { sensitive = true }
variable "ha_pg" { default = true }
variable "pg_tier" { default = "db-g1-small" }

variable "bigquery_dataset" {}
variable "bigquery_report_circles_table" { default = "report_circles" }
variable "cron_enabled" { default = false }
variable "blink_fiat_airtable_base_id" {}
variable "blink_fiat_telegram_group_chat_id_buy" {}
variable "blink_fiat_telegram_group_chat_id_sell" {}
variable "blink_fiat_google_analytics_id" {}
variable "blink_fiat_email_sender_name" {}
variable "root_domain" {}
variable "email_domain" {}

variable "blink_kyc_onfido_workflow_id" {}

locals {
  name_prefix = var.name_prefix
  gcp_project = var.gcp_project
  gcp_region  = var.gcp_region
  vpc_name    = "${local.name_prefix}-vpc"
  ha_pg       = var.ha_pg
  pg_tier     = var.pg_tier

  galoy_namespace                   = "${local.name_prefix}-galoy"
  smoketest_namespace               = "${local.name_prefix}-smoketest"
  blink_addons_namespace            = "${local.name_prefix}-blink-addons"
  otel_namespace                    = "${local.name_prefix}-otel"
  admin_graphql_endpoint            = "https://admin-api.${var.root_domain}/graphql"
  oathkeeper_proxy_endpoint         = "http://galoy-oathkeeper-proxy.${local.galoy_namespace}.svc.cluster.local:4455"
  oathkeeper_proxy_graphql_endpoint = "${local.oathkeeper_proxy_endpoint}/graphql"
  oathkeeper_api_endpoint           = "http://galoy-oathkeeper-api.${local.galoy_namespace}.svc.cluster.local:4456"
  otel_exporter_otlp_endpoint       = "http://opentelemetry-collector.${local.otel_namespace}.svc.cluster.local:4318"

  blink_circles_bigquery_sa_creds = jsondecode(var.secrets).blink_circles_bigquery_sa_creds
  bigquery_dataset                = var.bigquery_dataset
  bigquery_report_circles_table   = var.bigquery_report_circles_table
  notifications_host              = "notifications.${local.galoy_namespace}.svc.cluster.local"
  cron_enabled                    = var.cron_enabled
  hydra_public_api                = "http://galoy-hydra-public.${local.galoy_namespace}.svc.cluster.local:4444"

  big_query_viewers                                 = ["serviceAccount:<EMAIL>"]
  blink_fiat_host                                   = "fiat.${var.root_domain}"
  blink_fiat_airtable_token                         = jsondecode(var.secrets).blink_fiat_airtable_token
  blink_fiat_broker_token                           = jsondecode(var.secrets).blink_fiat_broker_token
  blink_fiat_airtable_base_id                       = var.blink_fiat_airtable_base_id
  blink_fiat_callback_url                           = "https://${local.blink_fiat_host}/api/callback"
  blink_fiat_telegram_token                         = jsondecode(var.secrets).blink_fiat_telegram_token
  blink_fiat_telegram_group_chat_id_buy             = var.blink_fiat_telegram_group_chat_id_buy
  blink_fiat_telegram_group_chat_id_sell            = var.blink_fiat_telegram_group_chat_id_sell
  blink_fiat_pg_instance_name                       = "${local.name_prefix}-blink-fiat"
  blink_fiat_databases                              = ["blink-fiat"]
  blink_fiat_google_analytics_id                    = var.blink_fiat_google_analytics_id
  blink_fiat_max_amount_per_transaction_cent_sell   = "1000000"
  blink_fiat_min_amount_per_transaction_cent_sell   = "1000"
  blink_fiat_max_daily_transaction_amount_cent_sell = "2500000"
  blink_fiat_max_amount_per_transaction_cent_buy    = "2500000"
  blink_fiat_min_amount_per_transaction_cent_buy    = "1000"
  blink_fiat_buy_fee_in_ppm                         = "20000"
  blink_fiat_min_buy_fee_in_cent                    = "100"
  blink_fiat_sell_fee_in_ppm                        = "20000"
  blink_fiat_min_sell_fee_in_cent                   = "100"
  blink_fiat_mailgun_smtp_username                  = "fiat@${var.email_domain}"
  blink_fiat_mailgun_smtp_password                  = jsondecode(var.secrets).blink_fiat_mailgun_smtp_password
  blink_fiat_mailgun_from_field                     = "${var.blink_fiat_email_sender_name} <fiat@${var.email_domain}>"

  kyc_pg_instance_name         = "${local.name_prefix}-kyc"
  kyc_databases                = ["kyc"]
  blink_kyc_host               = "kyc.${var.root_domain}"
  blink_kyc_onfido_workflow_id = var.blink_kyc_onfido_workflow_id
  blink_kyc_onfido_api_token   = jsondecode(var.secrets).blink_kyc_onfido_api_token
  notifications_api            = "notifications.${local.galoy_namespace}.svc.cluster.local:6685"
}
