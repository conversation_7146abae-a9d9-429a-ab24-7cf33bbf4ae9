resource "kubernetes_secret" "blink_fiat_smoketest" {
  metadata {
    name      = "blink-fiat-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    blink_fiat_endpoint = "https://${local.blink_fiat_host}"
    blink_fiat_port     = 443
  }
}

resource "hydra_oauth2_client" "blink_fiat" {
  client_name                = "blink_fiat"
  grant_types                = ["client_credentials"]
  response_types             = ["token"]
  token_endpoint_auth_method = "client_secret_basic"
  scopes                     = ["editor"]
}

resource "kubernetes_secret" "blink_fiat" {
  metadata {
    name      = "blink-fiat"
    namespace = kubernetes_namespace.blink_addons.metadata[0].name
  }
  data = {
    airtable-token          = local.blink_fiat_airtable_token
    telegram-token          = local.blink_fiat_telegram_token
    broker-token            = local.blink_fiat_broker_token
    admin-api-client-id     = hydra_oauth2_client.blink_fiat.client_id
    admin-api-client-secret = hydra_oauth2_client.blink_fiat.client_secret
    mailgun-smtp-password   = local.blink_fiat_mailgun_smtp_password
  }
}

resource "helm_release" "blink_fiat" {
  name      = "blink-fiat"
  chart     = "${path.module}/vendor/blink-fiat/chart"
  namespace = kubernetes_namespace.blink_addons.metadata[0].name

  dependency_update = true
  values = [
    templatefile("${path.module}/blink-fiat-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/blink-fiat/git-ref/ref")
      host : local.blink_fiat_host
      public_graphql_endpoint : local.oathkeeper_proxy_graphql_endpoint
      admin_graphql_endpoint : local.admin_graphql_endpoint
      airtable_base_id : local.blink_fiat_airtable_base_id
      callback_url : local.blink_fiat_callback_url
      telegram_group_chat_id_buy : local.blink_fiat_telegram_group_chat_id_buy
      telegram_group_chat_id_sell : local.blink_fiat_telegram_group_chat_id_sell
      otel_exporter_otlp_endpoint : local.otel_exporter_otlp_endpoint
      tracing_prefix : local.name_prefix
      hydra_public_api : local.hydra_public_api
      google_analytics_id : local.blink_fiat_google_analytics_id
      max_amount_per_transaction_cent_sell : local.blink_fiat_max_amount_per_transaction_cent_sell
      max_amount_per_transaction_cent_buy : local.blink_fiat_max_amount_per_transaction_cent_buy
      min_amount_per_transaction_cent_sell : local.blink_fiat_min_amount_per_transaction_cent_sell
      min_amount_per_transaction_cent_buy : local.blink_fiat_min_amount_per_transaction_cent_buy
      max_daily_transaction_amount_cent_sell : local.blink_fiat_max_daily_transaction_amount_cent_sell
      buy_fee_in_ppm : local.blink_fiat_buy_fee_in_ppm
      min_buy_fee_in_cent : local.blink_fiat_min_buy_fee_in_cent
      sell_fee_in_ppm : local.blink_fiat_sell_fee_in_ppm
      min_sell_fee_in_cent : local.blink_fiat_min_sell_fee_in_cent
      mailgun_smtp_username : local.blink_fiat_mailgun_smtp_username
      mailgun_from_field : local.blink_fiat_mailgun_from_field
    })
  ]
}
