# trigger_ref: ${trigger_ref}

ingress:
  enabled: true
  hosts:
  - "${host}"

secrets:
  create: false

blinkKyc:
  graphqlAdminApi: ${admin_graphql_endpoint}
  hydraPublicApi: ${hydra_public_api}
  oathkeeperApi: ${oathkeeper_api}
  onfidoWorkflowId: ${onfido_workflow_id}
  adminApiClientId: ${admin_api_client_id}
  notificationsApi: ${notifications_api}
  tracing:
    serviceName: "${tracing_prefix}-blink-kyc"
    otelExporterOtlpEndpoint: ${otel_exporter_otlp_endpoint}
