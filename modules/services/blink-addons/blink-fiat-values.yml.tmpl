# trigger_ref: ${trigger_ref}

ingress:
  enabled: true
  hosts:
  - "${host}"

secrets:
  create: false

blinkFiat:
  graphqlPublicApi: ${public_graphql_endpoint}
  graphqlAdminApi: ${admin_graphql_endpoint}
  airtableBaseId: ${airtable_base_id}
  callbackUrl: ${callback_url}
  telegramGroupChatIdBuy: "${telegram_group_chat_id_buy}"
  telegramGroupChatIdSell: "${telegram_group_chat_id_sell}"
  hydraPublicApi: ${hydra_public_api}
  tracing:
    serviceName: "${tracing_prefix}-blink-fiat"
    otelExporterOtlpEndpoint: ${otel_exporter_otlp_endpoint}
  googleAnalyticsId: ${google_analytics_id}
  maxAmountPerTransactionCentSell: "${max_amount_per_transaction_cent_sell}"
  maxAmountPerTransactionCentBuy: "${max_amount_per_transaction_cent_buy}"
  minAmountPerTransactionCentSell: "${min_amount_per_transaction_cent_sell}"
  minAmountPerTransactionCentBuy: "${min_amount_per_transaction_cent_buy}"
  maxDailyTransactionAmountCentSell: "${max_daily_transaction_amount_cent_sell}"
  buyFeeInPpm: ${buy_fee_in_ppm}
  minBuyFeeInCent: ${min_buy_fee_in_cent}
  sellFeeInPpm: ${sell_fee_in_ppm}
  minSellFeeInCent: ${min_sell_fee_in_cent}
  mailgunSmtpUsername: ${mailgun_smtp_username}
  mailgunFromField: ${mailgun_from_field}
