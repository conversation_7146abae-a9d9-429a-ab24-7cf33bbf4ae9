resource "hydra_oauth2_client" "blink_circles" {
  client_name                = "blink_circles"
  grant_types                = ["client_credentials"]
  response_types             = ["token"]
  token_endpoint_auth_method = "client_secret_basic"
  scopes                     = ["editor"]
}

resource "kubernetes_secret" "blink_circles" {
  metadata {
    name      = "circles"
    namespace = kubernetes_namespace.blink_addons.metadata[0].name
  }
  data = {
    "bigquery-service-account.json" = local.blink_circles_bigquery_sa_creds
  }
}

resource "helm_release" "blink_circles" {
  name      = "circles"
  chart     = "${path.module}/vendor/circles/chart"
  namespace = kubernetes_namespace.blink_addons.metadata[0].name

  dependency_update = true
  values = [
    templatefile("${path.module}/blink-circles-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/circles/git-ref/ref")
      bigquery_dataset : local.bigquery_dataset
      bigquery_report_circles_table : local.bigquery_report_circles_table
      otel_exporter_otlp_endpoint : local.otel_exporter_otlp_endpoint
      tracing_prefix : local.name_prefix
      notifications_host : local.notifications_host
      cron_enabled : local.cron_enabled
    })
  ]
}

provider "hydra" {
  endpoint = "http://galoy-hydra-admin.${local.galoy_namespace}:4445"
}

terraform {
  required_providers {
    hydra = {
      source  = "svrakitin/hydra"
      version = "0.5.2"
    }
  }
}
