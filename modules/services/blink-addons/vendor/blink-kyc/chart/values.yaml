image:
  repository: gcr.io/galoy-org/blink-kyc
  digest: "sha256:a0698095c45b83255545bd95b6e5c404d09f71debbd139cf5a56100b3965a4ee" # METADATA:: repository=https://github.com/blinkbitcoin/blink-kyc;commit_ref=71e69d0;app=blink-kyc;
  git_ref: "986057f"
ingress:
  enabled: false
  hosts:
    - "example.com"
service:
  port: 3000
  type: ClusterIP
blinkKyc:
  onfidoWorklflowId: "1a657d0c-b676-4bbf-9d18-d9ecb8547d8d"
  adminApiClientId: "client-id"
  graphqlAdminApi: "http://localhost:4002/admin/graphql"
  hydraPublicApi: "http://localhost:4444"
  oathkeeperApi: "http://localhost:4455"
  notificationsApi: "http://localhost:6685"
  tracing:
    serviceName: "blink-kyc"
    otelExporterOtlpEndpoint: http://localhost:4318
secrets:
  create: false
  adminApiClientSecret: ""
  onfidoApiToken: ""
  pgCon: ""
