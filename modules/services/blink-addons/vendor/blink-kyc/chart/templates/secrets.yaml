{{- if .Values.secrets.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "blinkKyc.fullname" . }}
  labels:
    app: {{ template "blinkKyc.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
type: Opaque
data:
  admin-api-client-secret: {{ .Values.secrets.adminApiClientSecret | b64enc | quote }}
  onfido-api-token: {{ .Values.secrets.onfidoApiToken | b64enc | quote }}
  pg-con: {{ .Values.secrets.pgCon | b64enc | quote }}
{{- end }}
