apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "blinkKyc.fullname" . }}
  labels:
    app: {{ template "blinkKyc.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
spec:
  selector:
    matchLabels:
      app: {{ template "blinkKyc.fullname" . }}
      release: {{ .Release.Name }}
  replicas: 1
  template:
    metadata:
      labels:
        app: {{ template "blinkKyc.fullname" . }}
        release: "{{ .Release.Name }}"
    spec:
      containers:
      - name: blink-kyc
        image: "{{ .Values.image.repository }}@{{ .Values.image.digest }}"
        ports:
        - containerPort: {{ .Values.service.port }}
        resources:
          {{- toYaml .Values.resources | nindent 10 }}
        env:
        - name: GRAPHQL_ADMIN_API
          value: {{ .Values.blinkKyc.graphqlAdminApi }}
        - name: CALLBACK_URL
          value: {{ .Values.blinkKyc.callbackUrl }}
        - name: OATHKEEPER_API
          value: {{ .Values.blinkKyc.oathkeeperApi }}
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: {{ .Values.blinkKyc.tracing.otelExporterOtlpEndpoint }}
        - name: TRACING_SERVICE_NAME
          value: {{ .Values.blinkKyc.tracing.serviceName }}
        - name: HYDRA_PUBLIC_API
          value: {{ .Values.blinkKyc.hydraPublicApi }}
        - name: NOTIFICATIONS_API
          value: {{ .Values.blinkKyc.notificationsApi }}
        - name: ADMIN_API_CLIENT_ID
          value: {{ .Values.blinkKyc.adminApiClientId }}
        - name: ADMIN_API_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ template "blinkKyc.fullname" . }}
              key: admin-api-client-secret
        - name: ONFIDO_WORKFLOW_ID
          value: {{ .Values.blinkKyc.onfidoWorkflowId }}
        - name: ONFIDO_API_TOKEN
          valueFrom:
            secretKeyRef:
              name: {{ template "blinkKyc.fullname" . }}
              key: onfido-api-token
        - name: PG_CON
          valueFrom:
            secretKeyRef:
              name: {{ template "blinkKyc.fullname" . }}
              key: pg-con
