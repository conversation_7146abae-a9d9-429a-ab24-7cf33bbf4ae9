apiVersion: v1
kind: Service
metadata:
  name: {{ template "blinkKyc.fullname" . }}
  labels:
    app: {{ template "blinkKyc.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: 3000
      protocol: TCP
      name: http
  selector:
    app: {{ template "blinkKyc.fullname" . }}
