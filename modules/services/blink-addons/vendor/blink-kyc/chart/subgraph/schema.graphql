extend schema
  @link(
    url: "https://specs.apollo.dev/federation/v2.3"
    import: ["@key", "@shareable", "@interfaceObject"]
  )

enum OnboardingStatus {
  NOT_STARTED
  AWAITING_INPUT
  PROCESSING
  ABANDONED
  ERROR
  APPROVED
  REVIEW
  DECLINED
}

type ConsumerAccount @key(fields: "id") {
  id: ID!
  onboardingStatus: OnboardingStatus
  firstName: String
  lastName: String
}

type Mutation {
  onboardingFlowStart(
    input: OnboardingFlowStartInput!
  ): OnboardingFlowStartResult!
}

input OnboardingFlowStartInput {
  firstName: String!
  lastName: String!
}

type OnboardingFlowStartResult {
  workflowRunId: String!
  tokenAndroid: String!
  tokenIos: String!
  tokenWeb: String!
}
