image:
  repository: gcr.io/galoy-org/blink-fiat
  digest: "sha256:e0ff42ae4f76f4ffcdfa030775ce37c210c08d82a00c4078e266bd7abbeca863" # METADATA:: repository=https://github.com/blinkbitcoin/blink-fiat;commit_ref=1200cc5;app=blink-fiat;
  git_ref: "22d6af2" # Not used by helm.
ingress:
  enabled: false
service:
  port: 3000
  type: ClusterIP
resources: {}
blinkFiat:
  graphqlPublicApi: http://localhost:4002/graphql
  graphqlAdminApi: http://localhost:4002/admin/graphql
  airtableBaseId: "dummy"
  googleAnalyticsId: ""
  telegramGroupChatIdBuy: "-12345"
  telegramGroupChatIdSell: "-12345"
  callbackUrl: http://localhost:3000/api/callback
  hydraPublicApi: "http://localhost:4444"
  tracing:
    serviceName: "blink-fiat"
    otelExporterOtlpEndpoint: http://localhost:4318
  maxAmountPerTransactionCentSell: 100000
  minAmountPerTransactionCentSell: 1000
  maxAmountPerTransactionCentBuy: 1000000
  minAmountPerTransactionCentBuy: 1000
  buyFeeInPpm: 15000
  minBuyFeeInCent: 100
  sellFeeInPpm: 15000
  minSellFeeInCent: 100
  maxDailyTransactionAmountCentSell: 500000
  mailgunSmtpUsername: "<EMAIL>"
  mailgunFromField: "Support <<EMAIL>>"
secrets:
  create: true
  airtableToken: ""
  telegramToken: ""
  brokerToken: ""
  adminApiClientId: ""
  adminApiClientSecret: ""
  mailgunSmtpPassword: ""
