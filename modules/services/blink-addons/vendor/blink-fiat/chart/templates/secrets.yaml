{{- if .Values.secrets.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "blinkFiat.fullname" . }}
  labels:
    app: {{ template "blinkFiat.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
type: Opaque
data:
  airtable-token: {{ .Values.secrets.airtableToken | b64enc | quote }}
  telegram-token: {{ .Values.secrets.telegramToken | b64enc | quote }}
  broker-token: {{ .Values.secrets.brokerToken | b64enc | quote }}
  admin-api-client-secret: {{ .Values.secrets.adminApiClientSecret | b64enc | quote }}
  admin-api-client-id: {{ .Values.secrets.adminApiClientId | b64enc | quote }}
  mailgun-smtp-password: {{ .Values.secrets.mailgunSmtpPassword | b64enc | quote }}
{{- end }}
