apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "blinkFiat.fullname" . }}
  labels:
    app: {{ template "blinkFiat.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
spec:
  selector:
    matchLabels:
      app: {{ template "blinkFiat.fullname" . }}
      release: {{ .Release.Name }}
  replicas: 1
  template:
    metadata:
      labels:
        app: {{ template "blinkFiat.fullname" . }}
        release: "{{ .Release.Name }}"
    spec:
      containers:
      - name: blink-fiat
        image: "{{ .Values.image.repository }}@{{ .Values.image.digest }}"
        ports:
        - containerPort: {{ .Values.service.port }}
        resources:
          {{- toYaml .Values.resources | nindent 10 }}
        env:
        - name: BROKER_TOKEN
          valueFrom:
            secretKeyRef:
              name: {{ template "blinkFiat.fullname" . }}
              key: broker-token
        - name: AIRTABLE_TOKEN
          valueFrom:
            secretKeyRef:
              name: {{ template "blinkFiat.fullname" . }}
              key: airtable-token
        - name: TELEGRAM_TOKEN
          valueFrom:
            secretKeyRef:
              name: {{ template "blinkFiat.fullname" . }}
              key: telegram-token
        - name: GRAPHQL_PUBLIC_API
          value: {{ .Values.blinkFiat.graphqlPublicApi }}
        - name: GRAPHQL_ADMIN_API
          value: {{ .Values.blinkFiat.graphqlAdminApi }}
        - name: AIRTABLE_BASE_ID
          value: {{ .Values.blinkFiat.airtableBaseId }}
        - name: CALLBACK_URL
          value: {{ .Values.blinkFiat.callbackUrl }}
        - name: TELEGRAM_GROUP_CHAT_ID_BUY
          value: {{ .Values.blinkFiat.telegramGroupChatIdBuy | quote }}
        - name: TELEGRAM_GROUP_CHAT_ID_SELL
          value: {{ .Values.blinkFiat.telegramGroupChatIdSell | quote }}
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: {{ .Values.blinkFiat.tracing.otelExporterOtlpEndpoint }}
        - name: TRACING_SERVICE_NAME
          value: {{ .Values.blinkFiat.tracing.serviceName }}
        - name: ADMIN_API_CLIENT_ID
          valueFrom:
            secretKeyRef:
              name: {{ template "blinkFiat.fullname" . }}
              key: admin-api-client-id
        - name: ADMIN_API_CLIENT_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ template "blinkFiat.fullname" . }}
              key: admin-api-client-secret
        - name: HYDRA_PUBLIC_API
          value: {{ .Values.blinkFiat.hydraPublicApi }}
        - name: NEXT_PUBLIC_GOOGLE_ANALYTICS_ID
          value: {{ .Values.blinkFiat.googleAnalyticsId }}
        - name: NEXT_PUBLIC_MAX_AMOUNT_PER_TRANSACTION_CENT_SELL
          value: "{{ .Values.blinkFiat.maxAmountPerTransactionCentSell }}"
        - name: NEXT_PUBLIC_MIN_AMOUNT_PER_TRANSACTION_CENT_SELL
          value: "{{ .Values.blinkFiat.minAmountPerTransactionCentSell }}"
        - name: NEXT_PUBLIC_MAX_AMOUNT_PER_TRANSACTION_CENT_BUY
          value: "{{ .Values.blinkFiat.maxAmountPerTransactionCentBuy }}"
        - name: NEXT_PUBLIC_MIN_AMOUNT_PER_TRANSACTION_CENT_BUY
          value: "{{ .Values.blinkFiat.minAmountPerTransactionCentBuy }}"
        - name: BUY_FEE_IN_PPM
          value: "{{ .Values.blinkFiat.buyFeeInPpm}}"
        - name: MIN_BUY_FEE_IN_CENT
          value: "{{ .Values.blinkFiat.minBuyFeeInCent}}"
        - name: SELL_FEE_IN_PPM
          value: "{{ .Values.blinkFiat.sellFeeInPpm}}"
        - name: MIN_SELL_FEE_IN_CENT
          value: "{{ .Values.blinkFiat.minSellFeeInCent}}"
        - name: NEXT_PUBLIC_MAX_DAILY_TRANSACTION_AMOUNT_CENT_SELL
          value: "{{ .Values.blinkFiat.maxDailyTransactionAmountCentSell }}"
        - name: MAILGUN_SMTP_USERNAME
          value: {{ .Values.blinkFiat.mailgunSmtpUsername }}
        - name: MAILGUN_SMTP_PASSWORD
          valueFrom:
            secretKeyRef:
              name: {{ template "blinkFiat.fullname" . }}
              key: mailgun-smtp-password
        - name: MAILGUN_FROM_FIELD
          value: {{ .Values.blinkFiat.mailgunFromField }}
