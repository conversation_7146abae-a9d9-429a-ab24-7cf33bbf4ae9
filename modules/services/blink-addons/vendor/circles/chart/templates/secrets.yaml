{{- if .Values.secrets.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "blinkFiat.fullname" . }}
  labels:
    app: {{ template "blinkFiat.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    heritage: "{{ .Release.Service }}"
type: Opaque
data:
  "bigquery-service-account.json": {{ .Values.secrets.bigqueryServiceAccountJson | b64enc | quote }}
{{- end }}
