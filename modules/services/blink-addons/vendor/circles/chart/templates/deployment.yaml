apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "circles.fullname" . }}
  labels:
    {{- include "circles.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "circles.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "circles.selectorLabels" . | nindent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}@{{ .Values.image.digest }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
            - name: BIG_QUERY_SERVICE_ACCOUNT_PATH
              value: "/etc/gcp/service-account.json"
            - name: BIG_QUERY_DATASET
              value: {{ .Values.bigQuery.dataset }}
            - name: BIG_QUERY_WELCOME_PROFILE_TABLE
              value: {{ .Values.bigQuery.welcomeProfileTable }}
            - name: BIG_QUERY_WELCOME_PROFILE_CHANGE_TABLE
              value: {{ .Values.bigQuery.welcomeProfileChangeTable }}
            - name: SUBGRAPH_PORT
              value: "{{ .Values.service.port }}"
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: {{ .Values.tracing.otelExporterOtlpEndpoint }}
            - name: NOTIFICATIONS_HOST
              value: {{ .Values.notifications.host }}
            - name: NOTIFICATIONS_PORT
              value: "{{ .Values.notifications.port }}"
            - name: RUN_CRON_IN_GQL_SERVER
              value: "{{ .Values.cron.runCronInGqlServer }}"
            - name: CRON_EXPRESSION
              value: "{{ .Values.cron.cronExpression }}"
          volumeMounts:
            - name: bigquery-service-account
              mountPath: /etc/gcp
              readOnly: true
      volumes:
        - name: bigquery-service-account
          secret:
            secretName: {{ template "circles.fullname" . }}
            items:
              - key: bigquery-service-account.json
                path: service-account.json
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
