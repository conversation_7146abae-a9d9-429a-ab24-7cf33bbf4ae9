extend schema @link(url: "https://specs.apollo.dev/federation/v2.3", import: ["@key", "@shareable", "@interfaceObject"])

type Query {
    welcomeLeaderboard(input: WelcomeLeaderboardInput!): Leaderboard!
}

input WelcomeLeaderboardInput {
    range: WelcomeRange!
}

type Leaderboard {
    range: WelcomeRange!
    leaders: [Leader!]!
}

type Leader {
    rank: Int!
    name: LeaderboardName
    points: Int!
}

scalar LeaderboardName

enum WelcomeRange {
    ThisMonth
    AllTime
}

type ConsumerAccount @key(fields: "id") {
    id: ID!
    welcomeProfile: WelcomeProfile
}

type WelcomeProfile {
    leaderboardName: LeaderboardName
    innerCircleThisMonthCount: Int!
    innerCircleAllTimeCount: Int!
    outerCircleThisMonthCount: Int!
    outerCircleAllTimeCount: Int!
    thisMonthPoints: Int!
    thisMonthRank: Int!
    allTimePoints: Int!
    allTimeRank: Int!
}

