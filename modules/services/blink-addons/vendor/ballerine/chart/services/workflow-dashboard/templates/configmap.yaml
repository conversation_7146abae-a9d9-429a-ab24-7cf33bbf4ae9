{{- $name := .Release.Name }}
{{- $namespace:= .Release.Namespace }}
{{- if .Values.workflowsdashboard.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
    name: {{ .Values.workflowsdashboard.nameOverride }}
    namespace: {{ .Release.Namespace | quote }}
    labels:
      app: {{ .Values.workflowsdashboard.nameOverride }}
data:
  {{- range $key, $value := .Values.workflowsdashboard.applicationConfig }}
  {{- if $value }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
  {{- end }}
{{- end }}