{{- $name := .Release.Name }}
{{- $namespace:= .Release.Namespace }}
{{- $postgresqlUser := .Values.postgresql.auth.username -}}
{{- $postgresqlPassword := .Values.postgresql.auth.password -}}
{{- $postgresqlDatabase := .Values.postgresql.auth.database -}}
{{- if .Values.workflowService.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
    name: {{ .Values.workflowService.nameOverride }}
    namespace: {{ .Release.Namespace | quote }}
    labels:
      app: {{ .Values.workflowService.nameOverride }}
data:
  {{- range $key, $value := .Values.workflowService.applicationConfig }}
  {{- if (eq "BACKOFFICE_CORS_ORIGIN" $key) }}
  {{ $key }} : backoffice.{{ $namespace }}.svc.cluster.local:80
  {{- end }}
  {{- if (eq "WORKFLOW_DASHBOARD_CORS_ORIGIN" $key) }}
  {{ $key }} : workflows-dashboard.{{ $namespace }}.svc.cluster.local:80
  {{- end }}
  {{- if (eq "HEADLESS_EXAMPLE_CORS_ORIGIN" $key) }}
  {{ $key }} : headlessexample.{{ $namespace }}.svc.cluster.local:80
  {{- end }}
  {{- if $.Values.postgresql.enabled }}  
  {{- if (eq "DB_URL" $key) }}
  {{ $key }}: postgres://{{ $postgresqlUser }}:{{ $postgresqlPassword }}@{{ $name }}-postgresql.{{ $namespace }}.svc.cluster.local:5432/{{ $postgresqlDatabase }}
  {{- end }}
  {{- if (eq "DB_USER" $key) }}
  {{ $key }}: {{ $postgresqlUser }}
  {{- end }}
  {{- if (eq "DB_PASSWORD" $key) }}
  {{ $key }}: {{ $postgresqlPassword }}
  {{- end }}
  {{- end }}
  {{- if $value }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
  {{- end }}
{{- end }}
