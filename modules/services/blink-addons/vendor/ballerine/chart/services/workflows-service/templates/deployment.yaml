{{- if .Values.workflowService.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
    name: {{ .Values.workflowService.nameOverride }}
    namespace: {{ .Release.Namespace | quote }}
    labels:
      app: {{ .Values.workflowService.nameOverride }}
spec:
    replicas: {{ .Values.workflowService.replicas }}
    {{- if .Values.workflowService.strategyType }}
    strategy:
      type: {{ .Values.workflowService.strategyType }}
    {{- end }}
    {{- if .Values.workflowService.updateStrategy }}
      rollingUpdate:
        {{- if .Values.workflowService.updateStrategy.maxSurge }}
        maxSurge: {{ .Values.workflowService.updateStrategy.maxSurge}}
        {{- end }}
        {{- if .Values.workflowService.updateStrategy.maxUnavailable }}
        maxUnavailable: {{ .Values.workflowService.updateStrategy.maxUnavailable }}
        {{- end }}
    {{- end }}
    selector:
        matchLabels:
            app: {{ .Values.workflowService.nameOverride }}
    template:
        metadata:
            labels:
                app: {{ .Values.workflowService.nameOverride }}
        spec:
            {{- with .Values.nodeSelector }}
            nodeSelector:
            {{- toYaml . | nindent 8 }}
            {{- end }}
            initContainers:
            {{- if .Values.postgresql.enabled }}
            - name: psql-init-container
              image: alpine:latest
              command: ['sh', '-c', "apk add postgresql-client; until pg_isready -h {{ .Release.Name }}-postgresql.{{.Release.Namespace}}.svc.cluster.local; do echo waiting for postgresql; sleep 2; done"]
            {{- end }}
            {{- if .Values.workflowService.migration }}
            - name: migration-init-container
              image: {{ .Values.workflowService.image.registry }}/{{ .Values.workflowService.image.repository }}:{{ .Values.workflowService.image.tag }}
              command: ['npm','run','db:init']
              envFrom:
                - configMapRef:
                    name: {{ .Values.workflowService.nameOverride }}
            {{- end }}
            containers:
            - name: {{ .Values.workflowService.nameOverride }}
              image: "{{ .Values.workflowService.image.registry }}/{{ .Values.workflowService.image.repository }}:{{ .Values.workflowService.image.tag }}"
              imagePullPolicy: {{ .Values.workflowService.image.pullPolicy }}
              command: [ "dumb-init", "npm", "run", "prod" ]
              envFrom:
                - configMapRef:
                    name: {{ .Values.workflowService.nameOverride }}
            {{- if .Values.workflowService.image.pullSecrets}}
            imagePullSecrets:
              - name: {{ .Values.workflowService.image.pullSecrets }}
            {{- end }}
{{- end }}
