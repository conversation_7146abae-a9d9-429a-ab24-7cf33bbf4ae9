{{- if .Values.websocketService.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
    name: {{ .Values.websocketService.nameOverride }}
    namespace: {{ .Release.Namespace | quote }}
    labels:
      app: {{ .Values.websocketService.nameOverride }}
spec:
    replicas: {{ .Values.websocketService.replicas }}
    {{- if .Values.websocketService.strategyType }}
    strategy:
      type: {{ .Values.websocketService.strategyType }}
    {{- end }}
    {{- if .Values.websocketService.updateStrategy }}
      rollingUpdate:
        {{- if .Values.websocketService.updateStrategy.maxSurge }}
        maxSurge: {{ .Values.websocketService.updateStrategy.maxSurge}}
        {{- end }}
        {{- if .Values.websocketService.updateStrategy.maxUnavailable }}
        maxUnavailable: {{ .Values.websocketService.updateStrategy.maxUnavailable }}
        {{- end }}
    {{- end }}
    selector:
        matchLabels:
            app: {{ .Values.websocketService.nameOverride }}
    template:
        metadata:
            labels:
                app: {{ .Values.websocketService.nameOverride }}
        spec:
            {{- with .Values.nodeSelector }}
            nodeSelector:
            {{- toYaml . | nindent 8 }}
            {{- end }}
            containers:
            - name: {{ .Values.websocketService.nameOverride }}
              image: "{{ .Values.websocketService.image.registry }}/{{ .Values.websocketService.image.repository }}:{{ .Values.websocketService.image.tag }}"
              imagePullPolicy: {{ .Values.websocketService.image.pullPolicy }}
              command: [ "npm", "run", "start:prod" ]
              envFrom:
                - configMapRef:
                    name: {{ .Values.websocketService.nameOverride }}
            {{- if .Values.websocketService.image.pullSecrets}}
            imagePullSecrets:
              - name: {{ .Values.websocketService.image.pullSecrets }}
            {{- end }}
{{- end }}
