{{- if .Values.websocketService.enabled }}
apiVersion: v1
kind: Service
metadata:
    name: {{ .Values.websocketService.nameOverride }}
    namespace: {{ .Release.Namespace | quote }}
    labels:
      app: {{ .Values.websocketService.nameOverride }}
spec:
    ports:
    - name: {{ .Values.websocketService.nameOverride }}
      port: {{ .Values.websocketService.service.port }}
      protocol: {{ .Values.websocketService.service.protocol }}
      targetPort: {{ .Values.websocketService.service.port }}
    selector:
        app: {{ .Values.websocketService.nameOverride }}
    type: {{ .Values.websocketService.service.type }}
{{- end }}