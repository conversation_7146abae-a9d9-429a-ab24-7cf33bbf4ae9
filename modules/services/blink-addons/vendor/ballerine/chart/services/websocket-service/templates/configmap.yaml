{{- $name := .Release.Name }}
{{- $namespace:= .Release.Namespace }}
{{- if .Values.websocketService.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
    name: {{ .Values.websocketService.nameOverride }}
    namespace: {{ .Release.Namespace | quote }}
    labels:
      app: {{ .Values.websocketService.nameOverride }}
data:
  {{- range $key, $value := .Values.websocketService.applicationConfig }}
  {{- if $value }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
  {{- end }}
{{- end }}
