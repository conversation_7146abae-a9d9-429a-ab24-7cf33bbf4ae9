{{- if .Values.headlessexample.enabled }}
apiVersion: v1
kind: Service
metadata:
    name: {{ .Values.headlessexample.nameOverride }}
    namespace: {{ .Release.Namespace | quote }}
    labels:
      app: {{ .Values.headlessexample.nameOverride }}
spec:
    ports:
    - name: {{ .Values.headlessexample.nameOverride }}
      port: {{ .Values.headlessexample.service.port }}
      protocol: {{ .Values.headlessexample.service.protocol }}
      targetPort: {{ .Values.headlessexample.service.port }}
    selector:
        app: {{ .Values.headlessexample.nameOverride }}
    type: {{ .Values.headlessexample.service.type }}
{{- end }}