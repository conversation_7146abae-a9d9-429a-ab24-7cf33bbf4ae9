{{- if .Values.headlessexample.enabled }}
apiVersion: apps/v1
kind: Deployment
metadata:
    name: {{ .Values.headlessexample.nameOverride }}
    namespace: {{ .Release.Namespace | quote }}
    labels:
      app: {{ .Values.headlessexample.nameOverride }}
spec:
    replicas: {{ .Values.headlessexample.replicas }}
    {{- if .Values.headlessexample.strategyType }}
    strategy:
      type: {{ .Values.headlessexample.strategyType }}
    {{- end }}
    {{- if .Values.headlessexample.updateStrategy }}
      rollingUpdate:
        {{- if .Values.headlessexample.updateStrategy.maxSurge }}
        maxSurge: {{ .Values.headlessexample.updateStrategy.maxSurge}}
        {{- end }}
        {{- if .Values.headlessexample.updateStrategy.maxUnavailable }}
        maxUnavailable: {{ .Values.headlessexample.updateStrategy.maxUnavailable }}
        {{- end }}
    {{- end }}
    selector:
        matchLabels:
            app: {{ .Values.headlessexample.nameOverride }}
    template:
        metadata:
            labels:
                app: {{ .Values.headlessexample.nameOverride }}
        spec:
            {{- with .Values.nodeSelector }}
            nodeSelector:
            {{- toYaml . | nindent 8 }}
            {{- end }}
            containers:
            - name: {{ .Values.headlessexample.nameOverride }}
              image: "{{ .Values.headlessexample.image.registry }}/{{ .Values.headlessexample.image.repository }}:{{ .Values.headlessexample.image.tag }}"
              imagePullPolicy: {{ .Values.headlessexample.image.pullPolicy }}
              command: ["nginx", "-g", "daemon off;"]
              envFrom:
                - configMapRef:
                    name: {{ .Values.headlessexample.nameOverride }}
            {{- if .Values.headlessexample.image.pullSecrets}}
            imagePullSecrets:
              - name: {{ .Values.headlessexample.image.pullSecrets }}
            {{- end }}
{{- end }}
