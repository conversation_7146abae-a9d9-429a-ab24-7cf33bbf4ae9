{{- $name := .Release.Name }}
{{- $namespace:= .Release.Namespace }}
{{- if .Values.headlessexample.enabled }}
apiVersion: v1
kind: ConfigMap
metadata:
    name: {{ .Values.headlessexample.nameOverride }}
    namespace: {{ .Release.Namespace | quote }}
    labels:
      app: {{ .Values.headlessexample.nameOverride }}
data:
  {{- range $key, $value := .Values.headlessexample.applicationConfig }}
  {{- if $value }}
  {{ $key }}: {{ $value | quote }}
  {{- end }}
  {{- end }}
{{- end }}