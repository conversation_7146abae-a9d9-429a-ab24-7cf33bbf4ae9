## Postg<PERSON> params
postgresql:
  image:
    repository: sibedge/postgres-plv8
    tag: 15.3-3.1.7
  enabled: true
  auth:
    username: admin
    password: admin
    postgresPassword: admin
    database: postgres
  # Local dev purpose
  persistence:
    existingClaim: postgresql-pv-claim
  volumePermissions:
    enabled: true

backoffice:
  enabled: true
  replicas: 1
  strategyType: RollingUpdate
  updateStrategy:
    maxSurge: 1
    maxUnavailable: "0"
  nameOverride: backoffice
  service:
    port: 80
    type: ClusterIP
    protocol: TCP
  image:
    registry: ghcr.io
    repository: "ballerine-io/backoffice"
    pullPolicy: Always
    pullSecrets: ""
    tag: "dev"

headlessexample:
  enabled: true
  replicas: 1
  strategyType: RollingUpdate
  updateStrategy:
    maxSurge: 1
    maxUnavailable: "0"
  nameOverride: headlessexample
  service:
    port: 80
    type: ClusterIP
    protocol: TCP
  image:
    registry: ghcr.io
    repository: "ballerine-io/headless-example"
    pullPolicy: Always
    pullSecrets: ""
    tag: "dev"

workflowsdashboard:
  enabled: true
  replicas: 1
  strategyType: RollingUpdate
  updateStrategy:
    maxSurge: 1
    maxUnavailable: "0"
  nameOverride: workflowsdashboard
  service:
    port: 80
    type: ClusterIP
    protocol: TCP
  image:
    registry: ghcr.io
    repository: "ballerine-io/workflows-dashboard"
    pullPolicy: Always
    pullSecrets: ""
    tag: "dev"

websocketService:
  enabled: true
  replicas: 1
  strategyType: RollingUpdate
  updateStrategy:
    maxSurge: 1
    maxUnavailable: "0"
  nameOverride: websocketservice
  migration: true
  service:
    port: 3500
    type: ClusterIP
    protocol: TCP
  image:
    registry: ghcr.io
    repository: "ballerine-io/websocket-service"
    pullPolicy: Always
    tag: "dev"
  applicationConfig:
    PORT: 3500
    NODE_ENV: development
    COMPOSE_PROJECT_NAME: ballerine-x

workflowService:
  enabled: true
  replicas: 1
  strategyType: RollingUpdate
  updateStrategy:
    maxSurge: 1
    maxUnavailable: "0"
  nameOverride: workflowservice
  migration: true
  service:
    port: 3000
    type: ClusterIP
    protocol: TCP
  image:
    registry: ghcr.io
    repository: "ballerine-io/workflows-service"
    pullPolicy: Always
    pullSecrets: ""
    tag: "dev"
  applicationConfig:
    BCRYPT_SALT: "10"
    JWT_SECRET_KEY: "secret"
    JWT_EXPIRATION: "10d"
    DB_URL: ""
    DB_USER: ""
    DB_PASSWORD: ""
    DB_PORT: "5432"
    PORT: "3000"
    COMPOSE_PROJECT_NAME: "ballerine-x"
    SESSION_SECRET: "iGdnj4A0YOhj8dHJK7IWSvQKEZsG7P70FFehuddhFPjtg/bSkzFejYILk4Xue6Ilx9y3IAwzR8pV1gb4"
    BACKOFFICE_CORS_ORIGIN: "http://localhost:5137"
    HEADLESS_EXAMPLE_CORS_ORIGIN: "http://localhost:5173"
    API_KEY: "secret"
    NODE_ENV: "development"
    SENTRY_DSN: ""
    WEBHOOK_URL: ""
    WEBHOOK_SECRET: "webhook_secret"
    WORKFLOW_DASHBOARD_CORS_ORIGIN: "http://localhost:5200"
    KYB_EXAMPLE_CORS_ORIGIN: "http://localhost:5201"
