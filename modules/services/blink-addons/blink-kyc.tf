module "postgresql" {
  source = "../../infra/vendor/tf/postgresql/gcp"

  gcp_project       = local.gcp_project
  region            = local.gcp_region
  vpc_name          = local.vpc_name
  instance_name     = local.kyc_pg_instance_name
  highly_available  = local.ha_pg
  databases         = local.kyc_databases
  tier              = local.pg_tier
  big_query_viewers = local.big_query_viewers
  database_version  = "POSTGRES_15"
}

resource "kubernetes_secret" "blink_kyc_smoketest" {
  metadata {
    name      = "blink-kyc-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    blink_kyc_endpoint = "https://${local.blink_kyc_host}"
    blink_kyc_port     = 443
  }
}

resource "hydra_oauth2_client" "blink_kyc" {
  client_name                = "blink_kyc"
  grant_types                = ["client_credentials"]
  response_types             = ["token"]
  token_endpoint_auth_method = "client_secret_basic"
  scopes                     = ["editor"]
}

resource "kubernetes_secret" "blink_kyc" {
  metadata {
    name      = "blink-kyc"
    namespace = kubernetes_namespace.blink_addons.metadata[0].name
  }
  data = {
    admin-api-client-secret : hydra_oauth2_client.blink_fiat.client_secret
    pg-con : module.postgresql.creds["kyc"].conn
    onfido-api-token : local.blink_kyc_onfido_api_token
  }
}

resource "helm_release" "blink_kyc" {
  name      = "blink-kyc"
  chart     = "${path.module}/vendor/blink-kyc/chart"
  namespace = kubernetes_namespace.blink_addons.metadata[0].name

  dependency_update = true
  values = [
    templatefile("${path.module}/blink-kyc-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/blink-kyc/git-ref/ref")
      host : local.blink_kyc_host
      admin_graphql_endpoint : local.admin_graphql_endpoint
      hydra_public_api : local.hydra_public_api
      oathkeeper_api : local.oathkeeper_api_endpoint
      onfido_workflow_id : local.blink_kyc_onfido_workflow_id
      admin_api_client_id : hydra_oauth2_client.blink_fiat.client_id
      notifications_api : local.notifications_api
      otel_exporter_otlp_endpoint : local.otel_exporter_otlp_endpoint
      tracing_prefix : local.name_prefix
    })
  ]
}
