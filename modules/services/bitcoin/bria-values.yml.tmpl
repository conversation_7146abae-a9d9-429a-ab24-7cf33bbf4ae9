# trigger_ref: ${trigger_ref}

bria:
  secrets:
    create: false

  replicas: ${bria_replicas}

  db:
    poolSize: 30

  api:
    service:
      type: LoadBalancer
      staticIP: "${bria_internal_ip}"
      annotations:
        networking.gke.io/load-balancer-type: "Internal"
  admin:
    service:
      type: LoadBalancer
      staticIP: "${bria_admin_internal_ip}"
      annotations:
        networking.gke.io/load-balancer-type: "Internal"

  app:
    blockchain:
      network: ${bitcoin_network}
      electrumUrl: ${fulcrum_endpoint}
    fees:
      mempoolSpace:
        url: "https://mempool.tk7.mempool.space"

  tracing:
    host: ${otel_host}
    serviceName: ${tracing_bria_service_name}

postgresql:
  enabled: false
