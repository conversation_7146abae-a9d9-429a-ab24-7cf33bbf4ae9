module "bria_pg" {
  source = "../../infra/vendor/tf/postgresql/gcp"

  gcp_project             = local.gcp_project
  region                  = local.gcp_region
  vpc_name                = local.vpc_name
  instance_name           = local.bria_pg_instance_name
  highly_available        = local.ha_pg
  databases               = local.bria_databases
  big_query_viewers       = local.big_query_viewers
  tier                    = local.pg_tier
  enable_detailed_logging = false
}

resource "kubernetes_secret" "bria" {
  metadata {
    name      = "bria"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }

  data = {
    pg-con : module.bria_pg.creds["bria"].conn
    signer-encryption-key : local.bria_signer_encryption_key
  }
}

resource "helm_release" "bria" {
  name      = "bria"
  chart     = "${path.module}/vendor/bria/chart"
  namespace = kubernetes_namespace.bitcoin.metadata[0].name

  values = [
    templatefile("${path.module}/bria-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/bria/git-ref/ref")
      bria_replicas : local.bria_replicas
      bitcoin_network : local.bitcoin_network
      fulcrum_endpoint : local.fulcrum_endpoint
      bria_internal_ip : local.bria_internal_ip
      bria_admin_internal_ip : local.bria_admin_internal_ip
      otel_host : local.otel_host
      tracing_bria_service_name : local.tracing_bria_service_name
    }),
    file("${path.module}/bria-blocked-addresses.yml"),
    file("${path.module}/../../../gcp/${local.name_prefix}/bitcoin/bria-scaling.yml")
  ]

  depends_on        = [kubernetes_secret.bria]
  dependency_update = true
}

provider "briaadmin" {
  endpoint = local.bria_admin_endpoint
  api_key  = local.bria_admin_api_key
}

resource "briaadmin_account" "main" {
  name = local.bria_account_name

  depends_on = [
    helm_release.bria
  ]
}

provider "bria" {
  api_key  = briaadmin_account.main.api_key
  endpoint = local.bria_endpoint
}

resource "bria_profile" "galoy" {
  name = "${local.name_prefix}-galoy"
}

resource "bria_api_key" "galoy" {
  profile = bria_profile.galoy.name
}

resource "bria_profile" "stablesats" {
  name = "${local.name_prefix}-stablesats"
  spending_policy {
    allowed_payout_addresses = [local.okex_exchange_address]
    max_payout_sats          = ********* # 2BTC
  }
}

resource "bria_api_key" "stablesats" {
  profile = bria_profile.stablesats.name
}

resource "bria_xpub" "bitcoind_onchain" {
  name       = "bitcoind_onchain"
  xpub       = local.bitcoind_signer_xpub
  derivation = "m/84h/0h/0h"
}

resource "bria_signer_config" "bitcoind_onchain" {
  xpub = bria_xpub.bitcoind_onchain.id
  bitcoind {
    endpoint     = local.onchain_bitcoind_endpoint
    rpc_user     = "rpcuser"
    rpc_password = random_password.bitcoind_onchain_rpcpassword.result
  }
}

resource "bria_wallet" "hot" {
  name = local.bria_hot_wallet_name
  keychain {
    wpkh {
      xpub = bria_xpub.bitcoind_onchain.id
    }
  }
}

resource "bria_static_address" "stablesats" {
  wallet      = bria_wallet.hot.name
  external_id = local.stablesats_withdraw_address
}

# We're not using the bria_wallet resource for the cold wallet anymore
# because it already exists in the Bria database but can't be imported
# Instead, we'll reference it by name in other resources

resource "bria_payout_queue" "fast" {
  name        = "fast"
  description = "Fast payout queue"

  config {
    tx_priority                      = "NEXT_BLOCK"
    interval_secs                    = 60
    consolidate_deprecated_keychains = false
    cpfp_payouts_after_mins          = 60
    cpfp_payouts_after_blocks        = 6
    force_min_change_sats            = 123456
  }
}

resource "kubernetes_secret" "galoy_profile_key" {
  metadata {
    name      = "galoy-bria-creds"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }

  data = {
    "api-key" : bria_api_key.galoy.key
    "hot-wallet-name" : bria_wallet.hot.name
    "cold-wallet-name" : local.bria_cold_wallet_name
    "fast-queue-name" : bria_payout_queue.fast.name
  }
}

resource "kubernetes_secret" "stablesats_profile_key" {
  metadata {
    name      = "stablesats-bria-creds"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }

  data = {
    "api-key" : bria_api_key.stablesats.key
    "address-external-id" : bria_static_address.stablesats.external_id
    "hot-wallet-name" : bria_wallet.hot.name
    "queue-name" : bria_payout_queue.fast.name
  }
}

data "kubernetes_secret" "lnd1_credentials" {
  metadata {
    name      = "lnd1-credentials"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }

  depends_on = [
    helm_release.lnd1
  ]
}

# resource "bria_xpub" "legacy_lnd" {
#   name       = "legacy-onchain-lnd"
#   xpub       = data.kubernetes_secret.lnd1_credentials.data["xpub"]
#   derivation = "m/84h/0h/0h"
# }

# resource "bria_wallet" "legacy_hot" {
#   name = local.bria_legacy_hot_wallet_name
#   keychain {
#     wpkh {
#       xpub = bria_xpub.legacy_lnd.id
#     }
#   }
# }

resource "bria_profile" "dev" {
  name = "${local.name_prefix}-dev"
}

resource "bria_api_key" "dev" {
  profile = bria_profile.dev.name
}

output "bria_api_key" {
  value     = bria_api_key.dev.key
  sensitive = true
}

output "stablesats_address" {
  value = bria_static_address.stablesats.address
}

terraform {
  required_providers {
    briaadmin = {
      source  = "galoymoney/briaadmin"
      version = "0.0.7"
    }
    bria = {
      source  = "galoymoney/bria"
      version = "0.0.14"
    }
  }
}
