resource "random_password" "specter_password" {
  length  = 20
  special = false
}

resource "kubernetes_secret" "specterpassword" {
  metadata {
    name      = "specter-password"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }

  data = {
    password = random_password.specter_password.result
  }
}

resource "kubernetes_secret" "specter_smoketest" {
  metadata {
    name      = "specter-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    specter_endpoint = var.specter_dns
    specter_port     = 80
  }
}

resource "helm_release" "specter" {
  name       = "specter"
  chart      = "${path.module}/vendor/specter/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = kubernetes_namespace.bitcoin.metadata[0].name

  values = [
    templatefile("${path.module}/specter-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/specter/git-ref/ref")
      host : local.specter_dns
      secret_name : "${local.name_prefix}-specter-tls"
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/bitcoin/specter-scaling.yml")
  ]

  depends_on = [
    helm_release.bitcoind
  ]
}
