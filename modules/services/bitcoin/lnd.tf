resource "random_password" "lnd1_pass" {
  length  = 20
  special = false
}

resource "random_password" "lnd2_pass" {
  length  = 20
  special = false
}

resource "kubernetes_secret" "lnd1_pass" {
  metadata {
    name      = "lnd1-pass"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }

  data = {
    password = local.lnd1_pass == "" ? random_password.lnd1_pass.result : local.lnd1_pass
  }
}

resource "kubernetes_secret" "lnd2_pass" {
  metadata {
    name      = "lnd2-pass"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }

  data = {
    password = random_password.lnd2_pass.result
  }
}

resource "kubernetes_secret" "lnd1_smoketest" {
  metadata {
    name      = "lnd1-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    lnd_api_endpoint = "lnd1.${local.bitcoin_namespace}.svc.cluster.local"
    lnd_p2p_endpoint = local.lnd1_public_ip
  }
}

resource "kubernetes_secret" "lnd2_smoketest" {
  metadata {
    name      = "lnd2-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    lnd_api_endpoint = "lnd2.${local.bitcoin_namespace}.svc.cluster.local"
    lnd_p2p_endpoint = local.lnd2_public_ip
  }
}

resource "tls_private_key" "lnd1" {
  algorithm   = "ECDSA"
  ecdsa_curve = "P256"
}

resource "tls_self_signed_cert" "lnd1" {
  private_key_pem = tls_private_key.lnd1.private_key_pem

  subject {
    common_name  = "lnd1"
    organization = "Galoy Inc."
  }

  validity_period_hours = 365 * 24

  allowed_uses = [
    "key_encipherment",
    "digital_signature",
    "server_auth",
  ]

  dns_names = [
    "localhost",
    "lnd1.${local.bitcoin_namespace}.svc.cluster.local",
  ]

  ip_addresses = ["127.0.0.1", "0.0.0.0", local.lnd1_internal_ip]

  early_renewal_hours = 24 * 14 # 2 weeks
}

resource "kubernetes_secret" "lnd1" {
  metadata {
    name      = "lnd1-tls"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }

  data = {
    "tls.cert" = tls_self_signed_cert.lnd1.cert_pem
    "tls.key"  = tls_private_key.lnd1.private_key_pem
  }
}

resource "tls_private_key" "lnd2" {
  algorithm   = "ECDSA"
  ecdsa_curve = "P256"
}

resource "tls_self_signed_cert" "lnd2" {
  private_key_pem = tls_private_key.lnd2.private_key_pem

  subject {
    common_name  = "lnd2"
    organization = "Galoy Inc."
  }

  validity_period_hours = 420 * 24

  allowed_uses = [
    "key_encipherment",
    "digital_signature",
    "server_auth",
  ]

  dns_names = [
    "localhost",
    "lnd2.${local.bitcoin_namespace}.svc.cluster.local",
  ]

  ip_addresses        = ["127.0.0.1", "0.0.0.0", local.lnd2_internal_ip]
  early_renewal_hours = 24 * 14 # 2 weeks
}

resource "kubernetes_secret" "lnd2" {
  metadata {
    name      = "lnd2-tls"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }

  data = {
    "tls.cert" = tls_self_signed_cert.lnd2.cert_pem
    "tls.key"  = tls_private_key.lnd2.private_key_pem
  }
}

resource "helm_release" "lnd1" {
  name       = "lnd1"
  chart      = "${path.module}/vendor/lnd1/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = kubernetes_namespace.bitcoin.metadata[0].name

  dependency_update = true
  timeout           = 600

  values = [
    templatefile("${path.module}/lnd-values.yml.tmpl", {
      lnd_public_ip : local.lnd1_public_ip
      lnd_min_channel_size : local.lnd_min_channel_size
      lnd_base_fee : local.lnd_base_fee
      lnd_fee_rate : local.lnd_fee_rate
      bitcoind_endpoint : local.bitcoind_endpoint
      bitcoind_rpc_pass_secret_name : local.bitcoind_rpc_pass_secret_name
      bitcoind_zmq_block_endpoint : local.bitcoind_zmq_block_endpoint
      bitcoind_zmq_tx_endpoint : local.bitcoind_zmq_tx_endpoint
      bitcoin_network : local.bitcoin_network
      bitcoind_rpc_port : local.rpc_port
      trigger_ref : file("${path.module}/vendor/lnd1/git-ref/ref")
      tls_checksum : sha256(tls_self_signed_cert.lnd1.cert_pem)
      lnd_internal_ip : local.lnd1_internal_ip
      lnd_node_alias : local.lnd1_alias
      existing_claim : local.lnd1_existing_claim
      lnd_image_tag : local.lnd1_image_tag
      storage_class : local.lnd1_storage_class
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/bitcoin/lnd-scaling.yml")
  ]

  depends_on = [
    helm_release.bitcoind
  ]
}

resource "helm_release" "lnd2" {
  name       = "lnd2"
  chart      = "${path.module}/vendor/lnd2/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = kubernetes_namespace.bitcoin.metadata[0].name

  dependency_update = true
  timeout           = 600
  values = [
    templatefile("${path.module}/lnd-values.yml.tmpl", {
      lnd_public_ip : local.lnd2_public_ip
      lnd_min_channel_size : local.lnd_min_channel_size
      lnd_base_fee : local.lnd_base_fee
      lnd_fee_rate : local.lnd_fee_rate
      bitcoind_endpoint : local.bitcoind2_endpoint
      bitcoind_rpc_pass_secret_name : local.bitcoind2_rpc_pass_secret_name
      bitcoind_zmq_block_endpoint : local.bitcoind2_zmq_block_endpoint
      bitcoind_zmq_tx_endpoint : local.bitcoind2_zmq_tx_endpoint
      bitcoin_network : local.bitcoin_network
      bitcoind_rpc_port : local.rpc_port
      trigger_ref : file("${path.module}/vendor/lnd2/git-ref/ref")
      tls_checksum : sha256(tls_self_signed_cert.lnd2.cert_pem)
      lnd_node_alias : local.lnd2_alias
      lnd_internal_ip : local.lnd2_internal_ip
      existing_claim : local.lnd2_existing_claim
      lnd_image_tag : local.lnd2_image_tag
      storage_class : local.lnd2_storage_class
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/bitcoin/lnd-scaling.yml")
  ]

  depends_on = [
    helm_release.lnd1
  ]
}
