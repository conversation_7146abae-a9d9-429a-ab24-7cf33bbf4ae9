resource "kubernetes_namespace" "bitcoin" {
  metadata {
    name = local.bitcoin_namespace
  }
}

resource "kubernetes_storage_class" "regional_pd" {
  metadata {
    name = "regional-pd"
  }
  storage_provisioner = "pd.csi.storage.gke.io"
  reclaim_policy      = "Retain"
  parameters = {
    type             = "pd-standard"
    replication-type = "regional-pd"
  }
  volume_binding_mode = "Immediate"
}

# TODO: rename bitcoind to bitcoind1
# config for bitcoind
resource "random_password" "bitcoind_rpcpassword" {
  length  = 20
  special = false
}

resource "kubernetes_secret" "rpcpassword" {
  metadata {
    name      = "bitcoind-rpcpassword"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }

  data = {
    password = random_password.bitcoind_rpcpassword.result
  }
}

resource "kubernetes_secret" "bitcoind_smoketest" {
  metadata {
    name      = "bitcoind-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    bitcoind_rpcpassword = random_password.bitcoind_rpcpassword.result
    bitcoind_endpoint    = "bitcoind.${local.bitcoin_namespace}.svc.cluster.local"
    bitcoind_port        = local.rpc_port
    bitcoind_user        = "rpcuser"
  }
}

resource "kubernetes_persistent_volume_claim" "bitcoind" {
  metadata {
    name      = "bitcoind"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }
  spec {
    access_modes = ["ReadWriteOnce"]
    resources {
      requests = {
        storage = local.bitcoind_pvc_size
      }
    }
    storage_class_name = kubernetes_storage_class.regional_pd.metadata[0].name
  }
}

resource "helm_release" "bitcoind" {
  name       = "bitcoind"
  chart      = "${path.module}/vendor/bitcoind/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = kubernetes_namespace.bitcoin.metadata[0].name

  values = [
    templatefile("${path.module}/bitcoind-values.yml.tmpl", {
      bitcoin_network : local.bitcoin_network
      existing_claim : kubernetes_persistent_volume_claim.bitcoind.metadata[0].name
      rpc_port : local.rpc_port
      p2p_port : local.p2p_port
      trigger_ref : file("${path.module}/vendor/bitcoind/git-ref/ref")
      sync_from_bucket : local.sync_from_bucket
    }),
    local.bitcoin_network == "testnet" ? file("${path.module}/bitcoind-testnet-values.yml") : "",
    local.bitcoin_network == "signet" ? file("${path.module}/bitcoind-signet-values.yml") : "",
    file("${path.module}/../../../gcp/${local.name_prefix}/bitcoin/bitcoind-scaling.yml")
  ]

  depends_on = [
    kubernetes_secret.rpcpassword
  ]
}


# config for bitcoind 2

resource "random_password" "bitcoind2_rpcpassword" {
  length  = 20
  special = false
}

resource "kubernetes_secret" "bitcoind2_rpcpassword" {
  metadata {
    name      = "bitcoind2-rpcpassword"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }

  data = {
    password = random_password.bitcoind2_rpcpassword.result
  }
}

resource "kubernetes_secret" "bitcoind2_smoketest" {
  metadata {
    name      = "bitcoind2-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    bitcoind_rpcpassword = random_password.bitcoind2_rpcpassword.result
    bitcoind_endpoint    = "bitcoind2.${local.bitcoin_namespace}.svc.cluster.local"
    bitcoind_port        = local.rpc_port
    bitcoind_user        = "rpcuser"
  }
}

resource "kubernetes_persistent_volume_claim" "bitcoind2" {
  metadata {
    name      = "bitcoind2"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }
  spec {
    access_modes = ["ReadWriteOnce"]
    resources {
      requests = {
        storage = local.bitcoind2_pvc_size
      }
    }
    storage_class_name = kubernetes_storage_class.regional_pd.metadata[0].name
  }
}

resource "helm_release" "bitcoind2" {
  name      = "bitcoind2"
  chart     = "${path.module}/vendor/bitcoind/chart"
  namespace = kubernetes_namespace.bitcoin.metadata[0].name

  values = [
    templatefile("${path.module}/bitcoind-values.yml.tmpl", {
      bitcoin_network : local.bitcoin_network
      existing_claim : kubernetes_persistent_volume_claim.bitcoind2.metadata[0].name
      rpc_port : local.rpc_port
      p2p_port : local.p2p_port
      trigger_ref : file("${path.module}/vendor/bitcoind/git-ref/ref")
      sync_from_bucket : local.sync_from_bucket
    }),
    local.bitcoin_network == "testnet" ? file("${path.module}/bitcoind-testnet-values.yml") : "",
    local.bitcoin_network == "signet" ? file("${path.module}/bitcoind-signet-values.yml") : "",
    file("${path.module}/../../../gcp/${local.name_prefix}/bitcoin/bitcoind-scaling.yml")
  ]
}

# config for bitcoind-onchain
resource "random_password" "bitcoind_onchain_rpcpassword" {
  length  = 20
  special = false
}

resource "kubernetes_secret" "bitcoind_onchain_rpcpassword" {
  metadata {
    name      = "bitcoind-onchain-rpcpassword"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }

  data = {
    password = random_password.bitcoind_onchain_rpcpassword.result
  }
}

resource "kubernetes_secret" "bitcoind_signer_descriptor" {
  metadata {
    name      = "bitcoind-signer-descriptor"
    namespace = local.bitcoin_namespace
  }

  data = {
    descriptor_json_base64 = base64encode(local.bitcoind_signer_descriptor)
  }
}

resource "kubernetes_secret" "bitcoind_onchain_smoketest" {
  metadata {
    name      = "bitcoind-onchain-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    bitcoind_rpcpassword = random_password.bitcoind_onchain_rpcpassword.result
    bitcoind_endpoint    = "bitcoind-onchain.${local.bitcoin_namespace}.svc.cluster.local"
    bitcoind_port        = local.rpc_port
    bitcoind_user        = "rpcuser"
  }
}

resource "kubernetes_persistent_volume_claim" "bitcoind_onchain" {
  metadata {
    name      = "bitcoind-onchain"
    namespace = kubernetes_namespace.bitcoin.metadata[0].name
  }
  spec {
    access_modes = ["ReadWriteOnce"]
    resources {
      requests = {
        storage = local.bitcoind_onchain_pvc_size
      }
    }
    storage_class_name = kubernetes_storage_class.regional_pd.metadata[0].name
  }
}

resource "helm_release" "bitcoind_onchain" {
  name       = "bitcoind-onchain"
  chart      = "${path.module}/vendor/bitcoind/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = kubernetes_namespace.bitcoin.metadata[0].name

  values = [
    templatefile("${path.module}/bitcoind-values.yml.tmpl", {
      bitcoin_network : local.bitcoin_network
      existing_claim : kubernetes_persistent_volume_claim.bitcoind_onchain.metadata[0].name
      rpc_port : local.rpc_port
      p2p_port : local.p2p_port
      trigger_ref : file("${path.module}/vendor/bitcoind/git-ref/ref")
      sync_from_bucket : local.bitcoind_onchain_sync_from_bucket
    }),
    file("${path.module}/bitcoind-onchain-values.yml"),
    local.bitcoin_network == "testnet" ? file("${path.module}/bitcoind-testnet-values.yml") : "",
    local.bitcoin_network == "signet" ? file("${path.module}/bitcoind-signet-values.yml") : "",
    file("${path.module}/../../../gcp/${local.name_prefix}/bitcoin/bitcoind-onchain-scaling.yml")
  ]

  depends_on = [
    kubernetes_secret.bitcoind_onchain_rpcpassword,
    kubernetes_secret.bitcoind_signer_descriptor
  ]
}
