apiVersion: v1
kind: Service
metadata:
  name: {{ include "lnd.fullname" . }}
  labels:
    {{- include "lnd.labels" . | nindent 4 }}
  {{- with .Values.apiService.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ .Values.apiService.type }}
  {{- if and (eq .Values.apiService.type "LoadBalancer") (.Values.apiService.staticIP) }}
  loadBalancerIP: {{ .Values.apiService.staticIP }}
  {{- end }}
  ports:
    - name: rpc
      port: {{ .Values.apiService.ports.rpc }}
    - name: rest
      port: {{ .Values.apiService.ports.rest }}
  selector:
    {{- include "lnd.selectorLabels" . | nindent 4 }}
