{{ $checksum := include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
{{ $exportChecksum := include (print $.Template.BasePath "/export-secrets-configmap.yaml") . | sha256sum }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "lnd.fullname" . }}
  labels:
    {{- include "lnd.labels" . | nindent 4 }}
    {{- if .Values.kubemonkey.enabled }}
    kube-monkey/enabled: enabled
    kube-monkey/identifier: {{ include "lnd.fullname" . }}
    kube-monkey/kill-mode: fixed
    kube-monkey/kill-value: "1"
    kube-monkey/mtbf: "3"
    {{- end }}
    {{- with .Values.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  serviceName: {{ include "lnd.fullname" . }}
  selector:
    matchLabels:
      {{- include "lnd.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/config: {{ $checksum }}
        checksum/exportSecrets: {{ $exportChecksum }}
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "lnd.selectorLabels" . | nindent 8 }}
        {{- if .Values.kubemonkey.enabled }}
        kube-monkey/enabled: enabled
        kube-monkey/identifier: {{ include "lnd.fullname" . }}
        {{- end }}
        {{- with .Values.labels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      serviceAccountName: {{ include "lnd.serviceAccountName" . }}
      terminationGracePeriodSeconds: {{ .Values.terminationGracePeriodSeconds }}
      initContainers:
        - name: copy-lnd-config
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command:
          - 'sh'
          - '-c'
          - |
            cat <<EOF > /root/.lnd/lnd.conf
            bitcoind.rpcpass=$(cat /rpcpassword/password)
            $(cat /configmap/lnd.conf)
            EOF
          volumeMounts:
            - name: configmap
              mountPath: /configmap
            - name: config
              mountPath: /root/.lnd/
            - name: rpcpassword
              mountPath: /rpcpassword
      containers:
        {{- if and (ne .Values.global.network "regtest") .Values.autoGenerateSeed.enabled }}
        - name: init-wallet
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          command: ['/bin/sh']
          args:
          - '-c'
          - |
            set -e
            if lncli -n {{ $.Values.global.network}} state | grep "WAITING_TO_START" ; then
              exit 0;
            fi
            lncli -n {{ $.Values.global.network}} state
            if [ ! -f /root/.lnd/data/chain/bitcoin/${NETWORK}/admin.macaroon ]; then
              while ! test -f /root/.lnd/tls.cert; do sleep 1; done
              apk update; apk add expect
              /home/<USER>/walletInit.exp ${NETWORK} $LND_PASS
            fi
            # Trap is required otherwise signals are not handled
            # https://stackoverflow.com/questions/45148381/why-cant-i-ctrl-c-a-sleep-infinity-in-docker-when-it-runs-as-pid-1
            trap "echo shutting down; exit 0" SIGKILL SIGTERM
            while true; do sleep 1; done
          env:
          - name: LND_PASS
            valueFrom:
              secretKeyRef:
                name: {{ printf "%s-pass" (include "lnd.fullname" .) }}
                key: password
          - name: NETWORK
            valueFrom:
              secretKeyRef:
                name: network
                key: network
          volumeMounts:
          - name: wallet-init
            mountPath: /home/<USER>/walletInit.exp
            subPath: walletInit.exp
          - name: lnd-storage
            mountPath: /root/.lnd
          {{- if not .Values.autoGenerateTls.enabled }}
          - name: tls-cert
            mountPath: /root/.lnd/tls.cert
            subPath: tls.cert
          {{- end }}
        {{- end }}
        - name: tor
          image: osminogin/tor-simple
          securityContext:
            runAsUser: 100
          command: ['/bin/sh']
          volumeMounts:
          - name: tor-cookie-auth
            mountPath: /var/lib/tor/auth
          args:
          - '-c'
          - |
            sed 's/SocksPort 0.0.0.0:9050/SocksPort 127.0.0.1:9050/g' /etc/tor/torrc > ~/torrc
            cat <<EOF > ~/torrc
            ControlPort 9051
            CookieAuthentication 1
            CookieAuthFile /var/lib/tor/auth/cookie
            CookieAuthFileGroupReadable 1
            $(cat ~/torrc)
            EOF
            exec tor -f ~/torrc
        - name: export-secrets
          image: "{{ .Values.sidecarImage.repository }}@{{ .Values.sidecarImage.digest }}"
          command: ['/bin/sh']
          args:
          - '-c'
          - |
            lncli -n=$NETWORK getinfo
            while [[ $? != 0 ]]; do sleep 1; lncli -n=$NETWORK getinfo; done
            /home/<USER>/exportSecrets.sh
            trap "echo shutting down; exit 0" SIGKILL SIGTERM
            while true; do sleep 1; done
          env:
          - name: NETWORK
            valueFrom:
              secretKeyRef:
                name: network
                key: network
          volumeMounts:
          - name: export-secrets
            mountPath: /home/<USER>/exportSecrets.sh
            subPath: exportSecrets.sh
          - name: lnd-storage
            mountPath: /root/.lnd
          {{- if not .Values.autoGenerateTls.enabled }}
          - name: tls-cert
            mountPath: /root/.lnd/tls.cert
            subPath: tls.cert
          {{- end }}
        - name: lnd
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          ports:
            - name: rpc
              containerPort: {{ .Values.apiService.ports.rpc }}
            - name: p2p
              containerPort: {{ .Values.p2pService.port }}
            - name: rest
              containerPort: {{ .Values.apiService.ports.rest }}
          volumeMounts:
            - name: tor-cookie-auth
              mountPath: /var/lib/tor/auth
            - name: lnd-storage
              mountPath: /root/.lnd
            - name: config
              mountPath: /root/.lnd/lnd.conf
              subPath: lnd.conf
            {{- if ne .Values.global.network "regtest" }}
            - name: lnd-password
              mountPath: /tmp/lnd-pass
              subPath: password
            {{- end }}
            {{- if not .Values.autoGenerateTls.enabled }}
            - name: tls-cert
              mountPath: /root/.lnd/tls.cert
              subPath: tls.cert
            - name: tls-cert
              mountPath: /root/.lnd/tls.key
              subPath: tls.key
            {{- end }}
          startupProbe:
            exec:
              command:
              - sh
              - -c
              - "lncli -n {{ $.Values.global.network}} state | grep -q SERVER_ACTIVE"
            failureThreshold: 540
            periodSeconds: 10
          readinessProbe:
            exec:
              command:
              - sh
              - -c
              - "lncli -n {{ $.Values.global.network}} state | grep -q SERVER_ACTIVE"
            periodSeconds: 5
            timeoutSeconds: 2
            failureThreshold: 3
          livenessProbe:
            exec:
              command:
              - sh
              - -c
              - "lncli -n {{ $.Values.global.network}} state | grep -q SERVER_ACTIVE && lncli -n {{ $.Values.global.network}} getinfo"
            periodSeconds: 5
            timeoutSeconds: 2
            failureThreshold: 3
            initialDelaySeconds: 600
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
        {{- if ne .Values.global.network "regtest" }}
        - name: wallet-init
          configMap:
            name: {{ printf "%s-wallet-init" (include "lnd.fullname" .) }}
            defaultMode: 0777
        {{- end }}
        {{- if not .Values.autoGenerateTls.enabled }}
        - name: tls-cert
          secret:
            secretName: {{ printf "%s-tls" (include "lnd.fullname" .) }}
            items:
            - key: tls.cert
              path: tls.cert
            - key: tls.key
              path: tls.key
        {{- end }}
        - name: export-secrets
          configMap:
            name: {{ printf "%s-export-secrets" (include "lnd.fullname" .) }}
            defaultMode: 0777
        - name: config
          emptyDir: {}
        - name: rpcpassword
          secret:
            secretName: {{ .Values.bitcoindRpcPassSecretName }}
        - name: configmap
          configMap:
            name: {{ include "lnd.fullname" . }}
        {{- if ne .Values.global.network "regtest" }}
        - name: lnd-password
          secret:
            secretName: {{ printf "%s-pass" (include "lnd.fullname" .) }}
        {{- end }}
        - name: lnd-storage
        {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ .Values.persistence.existingClaim | default (include "lnd.fullname" $) }}
        {{- else }}
          emptyDir: {}
        {{- end }}
        - name: tor-cookie-auth
          emptyDir: {}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
