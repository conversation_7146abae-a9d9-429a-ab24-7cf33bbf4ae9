apiVersion: v1
kind: Service
metadata:
  name: {{ include "lnd.fullname" . }}-p2p
  labels:
    {{- include "lnd.labels" . | nindent 4 }}
spec:
  type: {{ .Values.p2pService.type }}
  {{- if and (eq .Values.p2pService.type "LoadBalancer") (.Values.p2pService.staticIP) }}
  loadBalancerIP: {{ .Values.p2pService.staticIP }}
  {{- end }}
  ports:
    - name: p2p
      port: {{ .Values.p2pService.port }}
  selector:
    {{- include "lnd.selectorLabels" . | nindent 4 }}
