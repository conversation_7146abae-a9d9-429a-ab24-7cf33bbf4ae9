global:
  network: mainnet
image:
  repository: lightninglabs/lnd
  tag: v0.18.5-beta
  pullPolicy: IfNotPresent
sidecarImage:
  repository: us.gcr.io/galoy-org/lnd-sidecar
  digest: "sha256:e0ccf6d340a99cecc39c4d7ba52014c767220069b5b1b314aceb34253c4fa79f"
  git_ref: 1cb5596
kubemonkey:
  enabled: false
configmap:
  customValues: []
serviceAccount:
  # Specifies whether a service account should be created
  create: true
podAnnotations:
  prometheus.io/path: /metrics
  prometheus.io/port: "9092"
  prometheus.io/scrape: "true"
terminationGracePeriodSeconds: 600
resources: {}
apiService:
  # On google cloud, static ip can be created using `gcloud compute addresses create`
  staticIP: ""
  type: ClusterIP
  annotations: {}
  ports:
    rpc: 10009
    rest: 8080
p2pService:
  # On google cloud, static ip can be created using `gcloud compute addresses create`
  staticIP: ""
  type: ClusterIP
  port: 9735
persistence:
  enabled: true
  ## database data Persistent Volume Storage Class
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  ##
  # storageClass: "-"
  accessMode: ReadWriteOnce
  size: 20Gi
affinity:
  podAffinity:
    preferredDuringSchedulingIgnoredDuringExecution:
      - weight: 100
        podAffinityTerm:
          labelSelector:
            matchExpressions:
              - key: app.kubernetes.io/name
                operator: In
                values:
                  - bitcoind
          topologyKey: kubernetes.io/hostname
walletPassword: ""
secrets:
  create: true
lndGeneralConfig:
  - restlisten=0.0.0.0:8080
  - rpclisten=0.0.0.0:10009
  - listen=0.0.0.0:9735
  - prometheus.listen=0.0.0.0:9092
  - bitcoin.node=bitcoind
  - bitcoind.rpcuser=rpcuser
  - bitcoind.zmqpubrawblock=tcp://bitcoind:28332
  - bitcoind.zmqpubrawtx=tcp://bitcoind:28333
  - tlsextradomain=lnd
  - accept-keysend=1
  - allow-circular-route=1
  - stagger-initial-reconnect=1
  - protocol.wumbo-channels=1
  - maxchansize=500000000
  - bitcoin.timelockdelta=60
  - default-remote-max-htlcs=50
  - debuglevel=info
  - prometheus.enable=1
  - gc-canceled-invoices-on-the-fly=true
  - gc-canceled-invoices-on-startup=true
  - tor.active=true
  - tor.v3=true
  - tor.skip-proxy-for-clearnet-targets=true
bitcoindRpcPassSecretName: bitcoind-rpcpassword
rbac:
  create: true
autoGenerateSeed:
  enabled: false
autoGenerateTls:
  enabled: true
