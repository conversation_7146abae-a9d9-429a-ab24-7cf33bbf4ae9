global:
  network: mainnet
  service:
    ports:
      rpc: 8332

secrets:
  create: true

replicaCount: 1

image:
  repository: lncm/bitcoind
  pullPolicy: IfNotPresent
  tag: v27.0

extraInitContainers: []
sidecarContainers: []

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  fsGroup: 2000

securityContext:
  # capabilities:
  #   drop:
  #   - ALL
  readOnlyRootFilesystem: true
  runAsNonRoot: true
  runAsUser: 1000
  runAsGroup: 3000

service:
  type: ClusterIP
  ports:
    zmqpubrawtx: 28333
    zmqpubrawblock: 28332
    p2p: 8333
    metrics: 3000

ingress:
  enabled: false
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths: []
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # We recommend using the following values due to the resource intensive nature of bitcoind
  # limits:
  #   cpu: 3000m
  #   memory: 5120Mi
  # requests:
  #   cpu: 100m
  #   memory: 4096Mi

persistence:
  enabled: true
  ## database data Persistent Volume Storage Class
  ## If defined, storageClassName: <storageClass>
  ## If set to "-", storageClassName: "", which disables dynamic provisioning
  ## If undefined (the default) or set to null, no storageClassName spec is
  ##   set, choosing the default provisioner.  (gp2 on AWS, standard on
  ##   GKE, AWS & OpenStack)
  ##
  # storageClass: "-"
  accessMode: ReadWriteOnce
  size: 750Gi

nodeSelector: {}

tolerations: []

affinity: {}

labels: {}

podLabels: {}

labels: {}

# TODO: Document the usage of generic and custom config to instruct where to put specific flags

bitcoindGenericConfig:
- debug=mempool
- debug=rpc
- shrinkdebugfile=1
- server=1
- txindex=1
- printtoconsole=1
- rpcuser=rpcuser
- zmqpubrawtx=tcp://0.0.0.0:28333
- zmqpubrawblock=tcp://0.0.0.0:28332
- blockfilterindex=1

# these flags need to be here and not in bitcoindGenericConfig because they have to be present under a separate section inside bitcoind.conf when in testnet/regtest mode
bitcoindCustomConfig:
  bind: 0.0.0.0
  rpcbind: 0.0.0.0
  rpcallowip: 0.0.0.0/0

descriptor:
  secretName: ""
  secretKey: ""

startupProbe:
  failureThreshold: 90
