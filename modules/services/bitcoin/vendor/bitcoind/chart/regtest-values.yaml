# Example of values for regtest bitcoind.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  network: regtest
  service:
    ports: 
      rpc: 18443

secrets:
  create: false

persistence:
  enabled: false

service:
  type: ClusterIP
  ports:
    zmqpubrawtx: 28333
    zmqpubrawblock: 28332
    p2p: 18444

# these flags need to be here and not in bitcoindGenericConfig because they have to be present under a separate section inside bitcoind.conf when in testnet/regtest mode
bitcoindCustomConfig:
  bind: 0.0.0.0
  rpcbind: 0.0.0.0
  rpcallowip: 0.0.0.0/0
  fallbackfee: 0.0002
