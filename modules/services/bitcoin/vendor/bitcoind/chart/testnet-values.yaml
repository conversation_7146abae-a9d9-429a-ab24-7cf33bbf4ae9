# Example of values for testnet bitcoind.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
global:
  network: testnet
  service:
    ports:
      rpc: 18332

persistence:
  enabled: true
  size: 50Gi

service:
  type: ClusterIP
  ports:
    zmqpubrawtx: 28333
    zmqpubrawblock: 28332
    p2p: 18333

# these flags need to be here and not in bitcoindGenericConfig because they have to be present under a separate section inside bitcoind.conf when in testnet/regtest mode
bitcoindCustomConfig:
  bind: 0.0.0.0
  rpcbind: 0.0.0.0
  rpcallowip: 0.0.0.0/0
