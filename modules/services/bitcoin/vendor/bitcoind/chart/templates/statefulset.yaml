{{ $checksum := include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "bitcoind.fullname" . }}
  labels:
    kube-monkey/identifier: {{ include "bitcoind.fullname" . }}
    kube-monkey/enabled: enabled
    kube-monkey/kill-mode: fixed
    kube-monkey/kill-value: "1"
    kube-monkey/mtbf: "20"
    {{- include "bitcoind.labels" . | nindent 4 }}
    {{- with .Values.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  serviceName: {{ include "bitcoind.fullname" . }}
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      {{- include "bitcoind.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/config: {{ $checksum }}
        prometheus.io/path: /metrics
        prometheus.io/port: {{ $.Values.service.ports.metrics | quote }}
        prometheus.io/scrape: "true"
      {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        kube-monkey/identifier: {{ include "bitcoind.fullname" . }}
        kube-monkey/enabled: enabled
        {{- include "bitcoind.selectorLabels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "bitcoind.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      initContainers:
      {{- if .Values.extraInitContainers }}
      {{- toYaml .Values.extraInitContainers | nindent 8 }}
      {{- end }}
        - name: copy-bitcoind-config
          image: busybox
          command:
          - 'sh'
          - '-c'
          - |
            cat <<EOF > /data/.bitcoin/bitcoin.conf
            rpcpassword=$(cat /rpcpassword/password)
            $(cat /configmap/bitcoin.conf)
            EOF
          volumeMounts:
            - name: configmap
              mountPath: /configmap
            - name: rpcpassword
              mountPath: /rpcpassword
            - name: config
              mountPath: /data/.bitcoin/
      containers:
      {{- if .Values.sidecarContainers }}
      {{- toYaml .Values.sidecarContainers | nindent 8 }}
      {{- end }}
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: MALLOC_ARENA_MAX
            value: "1"
          ports:
            - name: rpc
              containerPort: {{ .Values.global.service.ports.rpc }}
            - name: p2p
              containerPort: {{ .Values.service.ports.p2p }}
            - name: zmqpubrawtx
              containerPort: {{ .Values.service.ports.zmqpubrawtx }}
            - name: zmqpubrawblock
              containerPort: {{ .Values.service.ports.zmqpubrawblock }}
          volumeMounts:
            - name: data
              mountPath: /data/.bitcoin
            - name: config
              mountPath: /data/.bitcoin/bitcoin.conf
              subPath: bitcoin.conf
          startupProbe:
            exec:
              command:
              - bitcoin-cli
              - -conf=/data/.bitcoin/bitcoin.conf
              {{- if ne .Values.global.network "mainnet" }}
              - -{{.Values.global.network}}
              {{- end }}
              - getblockchaininfo
            failureThreshold: {{.Values.startupProbe.failureThreshold}}
            periodSeconds: 10
          livenessProbe:
            exec:
              command:
              - bitcoin-cli
              - -conf=/data/.bitcoin/bitcoin.conf
              {{- if ne .Values.global.network "mainnet" }}
              - -{{.Values.global.network}}
              {{- end }}
              - getblockchaininfo
            initialDelaySeconds: 120
            periodSeconds: 30
          readinessProbe:
            exec:
              command:
              - bitcoin-cli
              - -conf=/data/.bitcoin/bitcoin.conf
              {{- if ne .Values.global.network "mainnet" }}
              - -{{.Values.global.network}}
              {{- end }}
              - getblockchaininfo
            periodSeconds: 4
            successThreshold: 3
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
        {{- if and .Values.descriptor.secretName .Values.descriptor.secretKey }}
        - name: descriptor-import
          image: lncm/bitcoind:v27.0
          command: ['/bin/sh']
          args:
          - '-c'
          - |
            echo "# Waiting for bitcoind to finish syncing"
            while ! bitcoin-cli getblockchaininfo | grep '"initialblockdownload": false'; do
              sleep 30
            done
            echo "# Checking for the default wallet"
            if ! bitcoin-cli listwallets | grep "default"; then
              if ! bitcoin-cli loadwallet "default"; then
                echo "# Creating the default wallet"
                bitcoin-cli createwallet "default" || exit 1
              fi
            fi
            echo "# Checking for the bitcoind-signer-descriptor"
            descriptor_json=$(base64 -d </data/descriptors/descriptor_json_base64)
            # extract the descriptor from the descriptor_json
            desc=$(echo "${descriptor_json}" | cut -d'"' -f 6)
            # derive the first address from the descriptor without importing it
            derived_address=$(bitcoin-cli deriveaddresses "$desc" 0 | sed -n '2p' | cut -d'"' -f2)
            # check if the address is already known to the default wallet
            if ! bitcoin-cli -rpcwallet=default getaddressinfo "$derived_address" | grep '"ismine": true'; then
              echo "# Importing the bitcoind-signer-descriptor"
              bitcoin-cli -rpcwallet=default importdescriptors "$descriptor_json" || exit 1
            fi
            echo "# The bitcoind-signer-descriptor is present in the default wallet"
            trap "echo shutting down; exit 0" SIGTERM
            while true; do sleep 100; done
          volumeMounts:
            - name: bitcoind-signer-descriptor
              mountPath: /data/descriptors
            - name: data
              mountPath: /data/.bitcoin
            - name: config
              mountPath: /data/.bitcoin/bitcoin.conf
              subPath: bitcoin.conf
        {{- end }}
        - name: exporter
          image: jvstein/bitcoin-prometheus-exporter:v0.6.0
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
          - name: BITCOIN_RPC_HOST
            value: 127.0.0.1
          - name: BITCOIN_RPC_USER
            value: rpcuser
          - name: BITCOIN_RPC_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ printf "%s-rpcpassword" (include "bitcoind.fullname" .) }}
                key: password
          - name: METRICS_PORT
            value: {{ .Values.service.ports.metrics | quote }}
          - name: BITCOIN_RPC_PORT
            value: {{ .Values.global.service.ports.rpc | quote }}
          ports:
          - name: metrics
            containerPort: {{ .Values.service.ports.metrics }}
            protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: metrics
          readinessProbe:
            httpGet:
              path: /
              port: metrics
      volumes:
        - name: config
          emptyDir: {}
        - name: rpcpassword
          secret:
            secretName: {{ printf "%s-rpcpassword" (include "bitcoind.fullname" .) }}
        - name: configmap
          configMap:
            name: {{ template "bitcoind.fullname" . }}
        - name: data
        {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ .Values.persistence.existingClaim | default (include "bitcoind.fullname" .) }}
        {{- else }}
          emptyDir: {}
        {{- end -}}
        {{- if and .Values.descriptor.secretName .Values.descriptor.secretKey }}
        - name: bitcoind-signer-descriptor
          secret:
            secretName: {{ .Values.descriptor.secretName }}
            items:
            - key: {{ .Values.descriptor.secretKey }}
              path: descriptor_json_base64
        {{- end -}}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
