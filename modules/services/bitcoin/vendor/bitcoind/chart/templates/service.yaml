apiVersion: v1
kind: Service
metadata:
  name: {{ include "bitcoind.fullname" . }}
  labels:
    {{- include "bitcoind.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - name: rpc
      port: {{ .Values.global.service.ports.rpc }}
    - name: p2p
      port: {{ .Values.service.ports.p2p }}
    - name: zmqpubrawtx
      port: {{ .Values.service.ports.zmqpubrawtx }}
    - name: zmqpubrawblock
      port: {{ .Values.service.ports.zmqpubrawblock }}
    - name: metrics
      port: {{ .Values.service.ports.metrics }}
  selector:
    {{- include "bitcoind.selectorLabels" . | nindent 4 }}
