apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "bitcoind.fullname" . }}
  labels:
    {{- include "bitcoind.labels" . | nindent 4 }}
data:
  bitcoin.conf: |-
  {{- range .Values.bitcoindGenericConfig }}
    {{ . }}
  {{- end }}
  {{- range $k, $v := $.Values.bitcoindCustomConfig }}
    {{ printf "%s=%v" $k $v }}
  {{- end }}
  {{ .Values.global.network | indent 2 }}=1
  {{- $sections := splitList "," "test,regtest,signet" }}
  {{- range $sections }}
    {{printf "[%s]" . }}
    {{- range $k, $v := $.Values.bitcoindCustomConfig }}
    {{ printf "%s=%v" $k $v }}
    {{- end }}
  {{- end}}
