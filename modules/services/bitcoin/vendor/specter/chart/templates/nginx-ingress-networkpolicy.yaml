{{- if .Values.networkPolicy.enabled -}}
kind: NetworkPolicy
apiVersion: networking.k8s.io/v1
metadata:
  name: allow-nginx-ingress
spec:
  podSelector:
    matchLabels:
      network/allow-nginx-ingress: "true"
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app.kubernetes.io/name: {{.Values.networkPolicy.podSelectorLabelAppName}}
      namespaceSelector:
        matchLabels:
          type: {{.Values.networkPolicy.namespaceSelectorLabelType}}
{{- end -}}
