apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: {{ include "specter.fullname" . }}
  labels:
    {{- include "specter.labels" . | nindent 4 }}
spec:
  serviceName: {{ include "specter.fullname" . }}
  selector:
    matchLabels:
      {{- include "specter.selectorLabels" . | nindent 6 }}
  template:
    metadata:
    {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
    {{- end }}
      labels:
        {{- include "specter.selectorLabels" . | nindent 8 }}
        network/allow-nginx-ingress: "true"
        network/allow-dns: "true"
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "specter.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          command: ["/usr/local/bin/python3", "-m", "cryptoadvance.specter", "server", "--host=0.0.0.0", "--specter-data-folder=/data"]
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          # livenessProbe:
          #   httpGet:
          #     path: /about
          #     port: 25441
          # readinessProbe:
          #   httpGet:
          #     path: /
          #     port: http
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: data
              mountPath: /data
          env:
          - name: BTC_RPC_USER
            value: {{ .Values.bitcoind.rpcUser }}
          - name: BTC_RPC_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ .Values.bitcoind.rpcPasswordRef.name }}
                key: {{ .Values.bitcoind.rpcPasswordRef.key }}
          - name: BTC_RPC_HOST
            value: bitcoind
          - name: BTC_RPC_PORT
            value: "18443"
          - name: BTC_RPC_PROTOCOL
            value: http
      volumes:
        - name: data
        {{- if .Values.persistence.enabled }}
          persistentVolumeClaim:
            claimName: {{ .Values.persistence.existingClaim | default (include "specter.fullname" .) }}
        {{- else }}
          emptyDir: {}
        {{- end -}}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
