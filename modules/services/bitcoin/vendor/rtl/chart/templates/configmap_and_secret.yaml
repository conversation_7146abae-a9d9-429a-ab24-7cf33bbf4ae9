{{ $password := include "rtl.password" . }}
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "rtl.fullname" . }}
  labels:
    {{- include "rtl.labels" . | nindent 4 }}
data:
  RTL-Config.json: |-
    {
      "multiPassHashed": {{ $password | sha256sum | quote }},
      "port": "3000",
      "defaultNodeIndex": 1,
      "SSO": {
        "rtlSSO": 0,
        "rtlCookiePath": "",
        "logoutRedirectLink": "/login"
      },
      "nodes": [
        {{- $c := 0 | int }}
        {{- range .Values.lnds }}
        {{- $c = add1 $c }}
        {
          "index": {{ $c }},
          "lnNode": "Node {{ $c }}",
          "lnImplementation": "LND",
          "Authentication": {
            "macaroonPath": "/RTL/data/lnd{{ $c }}/rpc",
            "configPath": ""
          },
          "Settings": {
            "userPersona": "OPERATOR",
            "themeMode": {{ if .dark }}"NIGHT"{{ else }}"DAY"{{ end }},
            "themeColor": "{{ .themeColor }}",
            "lnServerUrl": "https://{{ .url }}:8080",
            "enableLogging": true,
            "fiatConversion": true,
            "channelBackupPath": "/RTL/data/lnd{{ $c }}backup"
          }
        }{{- if ne (len $.Values.lnds) $c}},{{ end }}
        {{- end }}
      ]
    }
---
apiVersion: v1
kind: Secret
metadata:
  name: {{ printf "%s-pass" (include "rtl.fullname" .) }}
  labels:
    {{- include "rtl.labels" . | nindent 4 }}
stringData:
  password: {{ $password }}
