apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "rtl.fullname" . }}
  labels:
    {{- include "rtl.labels" . | nindent 4 }}
    kube-monkey/identifier: {{ .Release.Name }}
    {{- with .Values.labels }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  selector:
    matchLabels:
      {{- include "rtl.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "rtl.selectorLabels" . | nindent 8 }}
        kube-monkey/identifier: {{ .Release.Name }}
        {{- with .Values.labels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      initContainers:
        - name: copy-rtl-config
          image: busybox
          command:
          - 'sh'
          - '-c'
          - |
            cp /RTL/RTL-Config.json /RTL/data/RTL-Config.json
            chown 1000:1000 /RTL/data/RTL-Config.json
          volumeMounts:
            - name: rtl-conf
              mountPath: "/RTL/RTL-Config.json"
              subPath: "RTL-Config.json"
            - name: data
              mountPath: /RTL/data
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          env:
            - name: RTL_CONFIG_PATH
              value: /RTL/data
          ports:
            - name: http
              containerPort: {{.Values.service.port}}
              protocol: TCP
          livenessProbe:
            httpGet:
              path: /
              port: http
          readinessProbe:
            httpGet:
              path: /
              port: http
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
            - name: data
              mountPath: /RTL/data
            {{- $c := 0 | int }}
            {{- range .Values.lnds }}
            {{- $c = add1 $c }}
            - name: "lnd{{ $c }}-rpc"
              mountPath: "/RTL/data/lnd{{ $c }}/rpc"
              readOnly: true
            {{- end }}
      volumes:
          - name: data
            emptyDir: {}
          - name: rtl-conf
            configMap:
              name: {{ include "rtl.fullname" . }}
          {{- $c := 0 | int }}
          {{- range .Values.lnds }}
          {{- $c = add1 $c }}
          - name: lnd{{ $c }}-rpc
            secret:
              secretName: {{ .secretName }}
          {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
