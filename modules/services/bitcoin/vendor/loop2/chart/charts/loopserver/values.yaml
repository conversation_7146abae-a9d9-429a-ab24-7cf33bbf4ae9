# Default values for loop.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

image:
  repository: lightninglabs/loopserver
  tag: v0.9.52-beta
  pullPolicy: IfNotPresent

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 11009

resources:
  limits:
    cpu: 200m
  requests:
    cpu: 1m

nodeSelector: {}

tolerations: []

affinity: {}

persistence:
  enabled: true
  size: 16Mi
  accessMode: ReadWriteOnce

serviceAccount:
  create: true
rbac:
  create: true
