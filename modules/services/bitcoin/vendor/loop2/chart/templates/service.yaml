apiVersion: v1
kind: Service
metadata:
  name: {{ include "loop.fullname" . }}
  labels:
    {{- include "loop.labels" . | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port.rest }}
      targetPort: http
      protocol: TCP
      name: rest
    - port: {{ .Values.service.port.grpc }}
      targetPort: grpc
      protocol: TCP
      name: grpc
  selector:
    {{- include "loop.selectorLabels" . | nindent 4 }}
