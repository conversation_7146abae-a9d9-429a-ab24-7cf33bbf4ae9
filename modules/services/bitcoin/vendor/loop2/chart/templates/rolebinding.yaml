{{- if and .Values.serviceAccount.create .Values.rbac.create }}
apiVersion: {{ include "common.capabilities.rbac.apiVersion" . }}
kind: RoleBinding
metadata:
  name: {{ include "loop.fullname" . }}
  labels:
    {{- include "loop.labels" . | nindent 4 }}
roleRef:
  kind: Role
  name: {{ include "loop.fullname" . }}
  apiGroup: rbac.authorization.k8s.io
subjects:
  - kind: ServiceAccount
    name: {{ include "loop.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
{{- end }}
