apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-export-secrets" (include "loop.fullname" .) }}
  labels:
    {{- include "loop.labels" . | nindent 4 }}
data:
  exportSecrets.sh: |
    #!/bin/sh

    export TLS=$(base64 /root/.loop/{{ .Values.global.network }}/tls.cert | tr -d '\n\r')
    export MACAROON=$(base64 /root/.loop/{{ .Values.global.network }}/loop.macaroon | tr -d '\n\r')
    mkdir macaroons
    
    cp /root/.loop/{{ .Values.global.network }}/*.macaroon macaroons
    kubectl create secret generic {{ include "loop.fullname" . }}-credentials \
    --from-literal=tls_base64=$TLS --from-file=/root/.loop/{{ .Values.global.network }}/tls.cert \
    --from-literal=loop_macaroon_base64=$MACAROON --from-file=macaroons \
    --dry-run=client -o yaml | kubectl apply -f -
