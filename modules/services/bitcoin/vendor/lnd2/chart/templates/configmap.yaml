apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "lnd.fullname" . }}
  labels:
    {{- include "lnd.labels" . | nindent 4 }}
data:
  lnd.conf: |-
  {{- if ne .Values.global.network "regtest" }}
    wallet-unlock-password-file=/tmp/lnd-pass
    wallet-unlock-allow-create=true
  {{- end }}
  {{- range .Values.lndGeneralConfig }}
    {{ . }}
  {{- end }}
  {{- range .Values.configmap.customValues }}
    {{ . }}
  {{- end }}
    tlsextradomain={{ include "lnd.fullname" . }}
  {{- if .Values.apiService.staticIP }}
    tlsextraip={{ .Values.apiService.staticIP }}
  {{- end}}
  {{- if .Values.p2pService.staticIP }}
    tlsextraip={{ .Values.p2pService.staticIP }}
    externalip={{ .Values.p2pService.staticIP }}
  {{- end}}
    bitcoin.{{ .Values.global.network }}=true
