{{- if and .Values.serviceAccount.create .Values.rbac.create }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: {{ include "lnd.fullname" . }}
  labels:
    {{- include "lnd.labels" . | nindent 4 }}
roleRef:
  kind: Role
  name: {{ include "lnd.fullname" . }}
  apiGroup: rbac.authorization.k8s.io
subjects:
  - kind: ServiceAccount
    name: {{ include "lnd.serviceAccountName" . }}
    namespace: {{ .Release.Namespace }}
{{- end }}
