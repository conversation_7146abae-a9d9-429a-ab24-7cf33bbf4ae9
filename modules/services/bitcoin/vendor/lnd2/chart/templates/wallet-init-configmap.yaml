{{- if ne .Values.global.network "regtest" }}
apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-wallet-init" (include "lnd.fullname" .) }}
  labels:
    {{- include "lnd.labels" . | nindent 4 }}
data:
  walletInit.exp: |
    #!/usr/bin/expect -f
    #
    # This Expect script was generated by autoexpect on Sun Mar 14 17:50:41 2021
    # Expect and autoexpect were both written by <PERSON>, NIST.
    #
    # Note that autoexpect does not guarantee a working script.  It
    # necessarily has to guess about certain things.  Two reasons a script
    # might fail are:
    #
    # 1) timing - A surprising number of programs (rn, ksh, zsh, telnet,
    # etc.) and devices discard or ignore keystrokes that arrive "too
    # quickly" after prompts.  If you find your new script hanging up at
    # one spot, try adding a short sleep just before the previous send.
    # Setting "force_conservative" to 1 (see below) makes <PERSON>pect do this
    # automatically - pausing briefly before sending each character.  This
    # pacifies every program I know of.  The -c flag makes the script do
    # this in the first place.  The -C flag allows you to define a
    # character to toggle this mode off and on.

    set force_conservative 0  ;# set to 1 to force conservative mode even if
                ;# script wasn't run conservatively originally
    if {$force_conservative} {
        set send_slow {1 .1}
        proc send {ignore arg} {
            sleep .1
            exp_send -s -- $arg
        }
    }

    #
    # 2) differing output - Some programs produce different output each time
    # they run.  The "date" command is an obvious example.  Another is
    # ftp, if it produces throughput statistics at the end of a file
    # transfer.  If this causes a problem, delete these patterns or replace
    # them with wildcards.  An alternative is to use the -p flag (for
    # "prompt") which makes Expect only look for the last line of output
    # (i.e., the prompt).  The -P flag allows you to define a character to
    # toggle this mode off and on.
    #
    # Read the man page for more info.
    #
    # -Don

    set timeout -1
    set NETWORK [lindex $argv 0];
    set PASSWORD [lindex $argv 1];
    spawn lncli -n $NETWORK create
    match_max 100000
    expect -exact "Input wallet password: "
    send -- "$PASSWORD\r"
    expect -exact "\r
    Confirm password: "
    send -- "$PASSWORD\r"
    expect -exact "\r
    \r
    Do you have an existing cipher seed mnemonic or extended master root key you want to use?\r
    Enter 'y' to use an existing cipher seed mnemonic, 'x' to use an extended master root key \r
    or 'n' to create a new seed (Enter y/x/n): "
    send -- "n\r"
    expect -exact "n\r
    \r
    Your cipher seed can optionally be encrypted.\r
    Input your passphrase if you wish to encrypt it (or press enter to proceed without a cipher seed passphrase): "
    send -- "\r"
    expect eof
{{- end -}}
