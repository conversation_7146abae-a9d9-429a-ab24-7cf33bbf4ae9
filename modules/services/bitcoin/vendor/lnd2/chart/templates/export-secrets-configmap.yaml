apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ printf "%s-export-secrets" (include "lnd.fullname" .) }}
  labels:
    {{- include "lnd.labels" . | nindent 4 }}
data:
  exportSecrets.sh: |
    #!/bin/sh

    PUBKEY=$(lncli -n {{.Values.global.network}} getinfo | jq -r .identity_pubkey)
    kubectl create secret generic {{ include "lnd.fullname" . }}-pubkey --from-literal=pubkey=$PUBKEY --dry-run=client -o yaml | kubectl apply -f -

    export TLS=$(base64 /root/.lnd/tls.cert | tr -d '\n\r')
    export MACAROON=$(base64 /root/.lnd/data/chain/bitcoin/$NETWORK/admin.macaroon | tr -d '\n\r')
    export READONLY_MACAROON=$(base64 /root/.lnd/data/chain/bitcoin/$NETWORK/readonly.macaroon | tr -d '\n\r')
    export XPUB=$(lncli -n {{.Values.global.network}} wallet accounts list --name default | jq -c -r '.accounts[] | select(.derivation_path | contains("m/84")) | .extended_public_key')

    mkdir macaroons
    cp /root/.lnd/data/chain/bitcoin/$NETWORK/*.macaroon macaroons

    kubectl create secret generic {{ include "lnd.fullname" . }}-credentials \
    --from-literal=tls_base64=$TLS --from-file=/root/.lnd/tls.cert \
    --from-literal=readonly_macaroon_base64=$READONLY_MACAROON \
    --from-literal=xpub=$XPUB \
    --from-literal=admin_macaroon_base64=$MACAROON --from-file=macaroons \
    --dry-run=client -o yaml | kubectl apply -f -
