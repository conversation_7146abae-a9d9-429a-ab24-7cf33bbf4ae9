{{- if .Values.bria.secrets.create }}
apiVersion: v1
kind: Secret
metadata:
  name: {{ template "bria.fullname" . }}
  labels:
    app: {{ template "bria.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
  {{- if .Values.bria.secrets.annotations }}
  annotations:
{{ toYaml .Values.bria.secrets.annotations | indent 4 }}
  {{- end }}
type: Opaque
data:
  pg-con: {{ .Values.bria.secrets.pgCon | trim | b64enc | trim }}
  signer-encryption-key: {{ .Values.bria.secrets.signerEncryptionKey | trim | b64enc | trim }}
{{- end }}
