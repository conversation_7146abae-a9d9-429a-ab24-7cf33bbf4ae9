apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "bria.fullname" . }}
  labels:
    app: {{ template "bria.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    kube-monkey/identifier: {{ template "bria.fullname" . }}
    kube-monkey/enabled: enabled
    kube-monkey/kill-mode: fixed
    kube-monkey/kill-value: "1"
    kube-monkey/mtbf: "3"
spec:
  selector:
    matchLabels:
      app: {{ template "bria.fullname" . }}
      release: {{ .Release.Name }}
  replicas: {{ .Values.bria.replicas }}
  template:
    metadata:
      labels:
        app: {{ template "bria.fullname" . }}
        chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
        release: "{{ .Release.Name }}"
        kube-monkey/identifier: {{ template "bria.fullname" . }}
        kube-monkey/enabled: enabled
      {{- with .Values.bria.labels }}
        {{ toYaml . | nindent 8 }}
      {{- end }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/bria-cm.yaml") . | sha256sum }}
    spec:
      {{ if .Values.bria.provisionAdmin }}
      serviceAccountName: {{ template "bria.fullname" . }}
      {{ end }}
      initContainers:
      - name: wait-for-postgresql
        image: postgres
        command:
          - "bash"
          - "-c"
          - |
            while ! pg_isready -d $PG_CON > /dev/null 2>&1
            do
              echo "waiting for postgres to come up"
              sleep 5
            done
        env:
        - name: PG_CON
          valueFrom:
            secretKeyRef:
              name: {{ template "bria.fullname" . }}
              key: "pg-con"
      containers:
      - name: {{ .Chart.Name }}
        image: "{{ .Values.bria.image.repository }}@{{ .Values.bria.image.digest }}"
        ports:
        - name: api
          containerPort: {{ .Values.bria.api.service.port }}
          protocol: TCP
        - name: admin
          containerPort: {{ .Values.bria.admin.service.port }}
          protocol: TCP
        args:
        - bria
        - daemon
        {{ if .Values.bria.devDaemon.enabled }}
        - dev
        - -x
        - tpubDDDDGYiFda8HfJRc2AHFJDxVzzEtBPrKsbh35EaW2UGd5qfzrF2G87ewAgeeRyHEz4iB3kvhAYW1sH6dpLepTkFUzAktumBN8AXeXWE9nd1
        - -d
        - m/84h/0h/0h
        {{ else }}
        - run
        {{ end }}

        volumeMounts:
        - name: "config"
          mountPath: "/bria.yml"
          subPath: "bria.yml"
        resources:
          {{ toYaml .Values.resources | nindent 10 }}
        env:
        - name: BRIA_CONFIG
          value: "/bria.yml"
        - name: PG_CON
          valueFrom:
            secretKeyRef:
              name: {{ template "bria.fullname" . }}
              key: "pg-con"
        - name: SIGNER_ENCRYPTION_KEY
          valueFrom:
            secretKeyRef:
              name: {{ template "bria.fullname" . }}
              key: "signer-encryption-key"
        - name: KUBE_NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        {{ if .Values.bria.devDaemon.enabled }}
        - name: BITCOIND_SIGNER_ENDPOINT
          value: {{ .Values.bria.devDaemon.bitcoindSignerHost }}
        {{ end }}
        startupProbe:
          grpc:
            port: {{ .Values.bria.api.service.port }}
        livenessProbe:
          grpc:
            port: {{ .Values.bria.api.service.port }}
        resources:
        {{- toYaml .Values.bria.resources | nindent 10 }}
      volumes:
      - name: config
        configMap:
          name: {{ include "bria.fullname" . }}-config
