apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "bria.fullname" . }}-config
data:
  bria.yml: |-
    app: 
      blockchain:
        network: {{ .Values.bria.app.blockchain.network }}
        electrum_url: {{ .Values.bria.app.blockchain.electrumUrl }}
      fees:
        mempool_space:
          url: {{ .Values.bria.app.fees.mempoolSpace.url }}
          number_of_retries: {{ .Values.bria.app.fees.mempoolSpace.numberOfRetries }}
      {{- if gt (len .Values.bria.app.security.blockedAddresses) 0 }}
      security:
        blocked_addresses: {{ toYaml .Values.bria.app.security.blockedAddresses | nindent 8 }}
      {{- end }}
      {{- if and .Values.bria.app.deprecatedEncryptionKey.nonce .Values.bria.app.deprecatedEncryptionKey.key }}
      deprecated_encryption_key:
        nonce: {{ .Values.bria.app.deprecatedEncryptionKey.nonce }}
        key: {{ .Values.bria.app.deprecatedEncryptionKey.key }}
      {{- end }}
    db:
      migrate_on_start: true
      pool_size: {{ .Values.bria.db.poolSize }}
    tracing:
      host: {{ .Values.bria.tracing.host }}
      port: {{ .Values.bria.tracing.port }}
      service_name: {{ .Values.bria.tracing.serviceName }}
    admin:
      listen_port: {{ .Values.bria.admin.service.port }}
    api:
      listen_port: {{ .Values.bria.api.service.port }}
