apiVersion: v1
kind: Service
metadata:
  name: {{ template "bria.fullname" . }}-api
  labels:
    app: {{ template "bria.fullname" . }}-api
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
  {{ with .Values.bria.api.service.annotations }}
  annotations:
    {{- toYaml . | nindent 4 -}}
  {{ end }}
spec:
  type: {{ .Values.bria.api.service.type }}
  {{ if and (eq .Values.bria.api.service.type "LoadBalancer") .Values.bria.api.service.staticIP }}
  loadBalancerIP: {{ .Values.bria.api.service.staticIP }}
  {{ end }}
  ports:
  - port: {{ .Values.bria.api.service.port }}
    targetPort: {{ .Values.bria.api.service.port }}
    protocol: TCP
    name: http
  selector:
    app: {{ template "bria.fullname" . }}
