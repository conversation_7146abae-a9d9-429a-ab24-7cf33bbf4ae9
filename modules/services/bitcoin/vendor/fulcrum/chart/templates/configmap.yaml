apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "fulcrum.fullname" . }}
  labels:
    {{- include "fulcrum.labels" . | nindent 4 }}
data:
  fulcrum.conf: |
    tcp = 0.0.0.0:{{ .Values.service.ports.tcp }}
    ssl = 0.0.0.0:{{ .Values.service.ports.ssl }}
    ws = 0.0.0.0:{{ .Values.service.ports.ws }}
    wss = 0.0.0.0:{{ .Values.service.ports.wss }}
    stats = 0.0.0.0:{{ .Values.service.ports.stats }}
    admin = 0.0.0.0:{{ .Values.service.ports.admin }}
    bitcoind = {{ .Values.bitcoindRpcHost }}:{{ .Values.bitcoindRpcPort }}
    key = /.fulcrum/tls.key
    cert = /.fulcrum/tls.cert
  {{- if .Values.fulcrumGenericConfig }}
  {{- range .Values.fulcrumGenericConfig }}
    {{ . }}
  {{- end }}
  {{- end }}
