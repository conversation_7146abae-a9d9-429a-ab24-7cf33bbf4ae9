secrets:
  create: true

image:
  repository: cculianu/fulcrum
  pullPolicy: IfNotPresent
  tag: v1.11.1

# If true, generate blocks to the bitcoind node. Will fail if bitcoind is not in regtest mode.
# The bitcoind installation should be in the same namespace as the fulcrum installation.
autoGenerateBlocks: false

generateBlocksImage:
  repository: bitnami/kubectl
  pullPolicy: IfNotPresent
  tag: 1.24.12

serviceAccount:
  create: true
  annotations: {}
  name: ""

podAnnotations: {}

service:
  type: ClusterIP
  ports:
    tcp: 50001
    ssl: 50002
    ws: 50003
    wss: 50004
    stats: 8080
    admin: 8000

ingress:
  enabled: false
  annotations: {}
  hosts:
    - host: chart-example.local
      paths: []
  tls: []

resources: {}

terminationGracePeriodSeconds: 600

persistence:
  enabled: true
  accessMode: ReadWriteOnce
  size: 200Gi

bitcoindRpcPassSecretName: bitcoind-onchain-rpcpassword
bitcoindRpcPort: 8332
bitcoindRpcHost: bitcoind-onchain

fulcrumGenericConfig:
  # https://github.com/cculianu/Fulcrum/blob/master/doc/fulcrum-example-config.conf
  - rpcuser = rpcuser
  - datadir = /.fulcrum/db
  - bitcoind_clients = 1
  - peering = false
  - announce = false
  - utxo_cache = 1024
  - worker_threads = 1
  - bitcoind_throttle = 25 10 2
  - max_subs_per_ip = 1000000
