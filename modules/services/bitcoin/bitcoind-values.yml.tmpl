# trigger_ref: ${trigger_ref}

global:
  network: ${bitcoin_network}
  service:
    ports:
      rpc: ${rpc_port}

secrets:
  create: false

service:
  type: ClusterIP
  ports:
    zmqpubrawtx: 28333
    zmqpubrawblock: 28332
    p2p: ${p2p_port}

%{ if existing_claim != "" }
persistence:
  existingClaim: ${existing_claim}
%{ endif }

%{ if sync_from_bucket }
extraInitContainers:
- name: initial-sync
  image: us.gcr.io/galoy-org/bitcoin-chain-dl:latest
  imagePullPolicy: IfNotPresent
  command:
  - 'sh'
  - '-c'
  - |
  %{ if bitcoin_network == "testnet" }
    remote_state_folder="testnet3"
    local_state_folder=$${remote_state_folder}
  %{ else }
    remote_state_folder="mainnet"
    local_state_folder=""
  %{ endif }
    if [ ! -d /data/.bitcoin/$${local_state_folder}/chainstate ]; then
      echo "Syncing $${remote_state_folder} data"
      mkdir -p /data/.bitcoin/$${local_state_folder}
      gsutil -m cp -r gs://bitcoin-blockchain-state/$${remote_state_folder}/* \
        /data/.bitcoin/$${local_state_folder}
    else
      echo "Data already present... skipping initial download"
    fi
  volumeMounts:
    - name: data
      mountPath: /data/.bitcoin
%{ endif }
