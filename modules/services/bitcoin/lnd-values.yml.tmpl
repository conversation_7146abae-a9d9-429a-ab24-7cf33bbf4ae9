# trigger_ref: ${trigger_ref}

secrets:
  create: false

global:
  network: ${bitcoin_network}

%{ if lnd_image_tag != "" }
image:
  tag: ${lnd_image_tag}
%{ endif }

bitcoindRpcPassSecretName: ${bitcoind_rpc_pass_secret_name}

configmap:
  broadcastIP: true
  customValues:
    - bitcoind.rpchost=${bitcoind_endpoint}
    - db.bolt.auto-compact=true
    - bitcoin.basefee=${lnd_base_fee}
    - bitcoin.feerate=${lnd_fee_rate}
    - max-commit-fee-rate-anchors=100
    - bitcoind.zmqpubrawblock=${bitcoind_zmq_block_endpoint}
    - bitcoind.zmqpubrawtx=${bitcoind_zmq_tx_endpoint}
    - alias=${lnd_node_alias}
    %{ if bitcoin_network == "mainnet" }
    - minchansize=${lnd_min_channel_size}
    %{ else }
    - minchansize=50000
    %{ endif }

persistence:
  existingClaim: ${existing_claim}
%{ if storage_class != "" }
  storageClass: ${storage_class}
%{ endif }

p2pService:
  staticIP: "${lnd_public_ip}"
  type: LoadBalancer
  port: 9735

apiService:
  staticIP: "${lnd_internal_ip}"
  type: LoadBalancer
  annotations:
    networking.gke.io/load-balancer-type: "Internal"
  ports:
    rpc: 10009
    rest: 8080

kubemonkey:
  enabled: true

lnd:
  db:
    backend: bbolt

postgresql:
  enabled: false

autoGenerateTls:
  enabled: false

podAnnotations:
  tls_checksum: ${tls_checksum}
