resource "helm_release" "fulcrum" {
  name       = "fulcrum"
  chart      = "${path.module}/vendor/fulcrum/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = kubernetes_namespace.bitcoin.metadata[0].name

  dependency_update = true

  values = [
    templatefile("${path.module}/fulcrum-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/fulcrum/git-ref/ref")
      bitcoind_rpc_port : local.rpc_port
    }),
    file("${path.module}/../../../gcp/${local.name_prefix}/bitcoin/fulcrum-scaling.yml")
  ]

  depends_on = [
    helm_release.bitcoind_onchain
  ]
}

resource "kubernetes_secret" "fulcrum_smoketest" {
  metadata {
    name      = "fulcrum-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    fulcrum_endpoint   = "fulcrum.${kubernetes_namespace.bitcoin.metadata[0].name}.svc.cluster.local"
    fulcrum_stats_port = 8080
  }
}
