resource "random_password" "auth_backend_session_keys" {
  length  = 128
  special = false
}

resource "kubernetes_secret" "auth_backend_session_keys" {
  metadata {
    name      = "auth-backend"
    namespace = local.galoy_namespace
  }
  data = {
    "session-keys" : random_password.auth_backend_session_keys.result
  }
}

resource "random_password" "kratos_default" {
  length  = 32
  special = false
}
resource "random_password" "kratos_cookie" {
  length  = 32
  special = false
}
resource "random_password" "kratos_cipher" {
  length  = 32
  special = false
}

resource "kubernetes_secret" "kratos_secrets" {
  metadata {
    name      = local.kratos_secret_name
    namespace = local.galoy_namespace
  }

  data = {
    "dsn"               = module.postgresql.creds["kratos"].conn
    "secretsDefault"    = random_password.kratos_default.result
    "secretsCookie"     = random_password.kratos_cookie.result
    "secretsCipher"     = random_password.kratos_cipher.result
    "smtpConnectionURI" = "smtp://${local.smtp_username}:${local.smtp_password}@smtp.mailgun.org:587"
  }
}

resource "random_password" "kratos_master_user_password" {
  length  = 32
  special = false
}

resource "kubernetes_secret" "kratos_secret" {
  metadata {
    name      = "kratos-secret"
    namespace = local.galoy_namespace
  }

  data = {
    "master_user_password" = random_password.kratos_master_user_password.result
    "callback_api_key"     = random_password.kratos_callback_api_key.result
  }
}
