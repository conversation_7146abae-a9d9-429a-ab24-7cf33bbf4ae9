galoy:
  mattermostWebhookUrl: ${mattermost_webhook_url}
  openai:
    assistantId: ${openai_assistant_id}
  config:
    test_accounts:
    - phone: "+***********"
      code: "${bank_owner_code}"
    - phone: "${dealer_phone}"
      code: "${dealer_code}"
%{ for account in test_accounts ~}
    - phone: "${account.phone}"
      code: "${account.code}"
%{ endfor ~}
kratos:
  kratos:
    config:
      selfservice:
        flows:
          registration:
            after:
              password:
                hooks:
                  - hook: session
price:
  realtime:
    config:
      exchanges:
      - name: "bitfinex2"
        enabled: true
        quoteAlias: "USD"
        base: "BTC"
        quote: "USD"
        provider: "ccxt"
        cron: "*/15 * * * * *"
      - name: "kraken"
        enabled: true
        quoteAlias: "USD"
        base: "BTC"
        quote: "USD"
        provider: "ccxt"
        cron: "*/20 * * * * *"
      - name: "bitstamp"
        enabled: true
        quoteAlias: "USD"
        base: "BTC"
        quote: "USD"
        provider: "ccxt"
        cron: "*/15 * * * * *"
      - name: "free-currency-rates-usd"
        enabled: true
        quoteAlias: "*"
        base: "USD"
        quote: "*"
        excludedQuotes: ["LBP", "NGN", "BOB"]
        provider: "free-currency-rates"
        cron: "*/5 * * * * *"
        config:
          baseUrl: "https://cdn.jsdelivr.net/npm/@fawazahmed0/currency-api@latest/v1/currencies"
          fallbackUrl: "https://currency-api.pages.dev/v1/currencies"
          cacheSeconds: 1800
      - name: "exchangeratehost"
        enabled: ${price_exchangeratehost_enabled}
        quoteAlias: "*"
        base: "USD"
        quote: "*"
        excludedQuotes: ["LBP", "NGN", "BOB"]
        provider: "exchangeratehost"
        cron: "*/5 * * * * *"
        config:
          baseUrl: "https://api.exchangerate.host"
          apiKey: "${price_exchangeratehost_api_key}"
          cacheSeconds: 1800
      - name: "currencybeacon"
        enabled: ${price_currency_beacon_enabled}
        quoteAlias: "*"
        base: "USD"
        quote: "*"
        excludedQuotes: ["LBP", "NGN", "BOB"]
        provider: "currencybeacon"
        cron: "*/5 * * * * *"
        config:
          baseUrl: "https://api.currencybeacon.com/v1"
          apiKey: "${price_currency_beacon_api_key}"
          cacheSeconds: 1800
      - name: "yadio-btc"
        enabled: true
        base: "BTC"
        quote: ["ARS", "LBP", "NGN", "BRL", "PEN", "BOB"]
        quoteAlias: ["ARS", "LBP", "NGN", "BRL", "PEN", "BOB"]
        provider: "yadio"
        cron: "*/5 * * * * *"
        config:
          baseUrl: "https://api.yadio.io/exrates"
          cacheSeconds: 900
      - name: "yadio-usd"
        enabled: true
        base: "USD"
        quote: ["ARS", "LBP", "NGN", "BRL", "PEN", "BOB"]
        quoteAlias: ["ARS", "LBP", "NGN", "BRL", "PEN", "BOB"]
        provider: "yadio"
        cron: "*/5 * * * * *"
        config:
          baseUrl: "https://api.yadio.io/exrates"
          cacheSeconds: 900

router:
  supergraphFile: |
    ${indent(4, supergraph)}
