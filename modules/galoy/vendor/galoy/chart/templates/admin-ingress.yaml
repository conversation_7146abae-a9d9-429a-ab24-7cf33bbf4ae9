{{- if .Values.galoy.admin.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ template "galoy.admin.fullname" . }}
  labels:
    app: {{ template "galoy.admin.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: Helm
  annotations:
    cert-manager.io/cluster-issuer: {{ .Values.galoy.admin.ingress.clusterIssuer }}
    nginx.ingress.kubernetes.io/limit-rpm: "10"
    nginx.ingress.kubernetes.io/limit-burst-multiplier: "20"
    nginx.ingress.kubernetes.io/limit-connections: "4"
    nginx.ingress.kubernetes.io/auth-url: "http://galoy-oathkeeper-api.{{ .Release.Namespace }}.svc.cluster.local:4456/decisions"
    nginx.ingress.kubernetes.io/auth-method: GET
    nginx.ingress.kubernetes.io/auth-response-headers: Authorization
    nginx.ingress.kubernetes.io/auth-snippet: |
      proxy_set_header X-Original-URL /admin$request_uri;
      proxy_set_header X-Forwarded-Method $request_method;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_set_header X-Forwarded-Host $host;
      proxy_set_header X-Forwarded-Uri /admin$request_uri;
    {{- with .Values.galoy.admin.ingress.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  ingressClassName: nginx
  tls:
    {{- range .Values.galoy.admin.ingress.hosts }}
    - hosts:
      - {{ . }}
      secretName: {{ printf "%s-tls" . }}
    {{- end }}
  rules:
  {{- range .Values.galoy.admin.ingress.hosts }}
    - host: {{ . }}
      http:
        paths:
            {{- if $.Values.galoy.admin.ingress.extraPaths }}
            {{- toYaml $.Values.galoy.admin.ingress.extraPaths | nindent 10 }}
            {{- end }}
          - pathType: ImplementationSpecific
            path: /
            backend:
              service:
                name: {{ template "galoy.admin.fullname" $ }}
                port:
                  number: {{ $.Values.galoy.admin.port }}
  {{- end -}}
{{- end -}}
