apiVersion: v1
kind: Service
metadata:
  name: {{ template "galoy.consent.fullname" . }}
  labels:
    app: {{ template "galoy.consent.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: Helm
spec:
  type: {{ .Values.galoy.consent.serviceType }}
  ports:
    - port: {{ .Values.galoy.consent.port }}
      targetPort: {{ .Values.galoy.consent.containerPort }}
      protocol: TCP
      name: http
  selector:
    app: {{ template "galoy.consent.fullname" . }}
