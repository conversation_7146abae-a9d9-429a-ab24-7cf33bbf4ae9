apiVersion: v1
kind: Service

metadata:
  name: {{ template "galoy.trigger.fullname" . }}
  labels:
    app: {{ template "galoy.trigger.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: Helm

spec:
  type: {{ .Values.galoy.trigger.serviceType }}
  ports:
    - port: {{ .Values.galoy.trigger.port }}
      targetPort: {{ .Values.galoy.trigger.port }}
      protocol: TCP
      name: http
  selector:
    app: {{ template "galoy.trigger.fullname" . }}
