apiVersion: apps/v1
kind: Deployment

metadata:
  name: {{ template "galoy.exporter.fullname" . }}
  labels:
    app: {{ template "galoy.exporter.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: <PERSON><PERSON>

    kube-monkey/enabled: enabled
    kube-monkey/identifier: {{ template "galoy.exporter.fullname" . }}
    kube-monkey/kill-mode: fixed
    kube-monkey/kill-value: "1"
    kube-monkey/mtbf: "3"

spec:

  replicas: {{ .Values.galoy.exporter.replicas }}

  selector:
    matchLabels:
      app: {{ template "galoy.exporter.fullname" . }}

  template:

    metadata:
      name: {{ template "galoy.exporter.fullname" . }}
      labels:
        app: {{ template "galoy.exporter.fullname" . }}
        kube-monkey/enabled: enabled
        kube-monkey/identifier: {{ template "galoy.exporter.fullname" . }}
      annotations:
        prometheus.io/path: /metrics
        prometheus.io/port: {{ .Values.galoy.exporter.port | quote }}
        prometheus.io/scrape: "true"

    spec:
      serviceAccountName: {{ template "galoy.name" . }}

      initContainers:
      - name: wait-for-mongodb-migrate
        image: "groundnuty/k8s-wait-for:v2.0"
        args:
        - job-wr
        - {{ template "galoy.migration.jobname" . }}

      containers:
      - name: exporter
        image: "{{ .Values.galoy.images.exporter.repository }}@{{ .Values.galoy.images.exporter.digest }}"
        resources:
          {{ toYaml .Values.galoy.exporter.resources | nindent 10 }}
        ports:
        - name: http
          containerPort: {{ .Values.galoy.exporter.port }}
          protocol: TCP

        env:
        - name: HELMREVISION
          value: {{ .Release.Revision | quote }}
        - name: LOGLEVEL
          value: {{ .Values.galoy.exporter.logLevel | quote }}

        - name: NETWORK
          value: {{ .Values.galoy.network }}
        - name: GALOY_API_PORT
          value: {{ .Values.galoy.exporter.port | quote }}
        - name: TRIGGER_PORT
          value: {{ .Values.galoy.trigger.port | quote }}
        - name: WEBSOCKET_PORT
          value: {{ .Values.galoy.websocket.port | quote }}

        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: {{ .Values.tracing.otelExporterOtlpEndpoint | quote }}
        - name: TRACING_SERVICE_NAME
          value: "{{ .Values.tracing.prefix }}-{{ template "galoy.exporter.fullname" . }}"

        - name: PROXY_CHECK_APIKEY
          valueFrom:
            secretKeyRef:
              name: {{ .Values.galoy.proxyCheckExistingSecret.name }}
              key: {{ .Values.galoy.proxyCheckExistingSecret.key }}

        - name: LND_PRIORITY
          value: {{ .Values.galoy.lndPriority }}

{{/* Databases */}}
{{ include "galoy.mongodb.env" . | indent 8 }}
{{ include "galoy.redis.env" . | indent 8 }}

{{/* Bitcoin/LND */}}
{{ include "galoy.lnd1.env" . | indent 8 }}
{{ include "galoy.lnd2.env" . | indent 8 }}
{{ include "galoy.bria.env" . | indent 8 }}

{{/* API Specifics */}}
{{ include "galoy.twilio.env" . | indent 8 }}
{{ include "galoy.geetest.env" . | indent 8 }}

{{ include "galoy.kratos.env" . | indent 8 }}

        - name: OATHKEEPER_DECISION_ENDPOINT
          value: http://galoy-oathkeeper-api:4456

        - name: NOTIFICATIONS_HOST
          value: notifications
        - name: NOTIFICATIONS_PORT
          value: {{ .Values.galoy.notifications.grpcPort | quote }}

        - name: PRICE_HISTORY_HOST
          value: {{ .Values.price.history.host | quote }}
        - name: PRICE_HISTORY_PORT
          value: {{ .Values.price.history.port | quote }}

        - name: PRICE_SERVER_HOST
          value: {{ .Values.galoy.dealer.host | quote }}
        - name: PRICE_SERVER_PORT
          value: {{ .Values.galoy.dealer.port | quote }}

        - name: PRICE_HOST
          value: {{ .Values.price.realtime.host | quote }}

        - name: MATTERMOST_WEBHOOK_URL
          value: {{ .Values.galoy.mattermostWebhookUrl | quote }}

        {{ if .Values.galoy.exporter.probes.enabled }}
        livenessProbe:
          httpGet:
            path: /healthz
            port: {{ .Values.galoy.exporter.port }}
          initialDelaySeconds: {{ .Values.galoy.exporter.probes.liveness.initialDelaySeconds }}
          periodSeconds: {{ .Values.galoy.exporter.probes.liveness.periodSeconds }}
          failureThreshold: {{ .Values.galoy.exporter.probes.liveness.failureThreshold }}
          timeoutSeconds: {{ .Values.galoy.exporter.probes.liveness.timeoutSeconds }}

        readinessProbe:
          httpGet:
            path: /healthz
            port: {{ .Values.galoy.exporter.port }}
          initialDelaySeconds: {{ .Values.galoy.exporter.probes.readiness.initialDelaySeconds }}
          failureThreshold: {{ .Values.galoy.exporter.probes.readiness.failureThreshold }}
          successThreshold: {{ .Values.galoy.exporter.probes.readiness.successThreshold }}
          timeoutSeconds: {{ .Values.galoy.exporter.probes.readiness.timeoutSeconds }}
        {{ end }}

        volumeMounts:
        - name: custom-yaml
          mountPath: "/var/yaml/"

      volumes:
      - name: custom-yaml
        secret:
          secretName: "{{ template "galoy.config.name" . }}"
