{{- if .Values.galoy.websocket.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ template "galoy.websocket.fullname" . }}
  labels:
    app: {{ template "galoy.websocket.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: Helm
  annotations:
    cert-manager.io/cluster-issuer: {{ .Values.galoy.websocket.ingress.clusterIssuer }}
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600" # 1 hour
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600" # 1 hour
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "5s"
    nginx.ingress.kubernetes.io/proxy-next-upstream: "error timeout"
    nginx.ingress.kubernetes.io/proxy-next-upstream-tries: "3"
    nginx.ingress.kubernetes.io/limit-rpm: "10"
    nginx.ingress.kubernetes.io/limit-burst-multiplier: "2"
    nginx.ingress.kubernetes.io/limit-connections: "10"
    {{- with .Values.galoy.websocket.ingress.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  ingressClassName: nginx
  tls:
    {{- range .Values.galoy.websocket.ingress.hosts }}
    - hosts:
      - {{ . }}
      secretName: {{ printf "%s-tls" . }}
    {{- end }}
  rules:
  {{- range .Values.galoy.websocket.ingress.hosts }}
    - host: {{ . }}
      http:
        paths:
          {{- if $.Values.galoy.websocket.ingress.extraPaths }}
          {{- toYaml $.Values.galoy.websocket.ingress.extraPaths | nindent 10 }}
          {{- end }}
          - pathType: ImplementationSpecific
            path: /
            backend:
              service:
                name: {{ template "galoy.websocket.fullname" $ }}
                port:
                  number: {{ $.Values.galoy.websocket.port }}
  {{- end -}}
{{- end -}}
