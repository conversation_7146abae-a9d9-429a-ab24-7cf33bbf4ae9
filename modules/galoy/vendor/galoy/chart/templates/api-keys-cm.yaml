apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "galoy.apiKeys.fullname" . }}
data:
  api-keys.yml: |-
    app:
      key_prefix: {{ .Values.galoy.apiKeys.config.keyPrefix }}
      default_expiry_days: 90
    db:
      pool_size: 20
    server:
      port: {{ .Values.galoy.apiKeys.port }}
      jwks_url: "http://galoy-oathkeeper-api:4456/.well-known/jwks.json"
      api_key_auth_header: "X-API-KEY"
    tracing:
      endpoint: {{ .Values.tracing.otelExporterOtlpEndpoint }}
      service_name: "{{ .Values.tracing.prefix }}-{{ template "galoy.apiKeys.fullname" . }}"
