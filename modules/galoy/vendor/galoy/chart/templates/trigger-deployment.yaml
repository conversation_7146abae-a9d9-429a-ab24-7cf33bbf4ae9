apiVersion: apps/v1
kind: Deployment

metadata:
  name: {{ template "galoy.trigger.fullname" . }}
  labels:
    app: {{ template "galoy.trigger.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: <PERSON><PERSON>

    kube-monkey/enabled: enabled
    kube-monkey/identifier: {{ template "galoy.trigger.fullname" . }}
    kube-monkey/kill-mode: fixed
    kube-monkey/kill-value: "1"
    kube-monkey/mtbf: "3"

spec:

  replicas: {{ .Values.galoy.trigger.replicas }}

  selector:
    matchLabels:
      app: {{ template "galoy.trigger.fullname" . }}

  template:

    metadata:
      name: {{ template "galoy.trigger.fullname" . }}
      labels:
        app: {{ template "galoy.trigger.fullname" . }}
        kube-monkey/enabled: enabled
        kube-monkey/identifier: {{ template "galoy.trigger.fullname" . }}
    spec:
      serviceAccountName: {{ template "galoy.name" . }}

      initContainers:
      - name: wait-for-mongodb-migrate
        image: "groundnuty/k8s-wait-for:v2.0"
        args:
        - job-wr
        - {{ template "galoy.migration.jobname" . }}

      containers:
      - name: api
        image: "{{ .Values.galoy.images.trigger.repository }}@{{ .Values.galoy.images.trigger.digest }}"
        resources:
          {{ toYaml .Values.galoy.trigger.resources | nindent 10 }}
        ports:
        - name: http
          containerPort: {{ .Values.galoy.trigger.port }}
          protocol: TCP

        env:
        - name: HELMREVISION
          value: {{ .Release.Revision | quote }}
        - name: LOGLEVEL
          value: {{ .Values.galoy.trigger.logLevel | quote }}

        - name: NETWORK
          value: {{ .Values.galoy.network }}

        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: {{ .Values.tracing.otelExporterOtlpEndpoint | quote }}
        - name: TRACING_SERVICE_NAME
          value: "{{ .Values.tracing.prefix }}-{{ template "galoy.trigger.fullname" . }}"
        - name: TRIGGER_PORT
          value: {{ .Values.galoy.trigger.port | quote }}
        - name: WEBSOCKET_PORT
          value: {{ .Values.galoy.websocket.port | quote }}

        - name: PROXY_CHECK_APIKEY
          valueFrom:
            secretKeyRef:
              name: {{ .Values.galoy.proxyCheckExistingSecret.name }}
              key: {{ .Values.galoy.proxyCheckExistingSecret.key }}

        - name: LND_PRIORITY
          value: {{ .Values.galoy.lndPriority }}

{{/* Databases */}}
{{ include "galoy.mongodb.env" . | indent 8 }}
{{ include "galoy.redis.env" . | indent 8 }}

{{/* Bitcoin/LND */}}
{{ include "galoy.lnd1.env" . | indent 8 }}
{{ include "galoy.lnd2.env" . | indent 8 }}
{{ include "galoy.bria.env" . | indent 8 }}

{{/* API Specifics */}}
{{ include "galoy.twilio.env" . | indent 8 }}
{{ include "galoy.geetest.env" . | indent 8 }}

{{ include "galoy.kratos.env" . | indent 8 }}

        - name: OATHKEEPER_DECISION_ENDPOINT
          value: http://galoy-oathkeeper-api:4456

        - name: NOTIFICATIONS_HOST
          value: notifications
        - name: NOTIFICATIONS_PORT
          value: {{ .Values.galoy.notifications.grpcPort | quote }}

        - name: PRICE_HISTORY_HOST
          value: {{ .Values.price.history.host | quote }}
        - name: PRICE_HISTORY_PORT
          value: {{ .Values.price.history.port | quote }}

        - name: PRICE_SERVER_HOST
          value: {{ .Values.galoy.dealer.host | quote }}
        - name: PRICE_SERVER_PORT
          value: {{ .Values.galoy.dealer.port | quote }}

        - name: PRICE_HOST
          value: {{ .Values.price.realtime.host | quote }}

        - name: MATTERMOST_WEBHOOK_URL
          value: {{ .Values.galoy.mattermostWebhookUrl | quote }}

        {{ if .Values.galoy.trigger.backups.gcs.enabled }}
        - name: GCS_APPLICATION_CREDENTIALS_PATH
          value: "/var/secret/cloud.google.com/gcs-sa-key.json"
        {{ end }}

        {{ if .Values.galoy.trigger.backups.s3.enabled }}
        - name: AWS_ACCESS_KEY_ID
          valueFrom:
            secretKeyRef:
              name: {{ .Values.galoy.trigger.backups.s3.accessKeyExistingSecret.name | quote }}
              key: {{ .Values.galoy.trigger.backups.s3.accessKeyExistingSecret.key | quote }}
        - name: AWS_SECRET_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: {{ .Values.galoy.trigger.backups.s3.secretKeyExistingSecret.name | quote }}
              key: {{ .Values.galoy.trigger.backups.s3.secretKeyExistingSecret.key | quote }}
        - name: AWS_REGION
          value: {{ .Values.galoy.trigger.backups.s3.region | quote }}
        {{ end }}

        {{ if .Values.galoy.api.firebaseNotifications.enabled }}
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/tmp/service-account.json"
        {{ end }}

        - name: SVIX_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ .Values.galoy.svixExistingSecret.name }}
              key: {{ .Values.galoy.svixExistingSecret.secret_key }}

        {{ if .Values.galoy.trigger.probes.enabled }}
        livenessProbe:
          httpGet:
            path: /healthz
            port: {{ .Values.galoy.trigger.port }}
          initialDelaySeconds: {{ .Values.galoy.trigger.probes.liveness.initialDelaySeconds }}
          periodSeconds: {{ .Values.galoy.trigger.probes.liveness.periodSeconds }}
          failureThreshold: {{ .Values.galoy.trigger.probes.liveness.failureThreshold }}
          timeoutSeconds: {{ .Values.galoy.trigger.probes.liveness.timeoutSeconds }}

        readinessProbe:
          httpGet:
            path: /healthz
            port: {{ .Values.galoy.trigger.port }}
          initialDelaySeconds: {{ .Values.galoy.trigger.probes.readiness.initialDelaySeconds }}
          failureThreshold: {{ .Values.galoy.trigger.probes.readiness.failureThreshold }}
          successThreshold: {{ .Values.galoy.trigger.probes.readiness.successThreshold }}
          timeoutSeconds: {{ .Values.galoy.trigger.probes.readiness.timeoutSeconds }}
        {{ end }}

        volumeMounts:
        {{ if .Values.galoy.api.firebaseNotifications.enabled }}
        - name: firebase-notifications-service-account
          mountPath: /tmp
          readOnly: true
        {{ end }}

        {{ if .Values.galoy.trigger.backups.gcs.enabled }}
        - name: service-account
          mountPath: "/var/secret/cloud.google.com"
        {{ end }}

        - name: custom-yaml
          mountPath: "/var/yaml/"
      volumes:
      {{ if .Values.galoy.api.firebaseNotifications.enabled }}
      - name: firebase-notifications-service-account
        secret:
          secretName: {{ .Values.galoy.api.firebaseNotifications.existingSecret.name }}
          items:
          - key: {{ .Values.galoy.api.firebaseNotifications.existingSecret.key }}
            path: service-account.json
      {{ end }}

      {{ if .Values.galoy.trigger.backups.gcs.enabled }}
      - name: service-account
        secret:
          secretName: {{ .Values.galoy.trigger.backups.gcs.serviceAccountExistingSecret.name | quote }}
          items:
          - key: {{ .Values.galoy.trigger.backups.gcs.serviceAccountExistingSecret.key | quote }}
            path: "gcs-sa-key.json"
      {{ end }}

      - name: custom-yaml
        secret:
          secretName: "{{ template "galoy.config.name" . }}"
