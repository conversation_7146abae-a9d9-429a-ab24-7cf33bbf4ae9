apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "galoy.apiKeys.fullname" . }}
  labels:
    app: {{ template "galoy.apiKeys.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: <PERSON>lm
    kube-monkey/enabled: enabled
    kube-monkey/identifier: {{ template "galoy.apiKeys.fullname" . }}
    kube-monkey/kill-mode: fixed
    kube-monkey/kill-value: "1"
    kube-monkey/mtbf: "3"
spec:
  replicas: {{ .Values.galoy.apiKeys.replicas }}
  selector:
    matchLabels:
      app: {{ template "galoy.apiKeys.fullname" . }}
  template:
    metadata:
      name: {{ template "galoy.apiKeys.fullname" . }}
      labels:
        app: {{ template "galoy.apiKeys.fullname" . }}
        kube-monkey/enabled: enabled
        kube-monkey/identifier: {{ template "galoy.apiKeys.fullname" . }}
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/api-keys-cm.yaml") . | sha256sum }}
    spec:
      containers:
      - name: api-keys
        image: "{{ .Values.galoy.images.apiKeys.repository }}@{{ .Values.galoy.images.apiKeys.digest }}"
        resources:
          {{ toYaml .Values.galoy.apiKeys.resources | nindent 10 }}
        ports:
        - name: api
          containerPort: {{ .Values.galoy.apiKeys.port }}
          protocol: TCP
        env:
          - name: PG_CON
            valueFrom:
              secretKeyRef:
                name: {{ template "galoy.apiKeys.fullname" . }}
                key: pg-con
          - name: OTEL_EXPORTER_OTLP_ENDPOINT
            value: {{ .Values.tracing.otelExporterGrpcEndpoint | quote }}
          - name: API_KEYS_CONFIG
            value: "/api-keys.yml"
        volumeMounts:
        - name: config
          mountPath: "/api-keys.yml"
          subPath: "api-keys.yml"
          readOnly: true
      volumes:
      - name: config
        configMap:
          name: {{ template "galoy.apiKeys.fullname" . }}
# TODO: add probes
