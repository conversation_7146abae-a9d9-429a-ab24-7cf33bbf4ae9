{{- if .Values.galoy.api.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ template "galoy.api.fullname" . }}
  labels:
    app: {{ template "galoy.api.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: Helm
  annotations:
    cert-manager.io/cluster-issuer: {{ .Values.galoy.api.ingress.clusterIssuer }}
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization,X-API-KEY,oauth2-token"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600" # 1 hour
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600" # 1 hour
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "1s"
    nginx.ingress.kubernetes.io/proxy-next-upstream: "error timeout"
    nginx.ingress.kubernetes.io/proxy-next-upstream-tries: "3"
    nginx.ingress.kubernetes.io/limit-rpm: {{ .Values.galoy.api.ingress.limitRpm | quote }}
    nginx.ingress.kubernetes.io/limit-burst-multiplier: {{ .Values.galoy.api.ingress.limitBurstMultiplier | quote }}
    nginx.ingress.kubernetes.io/limit-connections: {{ .Values.galoy.api.ingress.limitConnections | quote }}
    nginx.ingress.kubernetes.io/auth-url: "http://galoy-oathkeeper-api.{{ .Release.Namespace }}.svc.cluster.local:4456/decisions"
    nginx.ingress.kubernetes.io/auth-method: GET
    nginx.ingress.kubernetes.io/auth-response-headers: "Authorization, Set-Cookie, X-Appcheck-Jti"
    nginx.ingress.kubernetes.io/auth-forward-cookie: "*"
    nginx.ingress.kubernetes.io/auth-snippet: |
      proxy_set_header X-Original-URL $request_uri;
      proxy_set_header X-Forwarded-Method $request_method;
      proxy_set_header X-Forwarded-Proto $scheme;
      proxy_set_header X-Forwarded-Host $host;
      proxy_set_header X-Forwarded-Uri $request_uri;
    {{- with .Values.galoy.api.ingress.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  ingressClassName: nginx
  tls:
    {{- range .Values.galoy.api.ingress.hosts }}
    - hosts:
      - {{ . }}
      secretName: {{ printf "%s-tls" . }}
    {{- end }}
  rules:
  {{- range .Values.galoy.api.ingress.hosts }}
    - host: {{ . }}
      http:
        paths:
          {{- if $.Values.galoy.api.ingress.extraPaths }}
          {{- toYaml $.Values.galoy.api.ingress.extraPaths | nindent 10 }}
          {{- end }}
          - pathType: ImplementationSpecific
            path: /graphql
            backend:
              service:
                name: "{{ $.Release.Name }}-router"
                port:
                  number: {{ $.Values.galoy.router.port }}
          - pathType: ImplementationSpecific
            path: /
            backend:
              service:
                name: {{ template "galoy.api.fullname" $ }}
                port:
                  number: {{ $.Values.galoy.api.port }}
  {{- end -}}
{{- end -}}
