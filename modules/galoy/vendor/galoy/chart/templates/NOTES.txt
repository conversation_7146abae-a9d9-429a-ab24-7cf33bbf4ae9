Thank you for installing Galoy Backend.
Your installation is successful! 🥳

{{ if .Values.secrets.create }}
All secrets that have been provisined are the defaults, 
which runs a risk of being vulnerable to attacks. This option
must be disabled in production and secrets must be created via
Terraform which does a better job at handling secrets.

For information about how this works, look at the dev environment here: 
https://github.com/GaloyMoney/charts/tree/main/dev/galoy
{{ end }}
