{{- if .Values.galoy.consent.ingress.enabled -}}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ template "galoy.consent.fullname" . }}
  labels:
    app: {{ template "galoy.consent.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: Helm
  annotations:
    cert-manager.io/cluster-issuer: {{ .Values.galoy.consent.ingress.clusterIssuer }}
    {{- with .Values.galoy.consent.ingress.annotations }}
    {{- toYaml . | nindent 4 }}
    {{- end }}
spec:
  ingressClassName: nginx
  tls:
    {{- range .Values.galoy.consent.ingress.hosts }}
    - hosts:
      - {{ . }}
      secretName: {{ printf "%s-tls" . }}
    {{- end }}
  rules:
  {{- range .Values.galoy.consent.ingress.hosts }}
    - host: {{ . }}
      http:
        paths:
          {{- if $.Values.galoy.consent.ingress.extraPaths }}
          {{- toYaml $.Values.galoy.consent.ingress.extraPaths | nindent 10 }}
          {{- end }}
          - pathType: ImplementationSpecific
            path: /
            backend:
              service:
                name: {{ template "galoy.consent.fullname" $ }}
                port:
                  number: {{ $.Values.galoy.consent.port }}
  {{- end -}}
{{- end -}}
