apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ template "galoy.notifications.fullname" . }}
data:
  notifications.yml: |-
    db:
      pool_size: {{ .Values.galoy.notifications.config.db.poolSize }}
    subgraph_server:
      port: {{ .Values.galoy.notifications.graphqlPort }}
      jwks_url: "http://galoy-oathkeeper-api:4456/.well-known/jwks.json"
    grpc_server:
      port: {{ .Values.galoy.notifications.grpcPort }}
    kratos_import:
      execute_import: {{ .Values.galoy.notifications.config.importFromKratos }}
    tracing:
      endpoint: {{ .Values.tracing.otelExporterOtlpEndpoint }}
      service_name: "{{ .Values.tracing.prefix }}-{{ template "galoy.notifications.fullname" . }}"
    app:
      push_executor:
        fcm:
          google_application_credentials_path: "/tmp/firebase-service-account/service-account.json"
      email_executor:
        enabled: {{ .Values.galoy.notifications.config.smtp.enabled }}
        smtp:
          username: {{ .Values.galoy.notifications.config.smtp.username }}
          from_email: {{ .Values.galoy.notifications.config.smtp.fromEmail }}
          from_name: {{ .Values.galoy.notifications.config.smtp.fromName }}
          relay: {{ .Values.galoy.notifications.config.smtp.relayHost }}
          port: {{ .Values.galoy.notifications.config.smtp.relayPort }}
      jobs:
        enabled: false
      link_email_reminder:
        account_liveness_threshold_minutes: {{ .Values.galoy.notifications.config.linkEmailReminder.accountLivenessThresholdMinutes }}
        account_age_threshold_minutes: {{ .Values.galoy.notifications.config.linkEmailReminder.accountAgeThresholdMinutes }}
        notification_cool_off_threshold_minutes: {{ .Values.galoy.notifications.config.linkEmailReminder.notificationCoolOffThresholdMinutes }}
