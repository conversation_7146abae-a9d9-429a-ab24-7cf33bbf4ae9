apiVersion: v1
kind: Service
metadata:
  name: {{ template "galoy.apiKeys.fullname" . }}
  labels:
    app: {{ template "galoy.apiKeys.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: Helm
spec:
  type: {{ .Values.galoy.apiKeys.serviceType }}
  ports:
    - port: {{ .Values.galoy.apiKeys.port }}
      targetPort: {{ .Values.galoy.apiKeys.port }}
      protocol: TCP
      name: http
  selector:
    app: {{ template "galoy.apiKeys.fullname" . }}
