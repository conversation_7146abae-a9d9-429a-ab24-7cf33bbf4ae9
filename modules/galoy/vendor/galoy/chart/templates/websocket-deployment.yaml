apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "galoy.websocket.fullname" . }}
  labels:
    app: {{ template "galoy.websocket.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: <PERSON><PERSON>
    kube-monkey/enabled: enabled
    kube-monkey/identifier: {{ template "galoy.websocket.fullname" . }}
    kube-monkey/kill-mode: fixed
    kube-monkey/kill-value: "1"
    kube-monkey/mtbf: "3"
spec:
  replicas: {{ .Values.galoy.websocket.replicas }}
  selector:
    matchLabels:
      app: {{ template "galoy.websocket.fullname" . }}
  template:
    metadata:
      name: {{ template "galoy.websocket.fullname" . }}
      labels:
        app: {{ template "galoy.websocket.fullname" . }}
        kube-monkey/enabled: enabled
        kube-monkey/identifier: {{ template "galoy.websocket.fullname" . }}
    spec:
      serviceAccountName: {{ template "galoy.name" . }}
      initContainers:
      - name: wait-for-mongodb-migrate
        image: "groundnuty/k8s-wait-for:v2.0"
        args:
        - job-wr
        - {{ template "galoy.migration.jobname" . }}
      containers:
      - name: websocket
        image: "{{ .Values.galoy.images.websocket.repository }}@{{ .Values.galoy.images.websocket.digest }}"
        resources:
          {{ toYaml .Values.galoy.websocket.resources | nindent 10 }}
        ports:
        - name: http
          containerPort: {{ .Values.galoy.websocket.port }}
          protocol: TCP
        env:
        - name: HELMREVISION
          value: {{ .Release.Revision | quote }}
        - name: LOGLEVEL
          value: {{ .Values.galoy.websocket.logLevel | quote }}
        - name: NETWORK
          value: {{ .Values.galoy.network }}
        - name: GALOY_API_PORT
          value: {{ .Values.galoy.api.port | quote }}
        - name: GALOY_ADMIN_PORT
          value: {{ .Values.galoy.admin.port | quote }}
        - name: WEBSOCKET_PORT
          value: {{ .Values.galoy.websocket.port | quote }}
        - name: TRIGGER_PORT
          value: {{ .Values.galoy.trigger.port | quote }}
        - name: OTEL_EXPORTER_OTLP_ENDPOINT
          value: {{ .Values.tracing.otelExporterOtlpEndpoint | quote }}
        - name: TRACING_SERVICE_NAME
          value: "{{ .Values.tracing.prefix }}-{{ template "galoy.websocket.fullname" . }}"
{{/* Databases */}}
{{ include "galoy.mongodb.env" . | indent 8 }}
{{ include "galoy.redis.env" . | indent 8 }}

{{/* Bitcoin/LND */}}
{{ include "galoy.lnd1.env" . | indent 8 }}
{{ include "galoy.lnd2.env" . | indent 8 }}
{{ include "galoy.bria.env" . | indent 8 }}

{{/* API Specifics */}}
{{ include "galoy.twilio.env" . | indent 8 }}
{{ include "galoy.geetest.env" . | indent 8 }}
{{ include "galoy.telegram.env" . | indent 8 }}

{{ include "galoy.kratos.env" . | indent 8 }}

        - name: OATHKEEPER_DECISION_ENDPOINT
          value: http://galoy-oathkeeper-api:4456

        - name: NOTIFICATIONS_HOST
          value: notifications
        - name: NOTIFICATIONS_PORT
          value: {{ .Values.galoy.notifications.grpcPort | quote }}

        - name: PRICE_HISTORY_HOST
          value: {{ .Values.price.history.host | quote }}
        - name: PRICE_HISTORY_PORT
          value: {{ .Values.price.history.port | quote }}
        - name: PRICE_SERVER_HOST
          value: {{ .Values.galoy.dealer.host | quote }}
        - name: PRICE_SERVER_PORT
          value: {{ .Values.galoy.dealer.port | quote }}

        - name: PRICE_HOST
          value: {{ .Values.price.realtime.host | quote }}

        - name: SVIX_SECRET
          valueFrom:
            secretKeyRef:
              name: {{ .Values.galoy.svixExistingSecret.name }}
              key: {{ .Values.galoy.svixExistingSecret.secret_key }}

        - name: PROXY_CHECK_APIKEY
          valueFrom:
            secretKeyRef:
              name: {{ .Values.galoy.proxyCheckExistingSecret.name }}
              key: {{ .Values.galoy.proxyCheckExistingSecret.key }}

        - name: LND_PRIORITY
          value: {{ .Values.galoy.lndPriority }}

        {{ if .Values.galoy.websocket.firebaseNotifications.enabled }}
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: "/tmp/firebase-service-account/service-account.json"
        {{ end }}
        livenessProbe:
          tcpSocket:
            port: {{ .Values.galoy.websocket.port }}
          initialDelaySeconds: {{ .Values.galoy.websocket.probes.liveness.initialDelaySeconds }}
          periodSeconds: {{ .Values.galoy.websocket.probes.liveness.periodSeconds }}
          failureThreshold: {{ .Values.galoy.websocket.probes.liveness.failureThreshold }}
          timeoutSeconds: {{ .Values.galoy.websocket.probes.liveness.timeoutSeconds }}
        readinessProbe:
          tcpSocket:
            port: {{ .Values.galoy.websocket.port }}
          initialDelaySeconds: {{ .Values.galoy.websocket.probes.readiness.initialDelaySeconds }}
          failureThreshold: {{ .Values.galoy.websocket.probes.readiness.failureThreshold }}
          successThreshold: {{ .Values.galoy.websocket.probes.readiness.successThreshold }}
          timeoutSeconds: {{ .Values.galoy.websocket.probes.readiness.timeoutSeconds }}
        volumeMounts:
        {{ if .Values.galoy.api.firebaseNotifications.enabled }}
        - name: firebase-notifications-service-account
          mountPath: /tmp/firebase-service-account/
          readOnly: true
        {{ end }}
        - name: custom-yaml
          mountPath: "/var/yaml/"
      volumes:
      {{ if .Values.galoy.api.firebaseNotifications.enabled }}
      - name: firebase-notifications-service-account
        secret:
          secretName: {{ .Values.galoy.api.firebaseNotifications.existingSecret.name }}
          items:
          - key: {{ .Values.galoy.api.firebaseNotifications.existingSecret.key }}
            path: service-account.json
      {{ end }}
      - name: custom-yaml
        secret:
          secretName: "{{ template "galoy.config.name" . }}"
