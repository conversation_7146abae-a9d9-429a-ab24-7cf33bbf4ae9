apiVersion: batch/v1
kind: CronJob

metadata:
  name: {{ template "galoy.cron.jobname" . }}
  labels:
    app: {{ template "galoy.cron.jobname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: <PERSON><PERSON>

spec:
  schedule: 0 2 * * * # Fire everyday at 02:00AM

  jobTemplate:
    spec:
      activeDeadlineSeconds: 7201

      template:
        metadata:
          labels:
            app: {{ template "galoy.cron.jobname" . }}
        spec:
          serviceAccountName: {{ template "galoy.name" . }}
          restartPolicy: OnFailure

          initContainers:
          - name: wait-for-mongodb-migrate
            image: "groundnuty/k8s-wait-for:v2.0"
            args:
            - job-wr
            - {{ template "galoy.migration.jobname" . }}

          containers:
          - name: {{ template "galoy.cron.jobname" . }}
            image: "{{ .Values.galoy.images.cron.repository }}@{{ .Values.galoy.images.cron.digest }}"
            resources:
              {{ toYaml .Values.galoy.galoyCron.resources | nindent 14 }}
            env:
            - name: HELMREVISION
              value: {{ .Release.Revision | quote }}
            - name: LOGLEVEL
              value: {{ .Values.galoy.trigger.logLevel | quote }}

            - name: NETWORK
              value: {{ .Values.galoy.network }}

            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: {{ .Values.tracing.otelExporterOtlpEndpoint | quote }}
            - name: TRACING_SERVICE_NAME
              value: "{{ .Values.tracing.prefix }}-{{ template "galoy.cron.jobname" . }}"

            - name: NOTIFICATIONS_HOST
              value: notifications
            - name: NOTIFICATIONS_PORT
              value: {{ .Values.galoy.notifications.grpcPort | quote }}

            - name: PRICE_HOST
              value: {{ .Values.price.realtime.host | quote }}

            - name: PRICE_SERVER_HOST
              value: {{ .Values.galoy.dealer.host | quote }}
            - name: PRICE_SERVER_PORT
              value: {{ .Values.galoy.dealer.port | quote }}

            - name: TRIGGER_PORT
              value: {{ .Values.galoy.trigger.port | quote }}
            - name: WEBSOCKET_PORT
              value: {{ .Values.galoy.websocket.port | quote }}

            - name: PROXY_CHECK_APIKEY
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.galoy.proxyCheckExistingSecret.name }}
                  key: {{ .Values.galoy.proxyCheckExistingSecret.key }}

            - name: LND_PRIORITY
              value: {{ .Values.galoy.lndPriority }}

{{/* Databases */}}
{{ include "galoy.mongodb.env" . | indent 12 }}
{{ include "galoy.redis.env" . | indent 12 }}

{{/* Bitcoin/LND */}}
{{ include "galoy.lnd1.env" . | indent 12 }}
{{ include "galoy.lnd2.env" . | indent 12 }}
{{ include "galoy.bria.env" . | indent 12 }}

{{ include "galoy.kratos.env" . | indent 12 }}
{{ include "galoy.twilio.env" . | indent 12 }}

            - name: OATHKEEPER_DECISION_ENDPOINT
              value: http://galoy-oathkeeper-api:4456

            - name: SVIX_SECRET
              valueFrom:
                secretKeyRef:
                  name: {{ .Values.galoy.svixExistingSecret.name }}
                  key: {{ .Values.galoy.svixExistingSecret.secret_key }}

            {{ if .Values.galoy.api.firebaseNotifications.enabled }}
            - name: GOOGLE_APPLICATION_CREDENTIALS
              value: "/tmp/service-account.json"
            {{ end }}

            volumeMounts:
            {{ if .Values.galoy.api.firebaseNotifications.enabled }}
            - name: firebase-notifications-service-account
              mountPath: /tmp
              readOnly: true
            {{ end }}

            - name: custom-yaml
              mountPath: "/var/yaml/"

          volumes:
          {{ if .Values.galoy.api.firebaseNotifications.enabled }}
          - name: firebase-notifications-service-account
            secret:
              secretName: {{ .Values.galoy.api.firebaseNotifications.existingSecret.name }}
              items:
              - key: {{ .Values.galoy.api.firebaseNotifications.existingSecret.key }}
                path: service-account.json
          {{ end }}

          - name: custom-yaml
            secret:
              secretName: "{{ template "galoy.config.name" . }}"
