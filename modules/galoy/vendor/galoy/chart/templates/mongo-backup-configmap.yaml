apiVersion: v1
kind: ConfigMap

metadata:
  name: mongo-backup-configmap
  labels:
    app: {{ template "galoy.mongoBackupCron.jobname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: Helm

data:
  backup.sh: |
    {{ if or .Values.galoy.trigger.backups.gcs.enabled .Values.galoy.trigger.backups.s3.enabled }}
    echo "Backup script starts"
    set -e
    LOG_TIME=$(date +%s)
    BACKUP_NAME="$NETWORK-$LOG_TIME.gz"
    echo "Backing up mongodb"
    mongodump --host=$MONGODB_ADDRESS --port=$MONGODB_PORT --username=$MONGODB_USER --password=$MONGODB_PASSWORD --gzip --archive=$BACKUP_NAME -d=$MONGODB_DB --readPreference=secondary --numParallelCollections=4

    {{ if .Values.galoy.trigger.backups.s3.enabled }}
    export S3_BUCKET="{{ .Values.galoy.trigger.backups.s3.bucketName }}"
    echo "Uploading backup $BACKUP_NAME to s3"
    aws s3 cp $BACKUP_NAME s3://$S3_BUCKET/mongodb/$BACKUP_NAME
    {{ end }}

    {{ if .Values.galoy.trigger.backups.gcs.enabled }}
    export GCS_BUCKET="{{ .Values.galoy.trigger.backups.gcs.bucketName }}"
    echo "Activating service account"
    gcloud auth activate-service-account --key-file=$GOOGLE_APPLICATION_CREDENTIALS
    echo "Uploading backup $BACKUP_NAME to gcs"
    gsutil -m cp $BACKUP_NAME gs://$GCS_BUCKET/mongodb/$BACKUP_NAME 2>&1
    echo "Uploaded backup successfully"
    {{ end }}

    {{ else }}
    echo "Warning: Automatic MongoDB backups are disabled"
    exit 0
    {{ end }}
