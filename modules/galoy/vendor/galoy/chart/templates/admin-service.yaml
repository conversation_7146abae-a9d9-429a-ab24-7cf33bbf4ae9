apiVersion: v1
kind: Service

metadata:
  name: {{ template "galoy.admin.fullname" . }}
  labels:
    app: {{ template "galoy.admin.fullname" . }}
    chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    release: "{{ .Release.Name }}"
    app.kubernetes.io/managed-by: Helm

spec:
  type: {{ .Values.galoy.admin.serviceType }}
  ports:
    - port: {{ .Values.galoy.admin.port }}
      targetPort: {{ .Values.galoy.admin.port }}
      protocol: TCP
      name: http
  selector:
    app: {{ template "galoy.api.fullname" . }}
