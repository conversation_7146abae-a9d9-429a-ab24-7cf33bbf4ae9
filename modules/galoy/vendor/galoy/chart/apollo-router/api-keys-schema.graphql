type ApiKey {
	id: ID!
	name: String!
	createdAt: Timestamp!
	revoked: Boolean!
	expired: Boolean!
	lastUsedAt: Timestamp
	expiresAt: Timestamp
	readOnly: Boolean!
	scopes: [Scope!]!
}

input ApiKeyCreateInput {
	name: String!
	expireInDays: Int
	scopes: [Scope!]! = [READ, WRITE]
}

type ApiKeyCreatePayload {
	apiKey: ApiKey!
	apiKeySecret: String!
}

input ApiKeyRevokeInput {
	id: ID!
}

type ApiKeyRevokePayload {
	apiKey: ApiKey!
}





type Mutation {
	apiKeyCreate(input: ApiKeyCreateInput!): ApiKeyCreatePayload!
	apiKeyRevoke(input: ApiKeyRevokeInput!): ApiKeyRevokePayload!
}


enum Scope {
	READ
	WRITE
	RECEIVE
}


scalar Timestamp

extend type User @key(fields: "id") {
	id: ID! @external
	apiKeys: [ApiKey!]!
}

directive @include(if: Boolean!) on FIELD | FRAGMENT_SPREAD | INLINE_FRAGMENT
directive @skip(if: Boolean!) on FIELD | FRAGMENT_SPREAD | INLINE_FRAGMENT
extend schema @link(
	url: "https://specs.apollo.dev/federation/v2.3",
	import: ["@key", "@tag", "@shareable", "@inaccessible", "@override", "@external", "@provides", "@requires", "@composeDirective", "@interfaceObject"]
)
