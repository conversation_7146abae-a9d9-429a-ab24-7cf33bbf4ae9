enum Icon {
	ARROW_RIGHT
	ARROW_LEFT
	BACK_SPACE
	BANK
	BITCOIN
	BOOK
	BTC_BOOK
	CARET_DOWN
	CARET_LEFT
	CARET_RIGHT
	CARET_UP
	CHECK_CIRCLE
	CHECK
	CLOSE
	CLOSE_CROSS_WITH_BAC<PERSON>GROUND
	COINS
	PEOPLE
	COPY_PASTE
	DOLLAR
	EYE_SLASH
	EYE
	FILTER
	GLOBE
	GRAPH
	IMAGE
	INFO
	LIGHTNING
	LINK
	LOADING
	MAGNIFYING_GLASS
	MAP
	MENU
	PENCIL
	NOTE
	RANK
	QR_CODE
	QUESTION
	RECEIVE
	SEND
	SETTINGS
	SHARE
	TRANSFER
	USER
	VIDEO
	WARNING
	WARNING_WITH_BACKGROUND
	PAYMENT_SUCCESS
	PAYMENT_PENDING
	PAYMENT_ERROR
	BELL
	REFRESH
}


type Mutation {
	statefulNotificationAcknowledge(input: StatefulNotificationAcknowledgeInput!): StatefulNotificationAcknowledgePayload!
}

union NotificationAction = OpenDeepLinkAction | OpenExternalLinkAction

type OpenDeepLinkAction {
	deepLink: String!
}

type OpenExternalLinkAction {
	url: String!
}

"""
Information about pagination in a connection
"""
type PageInfo @shareable {
	"""
	When paginating backwards, are there more items?
	"""
	hasPreviousPage: Boolean!
	"""
	When paginating forwards, are there more items?
	"""
	hasNextPage: Boolean!
	"""
	When paginating backwards, the cursor to continue.
	"""
	startCursor: String
	"""
	When paginating forwards, the cursor to continue.
	"""
	endCursor: String
}


type StatefulNotification {
	id: ID!
	title: String!
	body: String!
	deepLink: String
	action: NotificationAction
	createdAt: Timestamp!
	acknowledgedAt: Timestamp
	bulletinEnabled: Boolean!
	icon: Icon
}

input StatefulNotificationAcknowledgeInput {
	notificationId: ID!
}

type StatefulNotificationAcknowledgePayload {
	notification: StatefulNotification!
}

type StatefulNotificationConnection @shareable {
	"""
	Information to aid in pagination.
	"""
	pageInfo: PageInfo!
	"""
	A list of edges.
	"""
	edges: [StatefulNotificationEdge!]!
	"""
	A list of nodes.
	"""
	nodes: [StatefulNotification!]!
}

"""
An edge in a connection.
"""
type StatefulNotificationEdge @shareable {
	"""
	The item at the end of the edge
	"""
	node: StatefulNotification!
	"""
	A cursor for use in pagination
	"""
	cursor: String!
}


scalar Timestamp

extend type User @key(fields: "id") {
	id: ID! @external
	statefulNotifications(first: Int!, after: String): StatefulNotificationConnection!
	statefulNotificationsWithoutBulletinEnabled(first: Int!, after: String): StatefulNotificationConnection!
	unacknowledgedStatefulNotificationsWithoutBulletinEnabledCount: Int!
	unacknowledgedStatefulNotificationsWithBulletinEnabled(first: Int!, after: String): StatefulNotificationConnection!
}

directive @include(if: Boolean!) on FIELD | FRAGMENT_SPREAD | INLINE_FRAGMENT
directive @skip(if: Boolean!) on FIELD | FRAGMENT_SPREAD | INLINE_FRAGMENT
extend schema @link(
	url: "https://specs.apollo.dev/federation/v2.3",
	import: ["@key", "@tag", "@shareable", "@inaccessible", "@override", "@external", "@provides", "@requires", "@composeDirective", "@interfaceObject"]
)
