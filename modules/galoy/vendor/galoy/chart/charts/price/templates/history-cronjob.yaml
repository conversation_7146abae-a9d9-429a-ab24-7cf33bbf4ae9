apiVersion: batch/v1
kind: CronJob
metadata:
  name: {{ include "price.history.fullname" . }}-cronjob
  labels:
    app.kubernetes.io/managed-by: <PERSON><PERSON>
spec:
  schedule: "2 * * * *"
  jobTemplate:
    spec:
      activeDeadlineSeconds: 60
      template:
        metadata:
          labels:
            app: {{ include "price.history.fullname" . }}-cronjob
        spec:
          containers:
          - name: update-price
            image: "{{ .Values.history.image.repository }}@{{ .Values.history.image.digest }}"
            args:
            - "servers/history/cron.js"
            resources:
              {{ toYaml .Values.history.cron.resources | nindent 14 }}
            env:
            - name: DB_HOST
              value: {{ .Values.history.postgresqlHost }}
            - name: DB_PWD
              valueFrom:
                secretKeyRef:
                  name: {{ include "price.history.fullname" . }}-postgres-creds
                  key: password
            - name: DB_USER
              valueFrom:
                secretKeyRef:
                  name: {{ include "price.history.fullname" . }}-postgres-creds
                  key: username
            - name: DB_DB
              valueFrom:
                secretKeyRef:
                  name: {{ include "price.history.fullname" . }}-postgres-creds
                  key: database
            - name: OTEL_EXPORTER_OTLP_ENDPOINT
              value: {{ .Values.history.tracing.otelExporterOtlpEndpoint }}
            - name: OTEL_SERVICE_NAME
              value: {{ .Values.history.tracing.otelServiceName }}-cron
            - name: NOTIFICATIONS_ENDPOINT
              value: {{ .Values.history.notificationsEndpoint }}
            volumeMounts:
            - name: custom-yaml
              mountPath: "/var/yaml/"
          volumes:
          - name: custom-yaml
            secret:
              secretName: {{ include "price.history.fullname" . }}-config
          restartPolicy: OnFailure
