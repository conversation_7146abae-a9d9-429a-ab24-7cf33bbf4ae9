apiVersion: batch/v1
kind: Job
metadata:
  name: {{ include "price.history.fullname" . }}-postgres-migrate-{{ .Release.Revision }}
spec:
  backoffLimit: 3
  template:
    spec:
      containers:
      - name: migrate
        image: "{{ .Values.history.migrateImage.repository }}@{{ .Values.history.migrateImage.digest }}"
        resources:
          {{ toYaml .Values.history.migrationJob.resources | nindent 10 }}
        env:
        - name: DB_HOST
          value: {{ .Values.history.postgresqlHost }}
        - name: DB_PWD
          valueFrom:
            secretKeyRef:
              name: {{ include "price.history.fullname" . }}-postgres-creds
              key: password
        - name: DB_USER
          valueFrom:
            secretKeyRef:
              name: {{ include "price.history.fullname" . }}-postgres-creds
              key: username
        - name: DB_DB
          valueFrom:
            secretKeyRef:
              name: {{ include "price.history.fullname" . }}-postgres-creds
              key: database
      restartPolicy: Never
