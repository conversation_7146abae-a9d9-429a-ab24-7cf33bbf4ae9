apiVersion: v1
kind: Service
metadata:
  name: {{ include "price.history.fullname" . }}
  labels:
    {{- include "price.history.labels" . | nindent 4 }}
spec:
  type: {{ .Values.history.service.type }}
  ports:
    - port: {{ .Values.history.service.grpc }}
      targetPort: grpc
      protocol: TCP
      name: grpc
    - port: {{ .Values.history.service.prometheus }}
      targetPort: prometheus
      protocol: TCP
      name: prometheus
  selector:
    {{- include "price.history.selectorLabels" . | nindent 4 }}
