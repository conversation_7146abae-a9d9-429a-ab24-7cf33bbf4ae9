apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "price.realtime.fullname" . }}
  labels:
    {{- include "price.realtime.labels" . | nindent 4 }}
    kube-monkey/enabled: enabled
    kube-monkey/identifier: {{ include "price.realtime.fullname" . }}
    kube-monkey/kill-mode: fixed
    kube-monkey/kill-value: "1"
    kube-monkey/mtbf: "3"
spec:
  selector:
    matchLabels:
      {{- include "price.realtime.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/realtime-config-secret.yaml") . | sha256sum }}
      {{- with .Values.realtime.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "price.realtime.selectorLabels" . | nindent 8 }}
        kube-monkey/enabled: enabled
        kube-monkey/identifier: {{ include "price.realtime.fullname" . }}
    spec:
      containers:
        - name: realtime
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ $.Values.realtime.image.repository }}@{{ $.Values.realtime.image.digest }}"
          ports:
            - name: grpc
              containerPort: {{ .Values.realtime.service.grpc }}
              protocol: TCP
            - name: prometheus
              containerPort: {{ .Values.realtime.service.prometheus }}
              protocol: TCP
          env:
          - name: OTEL_EXPORTER_OTLP_ENDPOINT
            value: {{ .Values.realtime.tracing.otelExporterOtlpEndpoint }}
          - name: OTEL_SERVICE_NAME
            value: {{ .Values.realtime.tracing.otelServiceName }}
          - name: TRACING_ENABLE_FILTER
            value: {{ .Values.realtime.tracing.enableFilter | quote }}
          volumeMounts:
            - name: custom-yaml
              mountPath: "/var/yaml/"
          livenessProbe:
            exec:
              command: ["/bin/grpc_health_probe", "-addr=:50051"]
            initialDelaySeconds: 45
            timeoutSeconds: 2
          readinessProbe:
            exec:
              command: ["/bin/grpc_health_probe", "-addr=:50051"]
            timeoutSeconds: 2
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      {{- with .Values.realtime.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.realtime.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.realtime.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      volumes:
        - name: custom-yaml
          secret:
            secretName: {{ include "price.realtime.fullname" . }}-config
