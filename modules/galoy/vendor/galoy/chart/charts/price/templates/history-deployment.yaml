apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "price.history.fullname" . }}
  labels:
    {{- include "price.history.labels" . | nindent 4 }}
    kube-monkey/enabled: enabled
    kube-monkey/identifier: {{ include "price.history.fullname" . }}
    kube-monkey/kill-mode: fixed
    kube-monkey/kill-value: "1"
    kube-monkey/mtbf: "3"
spec:
  selector:
    matchLabels:
      {{- include "price.history.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        checksum/config: {{ include (print $.Template.BasePath "/history-config-secret.yaml") . | sha256sum }}
      {{- with .Values.history.podAnnotations }}
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
        {{- include "price.history.selectorLabels" . | nindent 8 }}
        kube-monkey/enabled: enabled
        kube-monkey/identifier: {{ include "price.history.fullname" . }}
    spec:
      containers:
        - name: history
          image: "{{ .Values.history.image.repository }}@{{ .Values.history.image.digest }}"
          ports:
            - name: grpc
              containerPort: {{ .Values.history.service.grpc }}
              protocol: TCP
            - name: prometheus
              containerPort: {{ .Values.history.service.prometheus }}
              protocol: TCP
          env:
          - name: DB_HOST
            value: {{ .Values.history.postgresqlHost }}
          - name: DB_PWD
            valueFrom:
              secretKeyRef:
                name: {{ include "price.history.fullname" . }}-postgres-creds
                key: password
          - name: DB_USER
            valueFrom:
              secretKeyRef:
                name: {{ include "price.history.fullname" . }}-postgres-creds
                key: username
          - name: DB_DB
            valueFrom:
              secretKeyRef:
                name: {{ include "price.history.fullname" . }}-postgres-creds
                key: database
          - name: OTEL_EXPORTER_OTLP_ENDPOINT
            value: {{ .Values.history.tracing.otelExporterOtlpEndpoint }}
          - name: OTEL_SERVICE_NAME
            value: {{ .Values.history.tracing.otelServiceName }}
          - name: TRACING_ENABLE_FILTER
            value: {{ .Values.history.tracing.enableFilter | quote }}
          - name: NOTIFICATIONS_ENDPOINT
            value: {{ .Values.history.notificationsEndpoint }}
          volumeMounts:
            - name: custom-yaml
              mountPath: "/var/yaml/"
          livenessProbe:
            exec:
              command: ["/bin/grpc_health_probe", "-addr=:50052"]
            initialDelaySeconds: 45
            timeoutSeconds: 2
          readinessProbe:
            exec:
              command: ["/bin/grpc_health_probe", "-addr=:50052"]
            timeoutSeconds: 2
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
      - name: custom-yaml
        secret:
          secretName: {{ include "price.history.fullname" . }}-config
