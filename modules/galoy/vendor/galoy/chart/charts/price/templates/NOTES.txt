export REALTIME_POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name=price-realtime,app.kubernetes.io/instance={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}")
export HISTORY_POD_NAME=$(kubectl get pods --namespace {{ .Release.Namespace }} -l "app.kubernetes.io/name=price-history,app.kubernetes.io/instance={{ .Release.Name }}" -o jsonpath="{.items[0].metadata.name}")
export REALTIME_CONTAINER_PORT=$(kubectl get pod --namespace {{ .Release.Namespace }} $REALTIME_POD_NAME -o jsonpath="{.spec.containers[0].ports[0].containerPort}")
export HISTORY_CONTAINER_PORT=$(kubectl get pod --namespace {{ .Release.Namespace }} $HISTORY_POD_NAME -o jsonpath="{.spec.containers[0].ports[0].containerPort}")
echo "Realtime price server rpc url: http://127.0.0.1:50051"
echo "History price server rpc url: http://127.0.0.1:50052"
kubectl --namespace {{ .Release.Namespace }} port-forward $REALTIME_POD_NAME 50051:$REALTIME_CONTAINER_PORT
kubectl --namespace {{ .Release.Namespace }} port-forward $HISTORY_POD_NAME 50052:$HISTORY_CONTAINER_PORT
