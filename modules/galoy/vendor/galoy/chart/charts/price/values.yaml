# Default values for price.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1
image:
  repository: us.gcr.io/galoy-org/price
  digest: "sha256:f69ce5cd409254e5d37be1e5fc0d957545333d57bf7892a084cb6f3d6934bbea"
  git_ref: "705dd6a"
imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""
securityContext:
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  runAsUser: 1000
resources: {}
# We usually recommend not to specify default resources and to leave this as a conscious
# choice for the user. This also increases chances charts run on environments with little
# resources, such as Minikube. If you do want to specify resources, uncomment the following
# lines, adjust them as necessary, and remove the curly braces after 'resources:'.
# limits:
#   cpu: 100m
#   memory: 128Mi
# requests:
#   cpu: 100m
#   memory: 128Mi

nodeSelector: {}
tolerations: []
affinity: {}
realtime:
  tracing:
    otelExporterOtlpEndpoint: http://localhost:4318/v1/traces
    otelServiceName: galoy-price-realtime-dev
    enableFilter: true
  config: {}
  image:
    repository: us.gcr.io/galoy-org/price
    digest: "sha256:7f686252d53368e9589fe97610c21cf99d3c8a2be8d65df693e42900dbcb0185"
    git_ref: "2383bde"
  service:
    type: ClusterIP
    prometheus: 9464
    grpc: 50051
  podAnnotations:
    prometheus.io/path: /metrics
    prometheus.io/port: "9464"
    prometheus.io/scrape: "true"
history:
  notificationsEndpoint: notifications:6685
  tracing:
    otelExporterOtlpEndpoint: http://localhost:4318/v1/traces
    otelServiceName: galoy-price-history-dev
    enableFilter: true
  valuesOverride: {}
  image:
    repository: us.gcr.io/galoy-org/price-history
    digest: "sha256:04a2608539dffdc238acea3ed112e8e2cf05c4d2fc1d1349f5caa57313a974c0"
  service:
    type: ClusterIP
    prometheus: 9464
    grpc: 50052
  migrateImage:
    repository: us.gcr.io/galoy-org/price-history-migrate
    digest: sha256:af4693788152cb5ffa7dc508faea8f76a958620bf174aafdf3af4d85190afdbc
  postgresqlHost: postgresql
  cron:
    resources: {}
  migrationJob:
    resources: {}
