resource "random_password" "hydra_secret_cookies" {
  length  = 32
  special = false
}

resource "random_password" "hydra_secret_system" {
  length  = 32
  special = false
}

resource "kubernetes_secret" "hydra_secret" {
  metadata {
    name      = "galoy-hydra"
    namespace = local.galoy_namespace
  }

  data = {
    "dsn"           = module.hydra_postgresql.creds["hydra"].conn
    "secretsSystem" = random_password.hydra_secret_system.result
    "secretsCookie" = random_password.hydra_secret_cookies.result
  }
}
