terraform {
  required_providers {
    twilio = {
      source  = "RJPearson94/twilio"
      version = ">= 0.2.1"
    }
    postgresql = {
      source  = "cyrilgdn/postgresql"
      version = "1.24.0"
    }
    jose = {
      source  = "bluemill/jose"
      version = "1.0.0"
    }
  }
}

resource "kubernetes_namespace" "galoy" {
  metadata {
    name = local.galoy_namespace
  }
}
resource "kubernetes_secret" "firebase_service_account" {
  metadata {
    name      = "galoyapp-firebase-serviceaccount"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = {
    "galoyapp-firebase-serviceaccount.json" : local.firebase_service_account
  }
}

resource "kubernetes_secret" "dropbox_access_token" {
  metadata {
    name      = "dropbox-access-token"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = {
    "token" : local.dropbox_access_token
  }
}

resource "kubernetes_secret" "svix_secret" {
  metadata {
    name      = "svix-secret"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }
  data = {
    "svix-secret" = local.svix_secret
  }
}

resource "kubernetes_secret" "gcs_sa_key" {
  metadata {
    name      = "gcs-sa-key"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = {
    "gcs-sa-key.json" : local.backups_sa_creds
  }
}

resource "kubernetes_secret" "s3_creds" {
  metadata {
    name      = "s3-creds"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }
  data = {
    "access-key" : local.aws_access_key
    "secret-key" : local.aws_secret_key
  }
}

provider "twilio" {
  account_sid = local.twilio_account_sid
  auth_token  = local.twilio_auth_token
}

resource "twilio_account_sub_account" "galoy_sub_account" {
  friendly_name = "${local.name_prefix}-sub-account"
}

provider "twilio" {
  alias                      = "sub"
  skip_credential_validation = true
  account_sid                = twilio_account_sub_account.galoy_sub_account.sid
  auth_token                 = twilio_account_sub_account.galoy_sub_account.auth_token
}

resource "twilio_verify_service" "service" {
  provider                     = twilio.sub
  friendly_name                = local.twilio_verify_service_friendly_name
  lookup_enabled               = false
  do_not_share_warning_enabled = true
}

resource "twilio_messaging_service" "service" {
  provider      = twilio.sub
  friendly_name = local.twilio_messaging_service_friendly_name
}

resource "kubernetes_secret" "twilio_secret" {
  metadata {
    name      = "twilio-secret"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }
  data = {
    TWILIO_VERIFY_SERVICE_ID    = twilio_verify_service.service.id
    TWILIO_ACCOUNT_SID          = twilio_account_sub_account.galoy_sub_account.sid
    TWILIO_AUTH_TOKEN           = twilio_account_sub_account.galoy_sub_account.auth_token
    TWILIO_MESSAGING_SERVICE_ID = twilio_messaging_service.service.sid
    TWILIO_WELCOME_CONTENT_SID  = local.twilio_messaging_welcome_content_sid
  }
}

resource "kubernetes_secret" "telegram_secret" {
  metadata {
    name      = "telegram-secret"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }
  data = {
    TELEGRAM_PASSPORT_PRIVATE_KEY = local.telegram_private_key
    TELEGRAM_BOT_API_TOKEN        = local.telegram_api_token
  }
}

data "kubernetes_secret" "lnd2_pubkey" {
  metadata {
    name      = "lnd2-pubkey"
    namespace = local.bitcoin_namespace
  }
}

resource "kubernetes_secret" "lnd2_pubkey" {
  metadata {
    name      = "lnd2-pubkey"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = data.kubernetes_secret.lnd2_pubkey.data
}

data "kubernetes_secret" "lnd1_pubkey" {
  metadata {
    name      = "lnd1-pubkey"
    namespace = local.bitcoin_namespace
  }
}

resource "kubernetes_secret" "lnd1_pubkey" {
  metadata {
    name      = "lnd1-pubkey"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = data.kubernetes_secret.lnd1_pubkey.data
}

data "kubernetes_secret" "lnd2_credentials" {
  metadata {
    name      = "lnd2-credentials"
    namespace = local.bitcoin_namespace
  }
}

resource "kubernetes_secret" "lnd2_credentials" {
  metadata {
    name      = "lnd2-credentials"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = data.kubernetes_secret.lnd2_credentials.data
}

data "kubernetes_secret" "lnd1_credentials" {
  metadata {
    name      = "lnd1-credentials"
    namespace = local.bitcoin_namespace
  }
}

resource "kubernetes_secret" "lnd1_credentials" {
  metadata {
    name      = "lnd1-credentials"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = data.kubernetes_secret.lnd1_credentials.data
}

data "kubernetes_secret" "bria_credentials" {
  metadata {
    name      = "galoy-bria-creds"
    namespace = "${local.name_prefix}-bitcoin"
  }
}

resource "kubernetes_secret" "bria_credentials" {
  metadata {
    name      = "bria-api-key"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = data.kubernetes_secret.bria_credentials.data
}

resource "random_password" "mongo_password" {
  length  = 20
  special = false
}

resource "random_password" "mongo_root_password" {
  length  = 20
  special = false
}

resource "random_password" "mongo_replicaset_key" {
  length  = 20
  special = false
}

resource "kubernetes_secret" "mongodb_creds" {
  metadata {
    name      = "galoy-mongodb"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = {
    "mongodb-password" : random_password.mongo_password.result
    "mongodb-passwords" : random_password.mongo_password.result
    "mongodb-root-password" : random_password.mongo_root_password.result
    "mongodb-replica-set-key" : random_password.mongo_replicaset_key.result
  }
}

resource "kubernetes_secret" "mongodb_connection_string" {
  metadata {
    name      = "galoy-mongodb-connection-string"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = {
    "mongodb-con" : "mongodb://${local.mongo_user}:${random_password.mongo_password.result}@galoy-mongodb-0.galoy-mongodb-headless,galoy-mongodb-1.galoy-mongodb-headless,galoy-mongodb-2.galoy-mongodb-headless/${local.mongo_database}"
  }
}

resource "kubernetes_secret" "redis_password" {
  metadata {
    name      = "galoy-redis-pw"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = {
    "redis-password" : random_password.redis_password.result
  }
}

resource "random_password" "redis_password" {
  length  = 20
  special = false
}

resource "kubernetes_secret" "geetest_key" {
  metadata {
    name      = "geetest-key"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = {
    key = local.geetest_key
    id  = local.geetest_id
  }
}

resource "kubernetes_secret" "proxy_check_api_key" {
  metadata {
    name      = "proxy-check-api-key"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }
  data = {
    "api-key" = local.proxy_check_api_key
  }
}

#openai api key
resource "kubernetes_secret" "openai_api_key" {
  metadata {
    name      = "openai-secret"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }
  data = {
    "api-key" = local.openai_api_key
  }
}

# pinecone api key
resource "kubernetes_secret" "pinecone_api_key" {
  metadata {
    name      = "pinecone-secret"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }
  data = {
    "api-key" = local.pinecone_api_key
  }
}

resource "kubernetes_secret" "smoketest" {
  count = local.smoketest_kubeconfig == "" ? 1 : 0
  metadata {
    name      = "galoy-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    galoy_endpoint         = local.galoy_endpoint
    galoy_port             = 80
    price_history_endpoint = local.price_history_endpoint
    price_history_port     = 50052
    kratos_admin_endpoint  = local.kratos_admin_host
    kratos_admin_port      = 80
  }
}

resource "kubernetes_secret" "smoketest_with_cronjob" {
  count = local.smoketest_kubeconfig == "" ? 0 : 1
  metadata {
    name      = "galoy-smoketest"
    namespace = local.smoketest_namespace
  }
  data = {
    galoy_endpoint         = local.galoy_endpoint
    galoy_port             = 80
    price_history_endpoint = local.price_history_endpoint
    price_history_port     = 50052
    kratos_admin_endpoint  = local.kratos_admin_host
    kratos_admin_port      = 80
    galoy_namespace        = local.galoy_namespace
    smoketest_kubeconfig   = local.smoketest_kubeconfig
  }
}

resource "random_password" "bank_owner_code" {
  length      = 5
  min_numeric = 5
  numeric     = true
  lower       = false
  upper       = false
  special     = false
}

resource "random_password" "dealer_code" {
  length      = 5
  min_numeric = 5
  numeric     = true
  lower       = false
  upper       = false
  special     = false
}

resource "random_password" "test_account" {
  for_each    = toset([for k, v in local.test_account_numbers : k])
  length      = 5
  min_numeric = 5
  numeric     = true
  lower       = false
  upper       = false
  special     = false
}

resource "kubernetes_secret" "bankowner" {
  metadata {
    name      = "bankowner-code"
    namespace = local.galoy_namespace
  }
  data = {
    code = "1${random_password.bank_owner_code.result}"
  }
}

resource "kubernetes_secret" "dealer_creds" {
  metadata {
    name      = "dealer-creds"
    namespace = local.galoy_namespace
  }
  data = {
    code  = "1${random_password.dealer_code.result}"
    phone = local.dealer_phone
  }
}

resource "kubernetes_secret" "test_accounts" {
  metadata {
    name      = "test-accounts"
    namespace = local.galoy_namespace
  }
  data = {
    json = jsonencode([
      for k, v in local.test_account_numbers :
      {
        phone = k,
        code  = "1${random_password.test_account[k].result}"
        tag   = v
      }
    ])
  }
}

module "price_history_pg" {
  source = "../infra/vendor/tf/postgresql/gcp"

  gcp_project       = local.gcp_project
  region            = local.gcp_region
  vpc_name          = local.vpc_name
  instance_name     = local.price_history_pg_instance_name
  highly_available  = local.ha_pg
  databases         = [local.price_history_pg_dbname]
  tier              = local.price_history_pg_tier
  big_query_viewers = local.big_query_viewers
  database_version  = "POSTGRES_15"
}

resource "kubernetes_secret" "price_history_postgres_creds" {
  metadata {
    name      = "galoy-price-history-postgres-creds"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = {
    username = module.price_history_pg.creds[local.price_history_pg_dbname].user
    password = module.price_history_pg.creds[local.price_history_pg_dbname].password
    database = local.price_history_pg_dbname
  }
}

resource "random_string" "unsecure_default_login_code" {
  count = local.unsecure_default_login_code_enabled ? 1 : 0

  length  = 6
  upper   = false
  lower   = false
  special = false
  numeric = true
}

resource "kubernetes_secret" "unsecure_default_login_code" {
  count = local.unsecure_default_login_code_enabled ? 1 : 0

  metadata {
    name      = "galoyapp-unsecure-login-code"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = {
    code = random_string.unsecure_default_login_code[count.index].result
  }
}


resource "random_password" "kratos_callback_api_key" {
  length = 32
}

resource "kubernetes_secret" "api_keys" {
  metadata {
    name      = "api-keys"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = {
    pg-con : module.api_keys_postgresql.creds["api-keys"].conn
  }
}

resource "kubernetes_secret" "notifications" {
  metadata {
    name      = "notifications"
    namespace = kubernetes_namespace.galoy.metadata[0].name
  }

  data = {
    pg-con : module.notifications_postgresql.creds["notifications"].conn
    pg-read-con : module.notifications_postgresql.creds["notifications"].read_conn
    smtp-password : local.smtp_password
    firebase-service-account : local.firebase_service_account
  }
}

resource "helm_release" "galoy" {
  name       = "galoy"
  chart      = "${path.module}/vendor/galoy/chart"
  repository = "https://galoymoney.github.io/charts/"
  namespace  = kubernetes_namespace.galoy.metadata[0].name

  values = [
    templatefile("${path.module}/galoy-sensitive-values.yml.tmpl", {
      bank_owner_code : "1${random_password.bank_owner_code.result}"
      dealer_phone : local.dealer_phone
      dealer_code : "1${random_password.dealer_code.result}"
      test_accounts : [for k, v in local.test_account_numbers :
        {
          phone = k
          code  = "1${random_password.test_account[k].result}"
        }
      ]
      kratos_callback_api_key : random_password.kratos_callback_api_key.result
      price_currency_beacon_api_key : local.price_currency_beacon_api_key
      price_currency_beacon_enabled : local.price_currency_beacon_enabled
      price_exchangeratehost_api_key : local.price_exchangeratehost_api_key
      price_exchangeratehost_enabled : local.price_exchangeratehost_enabled
      openai_assistant_id : local.openai_assistant_id
      mattermost_webhook_url : local.mattermost_webhook_url
      hydra_pg_user : module.hydra_postgresql.creds["hydra"].user
      hydra_pg_password : module.hydra_postgresql.creds["hydra"].password
      hydra_pg_db : module.hydra_postgresql.creds["hydra"].db_name
      hydra_pg_host : module.hydra_postgresql.creds["hydra"].host
      supergraph : file("${path.module}/supergraph/vendor/supergraph.graphql")
    }),
    templatefile("${path.module}/galoy-values.yml.tmpl", {
      trigger_ref : file("${path.module}/vendor/galoy/git-ref/ref")

      api_limit_rpm : local.api_limit_rpm
      api_limit_burst_multiplier : local.api_limit_burst_multiplier
      api_limit_connections : local.api_limit_connections

      lnd1_credentials_checksum : sha256(jsonencode(data.kubernetes_secret.lnd1_credentials.data))
      lnd2_credentials_checksum : sha256(jsonencode(data.kubernetes_secret.lnd2_credentials.data))

      stablesats_namespace : local.stablesats_namespace
      galoy_namespace : local.galoy_namespace

      hydra_public_host : local.hydra_public_host

      lightning_address_domain : local.lightning_address_domain
      lightning_address_domain_aliases : "${jsonencode(local.lightning_address_domain_aliases)}"

      btc_network : local.bitcoin_network
      funder_user_name : local.funder_user_name
      withdraw_method : local.withdraw_method
      withdraw_ratio_basis_points_on_imbalance : local.withdraw_ratio_basis_points_on_imbalance
      withdraw_min_fee : local.withdraw_min_fee
      withdraw_fee_threshold : local.withdraw_fee_threshold
      max_hot_wallet : local.max_hot_wallet
      request_code_per_ip_points : local.request_code_per_ip_points
      withdrawal_level_zero : local.withdrawal_level_zero
      withdrawal_level_one : local.withdrawal_level_one
      withdrawal_level_two : local.withdrawal_level_two
      withdrawal_level_three : local.withdrawal_level_three
      intraledger_level_zero : local.intraledger_level_zero
      intraledger_level_one : local.intraledger_level_one
      intraledger_level_two : local.intraledger_level_two
      intraledger_level_three : local.intraledger_level_three
      trade_intra_account_level_zero : local.trade_intra_account_level_zero
      trade_intra_account_level_one : local.trade_intra_account_level_one
      trade_intra_account_level_two : local.trade_intra_account_level_two
      trade_intra_account_level_three : local.trade_intra_account_level_three
      quizzes_allow_phone_countries : jsonencode(local.quizzes_allow_phone_countries)
      quizzes_deny_phone_countries : jsonencode(local.quizzes_deny_phone_countries)
      quizzes_deny_asns : jsonencode(local.quizzes_deny_asns)
      accounts_deny_ip_countries : jsonencode(local.accounts_deny_ip_countries)
      accounts_deny_phone_countries : jsonencode(local.accounts_deny_phone_countries)
      accounts_enable_phone_check : local.accounts_enable_phone_check
      unsupported_countries : jsonencode(local.unsupported_countries)
      unsupported_sms_countries : jsonencode(distinct(concat(local.unsupported_countries, local.unsupported_sms_countries)))
      unsupported_whatsapp_countries : jsonencode(distinct(concat(local.unsupported_countries, local.unsupported_whatsapp_countries)))
      unsupported_telegram_countries : jsonencode(distinct(concat(local.unsupported_countries, local.unsupported_telegram_countries)))
      ip_recording_enabled : local.ip_recording_enabled
      proxy_checking_enabled : local.proxy_checking_enabled
      extra_cors_allowed_origins : local.extra_cors_allowed_origins
      unsecure_default_login_code_enabled : local.unsecure_default_login_code_enabled

      lnd1_host : local.lnd1_host
      lnd2_host : local.lnd2_host
      lnd_priority : local.lnd_priority

      bria_host : local.bria_host
      bria_hot_wallet_name : data.kubernetes_secret.bria_credentials.data["hot-wallet-name"]
      bria_cold_wallet_name : data.kubernetes_secret.bria_credentials.data["cold-wallet-name"]
      bria_fast_queue_name : data.kubernetes_secret.bria_credentials.data["fast-queue-name"]

      mongo_username : local.mongo_user
      mongo_database : local.mongo_database
      backups_bucket_name : local.backups_bucket_name
      mongodb_ssd_size : local.mongodb_ssd_size

      price_history_pg_host : module.price_history_pg.creds[local.price_history_pg_dbname].host
      price_history_tracing_service_name : local.price_history_tracing_service_name
      price_realtime_tracing_service_name : local.price_realtime_tracing_service_name

      otel_exporter_otlp_endpoint : local.otel_exporter_otlp_endpoint
      otel_exporter_grpc_endpoint : local.otel_exporter_grpc_endpoint
      tracing_prefix : local.name_prefix
      api_hosts : jsonencode(local.api_hosts)
      admin_api_hosts : jsonencode(local.admin_api_hosts)
      websocket_hosts : jsonencode(local.websocket_hosts)

      accounts_initial_status : local.accounts_initial_status
      kratos_public_api_url : local.kratos_public_api_url
      kratos_admin_api_url : local.kratos_admin_api_url

      android_min_build_number : local.android_min_build_number
      android_last_build_number : local.android_last_build_number
      ios_min_build_number : local.ios_min_build_number
      ios_last_build_number : local.ios_last_build_number
      rebalance_enabled : local.rebalance_enabled

      circles_subgraph_url : local.circles_subgraph_url
      blink_kyc_subgraph_url : local.blink_kyc_subgraph_url

      hydra_tracing_service_name : local.hydra_tracing_service_name
      hydra_public_host : local.hydra_public_host
      hydra_cookie_domain : local.hydra_cookie_domain

      kratos_public_host : local.galoy_auth_host
      kratos_cookie_domain : local.kratos_cookie_domain
      kratos_tracing_service_name : local.kratos_tracing_service_name
      kratos_secret_name : local.kratos_secret_name
      kratos_email_domain : local.kratos_email_domain
      kratos_email_sender_name : local.kratos_email_sender_name
      oathkeeper_tracing_service_name : local.oathkeeper_tracing_service_name
      oathkeeper_admin_cookie_session_url : local.admin_panel_session_verification_url
      verification_email_html : templatefile("${path.module}/emails/verification_html.tmpl", {
        galoy_instance_name : local.galoy_instance_name
      })
      verification_email_plaintext : templatefile("${path.module}/emails/verification.tmpl", {
        galoy_instance_name : local.galoy_instance_name
      })
      recovery_email_html : base64encode(file("${path.module}/emails/recovery_html.tmpl"))
      recovery_email_plaintext : base64encode(file("${path.module}/emails/recovery.tmpl"))
      kratos_identity_schemas : [
        for id in local.kratos_identity_schemas : {
          name : id
          schema : file("${path.module}/identity-schemas/${id}")
        }
      ]

      consent_hosts : jsonencode(local.consent_hosts)
      consent_host : local.consent_hosts[0]
      public_galoy_endpoint : local.galoy_endpoint
      core_via_oathkeeper_proxy : local.core_via_oathkeeper_proxy

      api_keys_prefix : local.api_keys_prefix
      notifications_smtp_username : local.smtp_username
      notifications_from_email : local.notifications_from_email
      notifications_from_name : local.notifications_from_name
    }),
    file("${path.module}/../../gcp/${local.name_prefix}/galoy/galoy-scaling.yml")
  ]

  dependency_update = true

  depends_on = [
    kubernetes_secret.redis_password,
    kubernetes_secret.lnd1_credentials,
    kubernetes_secret.lnd1_pubkey,
    kubernetes_secret.lnd2_credentials,
    kubernetes_secret.lnd2_pubkey,
    kubernetes_secret.api_keys,
    kubernetes_secret.notifications
  ]

  timeout = 600
}

module "postgresql" {
  source = "../infra/vendor/tf/postgresql/gcp"

  gcp_project       = local.gcp_project
  region            = local.gcp_region
  vpc_name          = local.vpc_name
  instance_name     = local.pg_instance_name
  highly_available  = local.ha_pg
  databases         = local.databases
  max_connections   = local.max_connections
  big_query_viewers = local.big_query_viewers
  database_version  = "POSTGRES_15"
}

module "hydra_postgresql" {
  source = "../infra/vendor/tf/postgresql/gcp"

  gcp_project      = local.gcp_project
  region           = local.gcp_region
  vpc_name         = local.vpc_name
  instance_name    = local.hydra_pg_instance_name
  highly_available = local.ha_pg
  databases        = local.hydra_databases
  max_connections  = local.max_connections
  database_version = "POSTGRES_15"
}

module "api_keys_postgresql" {
  source = "../infra/vendor/tf/postgresql/gcp"

  gcp_project       = local.gcp_project
  region            = local.gcp_region
  vpc_name          = local.vpc_name
  instance_name     = local.api_keys_pg_instance_name
  highly_available  = local.ha_pg
  databases         = local.api_keys_databases
  max_connections   = local.max_connections
  big_query_viewers = local.big_query_viewers
  tier              = local.api_keys_pg_tier
  database_version  = "POSTGRES_15"
}

module "notifications_postgresql" {
  source = "../infra/vendor/tf/postgresql/gcp"

  gcp_project            = local.gcp_project
  region                 = local.gcp_region
  vpc_name               = local.vpc_name
  instance_name          = local.notifications_pg_instance_name
  highly_available       = local.ha_pg
  databases              = local.notifications_databases
  max_connections        = 200
  big_query_viewers      = local.big_query_viewers
  tier                   = local.notifications_pg_tier
  provision_read_replica = true
}

output "postgresql_creds" {
  value     = module.postgresql.creds
  sensitive = true
}

output "hydra_postgresql_creds" {
  value     = module.hydra_postgresql.creds
  sensitive = true
}

output "api_keys_postgresql_creds" {
  value     = module.api_keys_postgresql.creds
  sensitive = true
}
