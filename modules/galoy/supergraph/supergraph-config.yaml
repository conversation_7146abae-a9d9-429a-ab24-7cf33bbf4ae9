federation_version: =2.3.2
subgraphs:
  galoy:
    routing_url: url
    schema:
      file: ../vendor/galoy/chart/apollo-router/public-schema.graphql
  api-keys:
    routing_url: url
    schema:
      file: ../vendor/galoy/chart/apollo-router/api-keys-schema.graphql
  notifications:
    routing_url: url
    schema:
      file: ../vendor/galoy/chart/apollo-router/notifications-schema.graphql
  circles:
    routing_url: url
    schema:
      file: ../../services/blink-addons/vendor/circles/chart/subgraph/schema.graphql
  blink-kyc:
    routing_url: url
    schema:
      file: ../../services/blink-addons/vendor/blink-kyc/chart/subgraph/schema.graphql
