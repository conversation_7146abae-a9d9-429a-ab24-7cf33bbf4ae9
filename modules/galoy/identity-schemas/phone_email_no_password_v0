{"$id": "http://mydomain.com/schemas/v2/customer.schema.json", "$schema": "http://json-schema.org/draft-07/schema#", "title": "A phone+email user", "type": "object", "properties": {"traits": {"type": "object", "properties": {"email": {"title": "E-Mail", "type": "string", "format": "email", "ory.sh/kratos": {"credentials": {"password": {"identifier": true}}, "verification": {"via": "email"}, "recovery": {"via": "email"}}}, "phone": {"title": "Phone", "type": "string", "format": "string", "ory.sh/kratos": {"credentials": {"password": {"identifier": true}}}}}, "required": ["email", "phone"], "additionalProperties": false}}}