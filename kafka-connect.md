<!-- omit in toc -->
# Deploy and debug Kafka connectors
- [Assess the health of the deployment](#assess-the-health-of-the-deployment)
  - [On the Google console](#on-the-google-console)
  - [On the bastion](#on-the-bastion)
- [Topics](#topics)
- [Mongodb streaming](#mongodb-streaming)
- [Postgres streaming](#postgres-streaming)
- [BigQuery sink](#bigquery-sink)
- [Find missing fields from the BigQuery schemas](#find-missing-fields-from-the-bigquery-schemas)
  - [Filter the kafka-connect logs for errors](#filter-the-kafka-connect-logs-for-errors)
  - [Find which topic has the missing field](#find-which-topic-has-the-missing-field)
- [Add or update connectors](#add-or-update-connectors)


## Assess the health of the deployment
### On the Google console
The kafka-connect logs are exposed on the google console. Following the links click on `Stream logs` and search for keywords as needed eg.: `invalid` (for schema errors).

* logs on staging: <https://cloudlogging.app.goo.gl/oFdn95vq2YuPk8ND7>

* logs on bbw:
<https://cloudlogging.app.goo.gl/2fjeAXXLEeftJ42P9>


### On the bastion
* watch the connectors for readiness and breaking errors
  ```
  k -n galoy-staging-kafka get kafkaconnector -w
  ```
* kafka-connect logs
  ```
  env="galoy-staging"
  kafkaconnect=$(kubectl get pods -n ${env}-kafka -l strimzi.io/kind=KafkaConnect -o jsonpath="{.items[0].metadata.name}")

  kubectl -n ${env}-kafka logs ${kafkaconnect} -f
  ```

## Topics
* list topics
  ```
  kubectl -n galoy-staging-kafka exec -it kafka-kafka-0 -- bin/kafka-topics.sh --bootstrap-server kafka-kafka-plain-bootstrap:9092 --list
  ```

* inspect the contents of a topic (contains the schema for every message in json)
  ```
  topic_to_read="mongodb_galoy_medici_balances"
  k -n galoy-staging-kafka exec -it kafka-kafka-0 -- bin/kafka-console-consumer.sh --bootstrap-server kafka-kafka-plain-bootstrap:9092 --topic ${topic_to_read} --from-beginning
  ```

* create topics
  ```
  env="galoy-staging"
  topics_to_create="mongodb_galoy_medici_balances
  mongodb_galoy_medici_journals
  mongodb_galoy_medici_transaction_metadatas
  mongodb_galoy_medici_transactions"

  for i in ${topics_to_create}; do
    kubectl -n galoy-staging-kafka exec -it kafka-kafka-0 -- \
    bin/kafka-topics.sh --bootstrap-server kafka-kafka-plain-bootstrap:9092 \
    --create --topic ${i} --partitions 1 --replication-factor 3 && \
    echo "# ${i} created"
  done
  ```

* delete topics (WARNING - DATA LOSS) - can be used to reset the offset to restart streaming
  ```
  env="galoy-staging"
  topics_to_delete="mongodb_galoy_medici_balances
  mongodb_galoy_medici_journals
  mongodb_galoy_medici_transaction_metadatas
  mongodb_galoy_medici_transactions"

  for i in ${topics_to_delete}; do
    kubectl -n ${env}-kafka exec -it kafka-kafka-0 -- \
  bin/kafka-topics.sh --bootstrap-server kafka-kafka-plain-bootstrap:9092 --delete --topic ${i} && \
    echo "# ${i} deleted"
  done
  ```

## Mongodb streaming
* plugin settings reference:
<https://www.mongodb.com/docs/kafka-connector/current/source-connector/configuration-properties/>
* there is a kafka-source-mongo connector for each streamed collection:
  ```
  kafka-source-mongo-medici-balances
  kafka-source-mongo-medici-journals
  kafka-source-mongo-medici-transaction-metadatas
  kafka-source-mongo-medici-transactions
  ```
* describe each individually eg.:
  ```
  k -n galoy-staging-kafka describe kafkaconnector kafka-source-mongo-medici-balances
  ```

## Postgres streaming
* plugin settings reference:
<https://debezium.io/documentation/reference/stable/connectors/postgresql.html#postgresql-connector-properties>
* describe the connector
  ```
  k -n galoy-staging-kafka describe kafkaconnector kafka-source-postgres
  ```

  * check if:
    ```
        table.include.list:                      public.galoy_transactions,public.okex_orders,public.okex_transfers,public.sqlx_ledger_balances,public.user_trades
    ```
  * matches (the missing tables might not have had changes since the restart, in that case missing is ok):
    ```
      Topics:
        pg_stablesats_public_galoy_transactions
        pg_stablesats_public_okex_orders
        pg_stablesats_public_okex_transfers
        pg_stablesats_public_sqlx_ledger_balances
        pg_stablesats_public_user_trades
    Events:  <none>
    ```

## BigQuery sink
* plugin settings reference:
<https://docs.confluent.io/kafka-connectors/bigquery/current/kafka_connect_bigquery_config.html>
* describe the connector
  ```
  k -n galoy-staging-kafka describe kafkaconnector kafka-sink-bigquery
  ```

* if there are fields missing from the schema they can be added to the json files in: <https://github.com/GaloyMoney/galoy-reporting/tree/main/kafka/bigquery-schemas>

## Find missing fields from the BigQuery schemas
### Filter the kafka-connect logs for errors
* look for the lines: `invalid: no such field:`
  ```
  env="galoy-staging"
  kafkaconnect=$(kubectl get pods -n ${env}-kafka -l strimzi.io/kind=KafkaConnect -o jsonpath="{.items[0].metadata.name}")

  kubectl -n ${env}-kafka logs ${kafkaconnect} -f | grep "invalid: no such field:"
  ```

### Find which topic has the missing field
* Compare with the schemas in: <https://github.com/GaloyMoney/galoy-reporting/tree/main/kafka/bigquery-schemas>
  * list topics:
    ```
    env="galoy-staging"

    kubectl -n ${env}-kafka exec -it kafka-kafka-0 -- bin/kafka-topics.sh --bootstrap-server kafka-kafka-plain-bootstrap:9092 --list
    ```
    ```
    __consumer_offsets
    connect-cluster-configs
    connect-cluster-offsets
    connect-cluster-status
    dlq_bigquery_sink
    mongodb_galoy_medici_balances
    mongodb_galoy_medici_journals
    mongodb_galoy_medici_transaction_metadatas
    mongodb_galoy_medici_transactions
    pg_stablesats_public_galoy_transactions
    pg_stablesats_public_okex_orders
    pg_stablesats_public_okex_transfers
    pg_stablesats_public_sqlx_ledger_balances
    pg_stablesats_public_user_trades
    ```
  * check if there is a schema field in the topic which is in the errors, but not in the it's BigQuery schema:
    ```
    # choose which topic to investigate
    topic="mongodb_galoy_medici_transactions"
    # the name of the missing field
    to_grep="payout_id"
    env="galoy-staging"

    k -n ${env}-kafka exec -it kafka-kafka-0 -- bin/kafka-console-consumer.sh --bootstrap-server kafka-kafka-plain-bootstrap:9092 --topic ${topic} --from-beginning | grep ${to_grep}
    ```
  * note that changing the schema of a BigQuery table will result in that table being deleted and recreated so will take time to be repopulated

## Add or update connectors
* edit the kafka-connect Dockerfile: <https://github.com/blinkbitcoin/charts/blob/main/images/kafka-connect/Dockerfile>. This will make the new or updated connector available in the kafka-connect pod.
* see the configuration of the existing connectors: <https://github.com/GaloyMoney/blink-deployments/tree/main/modules/services/monitoring/kafka-connect>
* add the credentials to connect to the database to be read
* create the Kafka topics to write to
* read the schema from Kafka. The json format does contain the schema for every message. Be aware that the schema can be different between messages and can also change when fields are added or modified.
  ```
  env="galoy-staging"
  topic="<topic_name>"

  k -n ${env}-kafka exec -it kafka-kafka-0 -- bin/kafka-console-consumer.sh --bootstrap-server kafka-kafka-plain-bootstrap:9092 --topic ${topic} --from-beginning
  ```
* define the schemas: <https://github.com/GaloyMoney/galoy-reporting/tree/main/kafka/bigquery-schemas>
  * for the conversion from the Kafka schemas an LLM can come useful (use only test or dummy data). The task includes parsing the json and change the fields to be compatible with the BigQuery format eg:
    * "type": "string" is mapped to "type": "STRING"
    * "type": "boolean" is mapped to "type": "BOOLEAN"
    * "type": "double" is mapped to "type": "FLOAT64"
    * "type": "int32" is mapped to "type": "INTEGER"
    * "optional": false is mapped to "mode": "REQUIRED"
    * "optional": true is mapped to "mode": "NULLABLE"
    * "mode": "NULLABLE" is the default so can be skipped
    * "mode": "REPEATED" is used for fields that can have multiple string values (array of strings) in a single Kafka message. Fields with this mode will become repeated fields in BigQuery. The repeated mode means that this field can contain multiple values of the specified type.
    * "name": "io.debezium.time.ZonedTimestamp" is interpreted as "type": "TIMESTAMP" in BigQuery schema

* create the BigQuery tables: <https://github.com/GaloyMoney/galoy-reporting/blob/main/kafka/main.tf#L6>
* add the topics to the BigQuery sink: <https://github.com/GaloyMoney/blink-deployments/blob/main/modules/services/monitoring/kafka-connect/kafka-sink-bigquery.tf#L5>

* automatic updates of the schemas requires a Schema Registry which is not part of the Strimi Kafka deployment, only offered as part of the Confluent Platform: https://docs.confluent.io/platform/current/schema-registry/installation/index.html#installing-and-configuring-sr
