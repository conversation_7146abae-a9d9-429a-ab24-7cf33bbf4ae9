output "docker_host_private_ip" {
  description = "The private IP address of the docker host."
  value       = google_compute_instance.docker_host.network_interface[0].network_ip
}

output "docker_host_zone" {
  value = google_compute_instance.docker_host.zone
}

output "docker_host_project" {
  value = google_compute_instance.docker_host.project
}

output "docker_host_name" {
  description = "The public IP of the docker host."
  value       = google_compute_instance.docker_host.name
}
