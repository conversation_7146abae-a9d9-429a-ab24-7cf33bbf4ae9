#@ load("@ytt:data", "data")

#@ def pipeline_image():
#@   return data.values.docker_registry + "/galoy-infra-pipeline"
#@ end

#@ def task_image_config():
type: registry-image
source:
  username: #@ data.values.docker_registry_user
  password: #@ data.values.docker_registry_password
  repository: #@ pipeline_image()
#@ end

jobs:
#@ for i in range(data.values.n_docker_hosts):
#@ env_name = "docker-host-" + str(i)
- name: #@ "deploy-docker-host-" + str(i)
  plan:
  - put: pool
    params:
      claim: #@ env_name
  - in_parallel:
    - put: pool
      params:
        remove: pool/
    - { get: docker-host, trigger: true }
  - put: tf-docker-host
    params:
      terraform_source: docker-host/gcp/galoy-staging/docker-host
      env_name: #@ env_name
      vars:
        host_index: #@ i
  - put: tf-docker-host
    params:
      terraform_source: docker-host/gcp/galoy-staging/docker-host
      env_name: #@ env_name
      vars:
        host_index: #@ i
  - task: add-docker-user
    config:
      platform: linux
      image_resource: #@ task_image_config()
      inputs:
      - name: docker-host
        path: repo
      - name: tf-docker-host
        path: terraform
      params:
        GOOGLE_CREDENTIALS: #@ data.values.staging_inception_creds
        SSH_PRIVATE_KEY: #@ data.values.staging_ssh_private_key
        SSH_PUB_KEY: #@ data.values.staging_ssh_pub_key
      run:
        path: repo/gcp/galoy-staging/docker-host/ci/tasks/add-docker-user.sh
  - put: pool
    params:
      add: tf-docker-host/

- name: #@ "redeploy-docker-host-" + str(i)
  plan:
  - { get: sunday-trigger, trigger: true }
  - put: pool
    params:
      claim: #@ env_name
  - in_parallel:
    - put: pool
      params:
        remove: pool/
    - { get: docker-host }
  - put: tf-docker-host
    params:
      action: destroy
      env_name: #@ env_name
      terraform_source: docker-host/gcp/galoy-staging/docker-host
      vars:
        host_index: #@ i
    get_params: { action: destroy }
  - put: tf-docker-host
    params:
      terraform_source: docker-host/gcp/galoy-staging/docker-host
      env_name: #@ env_name
      vars:
        host_index: #@ i
  - put: tf-docker-host
    params:
      terraform_source: docker-host/gcp/galoy-staging/docker-host
      env_name: #@ env_name
      vars:
        host_index: #@ i
  - task: add-docker-user
    config:
      platform: linux
      image_resource: #@ task_image_config()
      inputs:
      - name: docker-host
        path: repo
      - name: tf-docker-host
        path: terraform
      params:
        GOOGLE_CREDENTIALS: #@ data.values.staging_inception_creds
        SSH_PRIVATE_KEY: #@ data.values.staging_ssh_private_key
        SSH_PUB_KEY: #@ data.values.staging_ssh_pub_key
      run:
        path: repo/gcp/galoy-staging/docker-host/ci/tasks/add-docker-user.sh
  - put: pool
    params:
      add: tf-docker-host/
#@ end

resources:
- name: docker-host
  type: git
  source:
    paths: ["gcp/galoy-staging/docker-host"]
    uri: #@ data.values.git_uri
    branch: #@ data.values.git_branch
    private_key: #@ data.values.github_private_key

- name: tf-docker-host
  type: terraform
  source:
    env_name: default
    backend_type: gcs
    backend_config:
      bucket: #@ data.values.staging_state_bucket
      prefix: galoy-staging/docker-host
      credentials: #@ data.values.staging_inception_creds
    env:
      GOOGLE_CREDENTIALS: #@ data.values.staging_inception_creds

- name: pool
  type: pool
  source:
    uri: #@ data.values.concourse_locks_git_uri
    branch: main
    pool: docker-hosts
    private_key: #@ data.values.github_private_key

- name: sunday-trigger
  type: time
  source:
    start: 7:00 AM
    stop: 8:00 AM
    days: [Sunday]
    initial_version: true

resource_types:
- name: terraform
  type: docker-image
  source:
    repository: ljfranklin/terraform-resource
    tag: latest
