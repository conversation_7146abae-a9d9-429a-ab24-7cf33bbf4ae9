#@data/values
---
n_docker_hosts: 3
concourse_locks_git_uri: **************:blinkbitcoin/concourse-locks.git
git_uri: **************:blinkbitcoin/blink-deployments.git
git_branch: main
github_private_key: ((github-blinkbitcoin.private_key))
docker_registry: us.gcr.io/galoy-org
docker_registry_user: ((docker-creds.username))
docker_registry_password: ((docker-creds.password))
staging_inception_creds: ((staging-gcp-creds.creds_json))
staging_ssh_private_key: ((staging-ssh.ssh_private_key))
staging_ssh_pub_key: ((staging-ssh.ssh_public_key))
staging_state_bucket: ((staging-gcp-creds.bucket_name))
