#!/bin/bash

set -eu

CI_ROOT=$(pwd)

host_name=$(cat terraform/metadata | jq -r '.docker_host_name')
echo "Running on host: ${host_name}"
host_zone=$(cat terraform/metadata | jq -r '.docker_host_zone')
gcp_project=$(cat terraform/metadata | jq -r '.docker_host_project')

gcloud_ssh() {
  gcloud compute ssh ${host_name} \
    --zone=${host_zone} \
    --project=${gcp_project} \
    --ssh-key-file=${CI_ROOT}/login.ssh \
    --tunnel-through-iap \
    --command "$@" 2> /dev/null
}

cat <<EOF > ${CI_ROOT}/gcloud-creds.json
${GOOGLE_CREDENTIALS}
EOF
cat <<EOF > ${CI_ROOT}/login.ssh
${SSH_PRIVATE_KEY}
EOF
chmod 600 ${CI_ROOT}/login.ssh
cat <<EOF > ${CI_ROOT}/login.ssh.pub
${SSH_PUB_KEY}
EOF
gcloud auth activate-service-account --key-file ${CI_ROOT}/gcloud-creds.json
gcloud compute os-login ssh-keys add --key-file=${CI_ROOT}/login.ssh.pub > /dev/null

export DOCKER_HOST_USER="sa_$(cat ${CI_ROOT}/gcloud-creds.json  | jq -r '.client_id')"

set +e
for i in {1..120}; do
  echo "Attempt ${i} to find docker on docker-host"
  gcloud_ssh "getent group docker" && break
  sleep 4
done
set -e

gcloud_ssh "sudo usermod -aG docker ${DOCKER_HOST_USER}"
gcloud_ssh "docker ps"
gcloud_ssh "echo 'y' | gcloud auth configure-docker"
