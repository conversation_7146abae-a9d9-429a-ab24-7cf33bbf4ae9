module "shared" {
  source = "../shared"
}

module "platform" {
  source = "../../../modules/infra/vendor/tf/platform/gcp"

  node_default_machine_type = "n2-standard-4"
  node_service_account      = "<EMAIL>"
  max_default_node_count    = 3
  name_prefix               = module.shared.name_prefix
  gcp_project               = module.shared.gcp_project
  region                    = module.shared.gcp_region
  deploy_lnd_ips            = true

  pg_ha = true
}

output "lnd1_public_ip" {
  value = module.platform.lnd1_ip
}

output "lnd2_public_ip" {
  value = module.platform.lnd2_ip
}

data "google_compute_network" "vpc" {
  name    = "${module.shared.name_prefix}-vpc"
  project = module.shared.gcp_project
}

resource "google_compute_subnetwork" "docker_host" {

  name = "${module.shared.name_prefix}-docker-host"

  project = module.shared.gcp_project
  region  = module.shared.gcp_region

  network = data.google_compute_network.vpc.self_link

  ip_cidr_range = "********/24"
}

terraform {
  backend "gcs" {
    bucket = "galoy-staging-tf-state"
    prefix = "galoy-staging/platform"
  }
}
