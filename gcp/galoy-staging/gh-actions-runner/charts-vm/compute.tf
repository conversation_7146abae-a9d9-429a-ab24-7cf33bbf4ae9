resource "google_compute_instance" "gha_runner_charts_vm" {
  name         = "gha-runner-charts"
  project      = local.project
  zone         = local.zone
  machine_type = local.default_machine_type

  allow_stopping_for_update = true
  metadata_startup_script   = file("${path.module}/startup.sh")

  boot_disk {
    initialize_params {
      image = local.image
      size  = 1024
    }
  }

  network_interface {
    network = "default"
    access_config {}
  }

  metadata = {
    enable-oslogin     = "TRUE"
    enable-oslogin-2fa = "TRUE"
  }
}


data "google_iam_policy" "gha_runner_charts_vm" {
  binding {
    role    = "roles/compute.osLogin"
    members = local.users
  }
  binding {
    role    = "roles/compute.viewer"
    members = local.users
  }
  binding {
    role    = "roles/compute.admin"
    members = local.users
  }
}

resource "google_compute_instance_iam_policy" "gha_runner_charts_vm" {
  project       = local.project
  zone          = google_compute_instance.gha_runner_charts_vm.zone
  instance_name = google_compute_instance.gha_runner_charts_vm.name
  policy_data   = data.google_iam_policy.gha_runner_charts_vm.policy_data
}
