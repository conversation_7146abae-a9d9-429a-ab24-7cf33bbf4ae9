resource "tls_private_key" "scw_ssh_key" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

resource "scaleway_account_ssh_key" "gha_runner_mobile_mac_mini_ssh_key" {
  name       = "mac-mini-m1"
  public_key = tls_private_key.scw_ssh_key.public_key_pem
}

resource "scaleway_apple_silicon_server" "gha_runner_mobile_mac_mini_0" {
  name = "${local.runner_name_prefix}-0"
  type = "M1-M"
  zone = "fr-par-3"

  depends_on = [scaleway_account_ssh_key.gha_runner_mobile_mac_mini_ssh_key]
}
