locals {
  # Scaleway
  project_id = "************************************"
  region     = "fr-par"

  runner_name_prefix = "gha-runner-mobile"
}

provider "scaleway" {
  project_id = local.project_id
  region     = local.region
}

terraform {
  required_providers {
    scaleway = {
      source = "scaleway/scaleway"
    }
  }

  backend "gcs" {
    bucket = "galoy-staging-tf-state"
    prefix = "galoy-staging/gha-runner-mobile-mac"
  }
}
