#!/bin/bash

echo -n "Are you running on a VNC/Screen Sharing Session (yes/no): "
read answer

if [ "$answer" != "yes" ]; then
  echo "This script will not work from a SSH Session, make sure to start it using VNC Viewer or from Screen Sharing App in MacOS, retry later bye bye."
  exit 1
fi

cd ~

echo "Installing HomeBrew..."
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
(echo; echo 'eval "$(/opt/homebrew/bin/brew shellenv)"') >> ~/.zprofile
eval "$(/opt/homebrew/bin/brew shellenv)"

echo "Installing Docker..."
brew install --cask docker

echo -n "Start XCode once, do the initial setup and press enter... "
read answer

open -a simulator
sleep 30
killall Simulator

echo -n "Start Docker once, do the initial setup and press enter... "
read answer

docker ps

echo "Installing dependencies used by actions"
brew install nvm
brew tap homebrew/cask-versions
brew install --cask zulu17
brew install --cask android-studio
brew install --cask android-commandlinetools

echo "Start Android Studio once, and set it up using https://reactnative.dev/docs/environment-setup?os=macos&platform=android"
echo -n "Do the initial setup and then press enter... "
read answer

cat <<EOF >> ~/.zshrc

export JAVA_HOME=/Library/Java/JavaVirtualMachines/zulu-17.jdk/Contents/Home
export ANDROID_HOME=\$HOME/Library/Android/sdk
export PATH=\$PATH:\$ANDROID_HOME/emulator
export PATH=\$PATH:\$ANDROID_HOME/platform-tools
EOF
source ~/.zshrc

adb devices -l

echo -n "Setting up NodeJS"
mkdir ~/.nvm
cat <<EOF >> ~/.zshrc

export NVM_DIR="\$HOME/.nvm"
  [ -s "/opt/homebrew/opt/nvm/nvm.sh" ] && \. "/opt/homebrew/opt/nvm/nvm.sh"  # This loads nvm
  [ -s "/opt/homebrew/opt/nvm/etc/bash_completion.d/nvm" ] && \. "/opt/homebrew/opt/nvm/etc/bash_completion.d/nvm"  # This loads nvm bash_completion
EOF
source ~/.zshrc

nvm install 20

echo "Setting up Nix..."
curl --proto '=https' --tlsv1.2 -sSf -L https://install.determinate.systems/nix | sh -s -- install

echo "Setting up Applesiutils..."
brew tap wix/brew
brew install applesimutils

echo "Setting up Cocoapods..."
brew install cocoapods
sudo gem install bundler:2.2.30

echo "Setting up RVM..."
curl -sSL https://get.rvm.io | bash -s stable --ruby
brew install openssl
rvm install 2.7.7
rvm reinstall 2.7.7 --with-openssl-dir="/opt/homebrew/opt/openssl@1.1"

sudo mkdir /Users/<USER>
sudo chown $(whoami) /Users/<USER>

echo "Setting up GitHub Actions Runner..."
cd ~
mkdir actions-runner && cd actions-runner
curl -o actions-runner-osx-arm64-2.313.0.tar.gz -L https://github.com/actions/runner/releases/download/v2.313.0/actions-runner-osx-arm64-2.313.0.tar.gz
echo "97258c75cf500f701f8549289c85d885a9497f7886c102bf4857eed8764a9143  actions-runner-osx-arm64-2.313.0.tar.gz" | shasum -a 256 -c
tar xzf ./actions-runner-osx-arm64-2.313.0.tar.gz
rm actions-runner-osx-arm64-2.313.0.tar.gz

./config.sh --url https://github.com/GaloyMoney/galoy-mobile --name gha-mobile-runner
./svc.sh install
./svc.sh start

rm ~/setup.sh

cd ~
