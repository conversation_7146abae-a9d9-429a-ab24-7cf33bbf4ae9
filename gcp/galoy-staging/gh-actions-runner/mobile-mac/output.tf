output "scw_ssh_key" {
  value     = tls_private_key.scw_ssh_key.private_key_pem
  sensitive = true
}

output "runner0_details" {
  value = {
    ip       = scaleway_apple_silicon_server.gha_runner_mobile_mac_mini_0.ip
    username = try(regex("vnc://(.*):[^@]+@.*:.*", scaleway_apple_silicon_server.gha_runner_mobile_mac_mini_0.vnc_url)[0], "m1")
    password = try(regex("vnc://.*:([^@]+)@.*:.*", scaleway_apple_silicon_server.gha_runner_mobile_mac_mini_0.vnc_url)[0], "")
  }
  sensitive = true
}
