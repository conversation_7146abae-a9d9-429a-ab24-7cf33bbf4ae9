#!/bin/bash

set -e

if [[ $(which ytt) == "" ]]; then
  echo "You will need to install ytt to repipe. https://carvel.dev/ytt/"
  exit 1
fi

target="${FLY_TARGET:-galoy}"
team=dev

REPO_ROOT_DIR="$(git rev-parse --show-toplevel)"

TMPDIR=""
TMPDIR=$(mktemp -d -t repipe.XXXXXX)
trap "rm -rf ${TMPDIR}" INT TERM QUIT EXIT

ytt -f ${REPO_ROOT_DIR}/gcp/galoy-staging/gh-actions-runner/ci > ${TMPDIR}/pipeline.yml

echo "Updating pipeline @ ${target}"

fly -t ${target} set-pipeline --team=${team} -p gh-actions-runner -c ${TMPDIR}/pipeline.yml
fly -t ${target} unpause-pipeline --team=${team} -p gh-actions-runner
