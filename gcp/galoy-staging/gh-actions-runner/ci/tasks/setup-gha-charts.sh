#!/bin/bash

set -eu

CI_ROOT=$(pwd)

cat <<EOF > ${CI_ROOT}/gcloud-creds.json
${GOOGLE_CREDENTIALS}
EOF
cat <<EOF > ${CI_ROOT}/login.ssh
${SSH_PRIVATE_KEY}
EOF
chmod 600 ${CI_ROOT}/login.ssh
cat <<EOF > ${CI_ROOT}/login.ssh.pub
${SSH_PUB_KEY}
EOF
gcloud auth activate-service-account --key-file ${CI_ROOT}/gcloud-creds.json
gcloud compute os-login ssh-keys add --key-file=${CI_ROOT}/login.ssh.pub

export GH_ACTIONS_USER="sa_$(cat ${CI_ROOT}/gcloud-creds.json  | jq -r '.client_id')"
export ADDITIONAL_SSH_OPTS="-o StrictHostKeyChecking=no -i ${CI_ROOT}/login.ssh"

gh_actions_runner_ip=$(cat terraform/metadata | jq -r ".gha_runner_${name}_ip")

set +e
for i in {1..120}; do
  echo "Attempt ${i} to find docker on gh-actions-runner"
  ssh ${ADDITIONAL_SSH_OPTS} ${GH_ACTIONS_USER}@${gh_actions_runner_ip} "which docker" && break
  sleep 4
done
set -e

ssh ${ADDITIONAL_SSH_OPTS} ${GH_ACTIONS_USER}@${gh_actions_runner_ip} "sudo usermod -aG docker ${GH_ACTIONS_USER}"
ssh ${ADDITIONAL_SSH_OPTS} ${GH_ACTIONS_USER}@${gh_actions_runner_ip} "docker ps"

ssh ${ADDITIONAL_SSH_OPTS} ${GH_ACTIONS_USER}@${gh_actions_runner_ip} \
   "mkdir actions-runner && cd actions-runner && \
   curl -o actions-runner-linux-x64-2.294.0.tar.gz -L https://github.com/actions/runner/releases/download/v2.294.0/actions-runner-linux-x64-2.294.0.tar.gz && \
   echo \"a19a09f4eda5716e5d48ba86b6b78fc014880c5619b9dba4a059eaf65e131780  actions-runner-linux-x64-2.294.0.tar.gz\" | shasum -a 256 -c && \
   tar xzf ./actions-runner-linux-x64-2.294.0.tar.gz" || true

registration_token=${ACTIONS_REGISTRATION_TOKEN}

ssh ${ADDITIONAL_SSH_OPTS} ${GH_ACTIONS_USER}@${gh_actions_runner_ip} \
   "cd actions-runner && \
   ./config.sh --url https://github.com/GaloyMoney/${gh_repo} --token $registration_token --unattended --name gh-actions-runner-$RANDOM && \
   sudo ./svc.sh install && \
   sudo ./svc.sh start" || true
