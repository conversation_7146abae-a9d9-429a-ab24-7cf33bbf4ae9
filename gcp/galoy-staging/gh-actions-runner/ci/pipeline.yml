#@ load("@ytt:data", "data")

#@ def pipeline_image():
#@   return data.values.docker_registry + "/galoy-infra-pipeline"
#@ end

#@ def task_image_config():
type: registry-image
source:
  username: #@ data.values.docker_registry_user
  password: #@ data.values.docker_registry_password
  repository: #@ pipeline_image()
#@ end

jobs:
- name: deploy-gha-charts-runner
  serial: true
  plan:
  - { get: gh-actions-runner, trigger: true }
  - put: tf-gha-charts-runner
    params:
      terraform_source: gh-actions-runner/gcp/galoy-staging/gh-actions-runner/charts-vm
  - task: setup-gh-actions
    config:
      platform: linux
      image_resource: #@ task_image_config()
      inputs:
      - name: gh-actions-runner
        path: repo
      - name: tf-gha-charts-runner
        path: terraform
      params:
        GOOGLE_CREDENTIALS: #@ data.values.staging_inception_creds
        SSH_PRIVATE_KEY: #@ data.values.staging_ssh_private_key
        SSH_PUB_KEY: #@ data.values.staging_ssh_pub_key
        GH_TOKEN: #@ data.values.github_token
        ACTIONS_REGISTRATION_TOKEN: #@ data.values.charts_registration_token
        name: charts
        gh_repo: charts
      run:
        path: repo/gcp/galoy-staging/gh-actions-runner/ci/tasks/setup-gha-charts.sh

- name: redeploy-gha-charts-runner
  serial: true
  plan:
  - { get: gh-actions-runner }
  - put: tf-gha-charts-runner
    params:
      action: destroy
      terraform_source: gh-actions-runner/gcp/galoy-staging/gh-actions-runner/charts-vm
    get_params: { action: destroy }
  - put: tf-gha-charts-runner
    params:
      terraform_source: gh-actions-runner/gcp/galoy-staging/gh-actions-runner/charts-vm
  - task: setup-gh-actions
    config:
      platform: linux
      image_resource: #@ task_image_config()
      inputs:
      - name: gh-actions-runner
        path: repo
      - name: tf-gha-charts-runner
        path: terraform
      params:
        GOOGLE_CREDENTIALS: #@ data.values.staging_inception_creds
        SSH_PRIVATE_KEY: #@ data.values.staging_ssh_private_key
        SSH_PUB_KEY: #@ data.values.staging_ssh_pub_key
        GH_TOKEN: #@ data.values.github_token
        ACTIONS_REGISTRATION_TOKEN: #@ data.values.charts_registration_token
        name: charts
        gh_repo: charts
      run:
        path: repo/gcp/galoy-staging/gh-actions-runner/ci/tasks/setup-gha-charts.sh

- name: deploy-gha-mobile-runner
  serial: true
  plan:
  - { get: gh-actions-runner, trigger: true }
  - put: tf-gha-mobile-runner
    params:
      terraform_source: gh-actions-runner/gcp/galoy-staging/gh-actions-runner/mobile-mac

- name: redeploy-gha-mobile-runner
  serial: true
  plan:
  - { get: gh-actions-runner }
  - put: tf-gha-mobile-runner
    params:
      action: destroy
      terraform_source: gh-actions-runner/gcp/galoy-staging/gh-actions-runner/mobile-mac
  - put: tf-gha-mobile-runner
    params:
      terraform_source: gh-actions-runner/gcp/galoy-staging/gh-actions-runner/mobile-mac

resources:
- name: gh-actions-runner
  type: git
  source:
    paths: ["gcp/galoy-staging/gh-actions-runner"]
    uri: #@ data.values.git_uri
    branch: #@ data.values.git_branch
    private_key: #@ data.values.github_private_key

- name: tf-gha-charts-runner
  type: terraform
  source:
    env_name: default
    backend_type: gcs
    backend_config:
      bucket: #@ data.values.staging_state_bucket
      prefix: galoy-staging/gha-runner-charts-vm
      credentials: #@ data.values.staging_inception_creds
    env:
      GOOGLE_CREDENTIALS: #@ data.values.staging_inception_creds

- name: tf-gha-mobile-runner
  type: terraform
  source:
    env_name: default
    backend_type: gcs
    backend_config:
      bucket: #@ data.values.staging_state_bucket
      prefix: galoy-staging/gha-runner-mobile-mac
      credentials: #@ data.values.staging_inception_creds
    env:
      SCW_ACCESS_KEY: #@ data.values.scaleway_access_key
      SCW_SECRET_KEY: #@ data.values.scaleway_secret_key

resource_types:
- name: terraform
  type: docker-image
  source:
    repository: ljfranklin/terraform-resource
    tag: latest
