#@data/values
---
git_uri: **************:blinkbitcoin/blink-deployments.git
git_branch: main
github_private_key: ((github.private_key))
github_token: ((github.api_token))

docker_registry: us.gcr.io/galoy-org
docker_registry_user: ((docker-creds.username))
docker_registry_password: ((docker-creds.password))

staging_inception_creds: ((staging-gcp-creds.creds_json))
staging_ssh_private_key: ((staging-ssh.ssh_private_key))
staging_ssh_pub_key: ((staging-ssh.ssh_public_key))
staging_state_bucket: ((staging-gcp-creds.bucket_name))

scaleway_access_key: ((scaleway.access_key))
scaleway_secret_key: ((scaleway.secret_key))

charts_registration_token: AJV5UEREFK3JJPFHTRCFUMTFYXTGA
