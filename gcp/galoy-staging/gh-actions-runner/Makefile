CHARTS_ZONE:=$(shell cd charts-vm && tofu output -raw gha_runner_zone)
CHARTS_PROJECT:=$(shell cd charts-vm && tofu output -raw gha_runner_project)
CHARTS_RUNNER_NAME:=$(shell cd charts-vm && tofu output -raw gha_runner_charts_name)

MOBILE_MAC_RUNNER0_IP:=$(shell cd mobile-mac && tofu output -json runner0_details | jq -r '.ip')
MOBILE_MAC_RUNNER0_USERNAME:=$(shell cd mobile-mac && tofu output -json runner0_details | jq -r '.username')
MOBILE_MAC_RUNNER0_PASSWORD:=$(shell cd mobile-mac && tofu output -json runner0_details | jq -r '.password')

ssh-gha-charts-vm:
	gcloud compute ssh $(CHARTS_RUNNER_NAME) --zone=$(ZONE) --project=$(PROJECT)

ssh-gha-mobile-mac-0:
	@ssh $(MOBILE_MAC_RUNNER0_USERNAME):$(MOBILE_MAC_RUNNER0_PASSWORD)@$(MOBILE_MAC_RUNNER0_IP)
