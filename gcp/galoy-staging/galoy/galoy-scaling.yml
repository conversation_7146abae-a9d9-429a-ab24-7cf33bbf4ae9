mongodb:
  resources:
    requests:
      cpu: 250m
      memory: 800Mi
    limits:
      cpu: 350m
      memory: 1200Mi
  extraFlags:
    - "--wiredTigerCacheSizeGB=0.25"

galoy:
  api:
    resources:
      requests:
        cpu: 450m
        memory: 300Mi
      limits:
        cpu: 600m
        memory: 500Mi
  trigger:
    resources:
      requests:
        cpu: 75m
        memory: 200Mi
      limits:
        cpu: 150m
        memory: 300Mi
  exporter:
    resources:
      requests:
        cpu: 25m
        memory: 150Mi
      limits:
        cpu: 100m
        memory: 250Mi
  mongoBackupCron:
    resources:
      requests:
        cpu: 300m
        memory: 300Mi
      limits:
        cpu: 400m
        memory: 500Mi
  galoyCron:
    resources:
      requests:
        cpu: 500m
        memory: 250Mi
      limits:
        cpu: 650m
        memory: 400Mi
  mongodbMigrationJob:
    resources:
      requests:
        cpu: 100m
        memory: 100Mi
      limits:
        cpu: 200m
        memory: 200Mi
  notifications:
    serverDeployment:
      replicas: 1
      resources:
        requests:
          cpu: 5m
          memory: 20Mi
        limits:
          cpu: 50m
          memory: 64Mi
    jobsDeployment:
      replicas: 1
      resources:
        requests:
          cpu: 100m
          memory: 32Mi
        limits:
          cpu: 200m
          memory: 64Mi

kratos:
  statefulset:
    resources:
      requests:
        cpu: 100m
        # to reduce back once memlead is fixed
        # https://github.com/ory/kratos/issues/3258
        memory: 500Mi
      limits:
        cpu: 300m
        memory: 1000Mi
  deployment:
    resources:
      requests:
        cpu: 100m
        memory: 500Mi
      limits:
        cpu: 300m
        memory: 1000Mi
