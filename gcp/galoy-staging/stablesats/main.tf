module "shared" {
  source = "../shared"
}
variable "secrets" { sensitive = true }

module "stablesats" {
  source             = "../../../modules/services/stablesats/"
  gcp_project        = module.shared.gcp_project
  gcp_region         = module.shared.gcp_region
  pg_tier            = "db-g1-small"
  db_pool_size       = 10
  ha_pg              = false
  name_prefix        = module.shared.name_prefix
  secrets            = var.secrets
  root_domain        = module.shared.root_domain
  play_money_hedging = true
}

data "google_container_cluster" "primary" {
  project  = module.shared.gcp_project
  name     = module.shared.cluster_name
  location = module.shared.gcp_region
}

data "google_client_config" "default" {
  provider = google-beta
}

provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
}

provider "helm" {
  kubernetes {
    host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
    token                  = data.google_client_config.default.access_token
    cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-staging-tf-state"
    prefix = "galoy-staging/services/stablesats"
  }
}
