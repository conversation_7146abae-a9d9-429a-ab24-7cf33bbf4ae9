module "shared" {
  source = "../shared"
}

variable "secrets" { sensitive = true }

variable "lnd1_pass" {
  sensitive = true
}

module "bitcoin" {
  source = "../../../modules/services/bitcoin/"

  secrets = var.secrets

  gcp_project = module.shared.gcp_project
  gcp_region  = module.shared.gcp_region

  specter_dns      = "specter.${module.shared.root_domain}"
  sync_from_bucket = false

  lnd1_pass = var.lnd1_pass

  lnd1_alias     = "lnd1.staging.blink.sv"
  lnd1_public_ip = "*************"

  lnd2_alias     = "lnd2.staging.blink.sv"
  lnd2_public_ip = "**************"

  bria_replicas = 1
  pg_tier       = "db-custom-1-3840"

  bitcoind_pvc_size  = "210Gi"
  bitcoind2_pvc_size = "211Gi"

  bitcoind_onchain_pvc_size         = "200Gi"
  bitcoind_onchain_sync_from_bucket = false

  name_prefix     = module.shared.name_prefix
  bitcoin_network = module.shared.bitcoin_network

  okex_exchange_address = "tb1qfqh7ksqcrhjgq35clnf06l5d9s6tk2ke46ecrj"

  ha_pg = false

  # The Bria cold wallet is managed outside of terraform.
  # Match this name with the cold wallet name in Bria.
  bria_cold_wallet_name = "blink-staging-cold"
}

output "bria_api_key" {
  value     = module.bitcoin.bria_api_key
  sensitive = true
}

output "stablesats_address" {
  value = module.bitcoin.stablesats_address
}

data "google_container_cluster" "primary" {
  project  = module.shared.gcp_project
  name     = module.shared.cluster_name
  location = module.shared.gcp_region
}

data "google_client_config" "default" {
  provider = google-beta
}

provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
}

provider "helm" {
  kubernetes {
    host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
    token                  = data.google_client_config.default.access_token
    cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-staging-tf-state"
    prefix = "galoy-staging/services/bitcoin"
  }
}
