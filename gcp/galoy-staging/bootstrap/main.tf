module "bootstrap" {
  source = "../../../modules/infra/vendor/tf/bootstrap/gcp"

  name_prefix = "galoy-staging"
  gcp_project = "galoy-staging"
}

output "inception_sa" {
  value = module.bootstrap.inception_sa
}
output "tf_state_bucket_name" {
  value = module.bootstrap.tf_state_bucket_name
}
output "tf_state_bucket_location" {
  value = module.bootstrap.tf_state_bucket_location
}

terraform {
  backend "gcs" {
    bucket = "galoy-org-tf-state"
    prefix = "galoy-org/environments/galoy-staging"
  }
}
