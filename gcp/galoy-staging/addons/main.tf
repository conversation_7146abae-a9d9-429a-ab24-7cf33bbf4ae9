module "shared" {
  source = "../shared"
}

variable "secrets" { sensitive = true }

module "addons" {
  source      = "../../../modules/services/addons/"
  name_prefix = module.shared.name_prefix
  gcp_project = module.shared.gcp_project
  gcp_region  = module.shared.gcp_region

  nostr_public_key      = "0634098c376910a4cd429a5d31c7bf3e5addafa708001f112c95e5cef8d5fa11"
  tld_domain            = "staging.blink.sv"
  extra_pay_domains     = ["pay.staging.blink.sv"]
  nip05_domain          = "agbeg.in"
  legacy_website_domain = "staging.bbw.sv"

  secrets     = var.secrets
  root_domain = module.shared.root_domain

  primary_offchain_lnd = "lnd2"

  bitcoin_network = module.shared.bitcoin_network

  admin_panel_authorized_emails = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
  ]
}

data "google_container_cluster" "primary" {
  project  = module.shared.gcp_project
  name     = module.shared.cluster_name
  location = module.shared.gcp_region
}

data "google_client_config" "default" {
  provider = google-beta
}

provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
}

provider "helm" {
  kubernetes {
    host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
    token                  = data.google_client_config.default.access_token
    cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-staging-tf-state"
    prefix = "galoy-staging/services/addons"
  }
}
