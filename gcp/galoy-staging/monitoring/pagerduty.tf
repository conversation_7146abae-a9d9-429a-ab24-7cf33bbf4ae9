provider "pagerduty" {
  token = local.pagerduty_api_key
}

locals {
  // Spreadsheet to see rotation schedule
  // https://docs.google.com/spreadsheets/d/1jtFp32RhArBGjIhBm1YG1nJ-TTldl-C4i-Sb9cGTwCo/edit#gid=0

  // Time of day for 'east' shift to start
  east_layer_start = "00:00:00Z"
  // Time of day for 'west' shift to start
  west_layer_start = "12:00:00Z" // should be 12 hours after east_layer_start

  // Rotation starts at standup east on mondays
  blue_rotation_start = "2025-05-26T08:00:00Z"
  // Rotation finishes 1 week later
  blue_rotation_end = timeadd(local.blue_rotation_start, "168h")
  // Green rotation starts when blue ends, finishes a week later
  green_rotation_end = timeadd(local.blue_rotation_end, "168h")

  // Blue is this week's primary
  blue_east_email = "<EMAIL>"
  blue_west_email = "<EMAIL>"

  // Green is this week's backup, next week's primary
  green_east_email = "<EMAIL>"
  green_west_email = "<EMAIL>"

  // Yellow is next week's backup
  yellow_east_email = "<EMAIL>"
  yellow_west_email = "<EMAIL>"

  // Time of day for 'Blink East' shift to start
  blink_east_layer_start = "00:00:00Z"
  // Time of day for 'Blink West' shift to start
  blink_west_layer_start = "12:00:00Z" // should be 12 hours after east_layer_start

  # Blink is on fixed secondary backup
  blink_east_email = "<EMAIL>"
  blink_west_email = "<EMAIL>"

  users = {
    // EAST
    "<EMAIL>"   = "Justin Carter",
    "<EMAIL>" = "Kartik Shah",
    "<EMAIL>"       = "Sebastien Verreault",
    "<EMAIL>" = "Sandipan Dey",
    "<EMAIL>"  = "Vaibhav Dixit",

    // WEST
    "<EMAIL>"    = "Nicolas Burtey",
    "<EMAIL>"   = "Jose",
    "<EMAIL>" = "Arvin",

    // BLINK EAST
    # "<EMAIL>" = "Openoms", // added as data because account owner
    "<EMAIL>" = "Kim Neunert",
    // BLINK WEST
    "<EMAIL>" = "Juan Pablo",
  }
}

resource "pagerduty_user" "users" {
  for_each = toset(keys(local.users))
  name     = local.users[each.value]
  email    = each.value
}

data "pagerduty_user" "owner" {
  email = "<EMAIL>"
}

resource "pagerduty_schedule" "primary" {
  name      = "Primary schedule"
  time_zone = "Etc/UTC"

  layer {
    name                         = "East layer"
    start                        = local.blue_rotation_start
    end                          = local.blue_rotation_end
    rotation_virtual_start       = local.blue_rotation_start
    rotation_turn_length_seconds = 86400

    users = local.blue_east_email == "<EMAIL>" ? [data.pagerduty_user.owner.id] : [resource.pagerduty_user.users[local.blue_east_email].id]

    restriction {
      type              = "daily_restriction"
      start_time_of_day = local.east_layer_start
      duration_seconds  = 43200
    }
  }

  layer {
    name                         = "West layer"
    start                        = local.blue_rotation_start
    end                          = local.blue_rotation_end
    rotation_virtual_start       = local.blue_rotation_start
    rotation_turn_length_seconds = 86400

    users = [resource.pagerduty_user.users[local.blue_west_email].id]

    restriction {
      type              = "daily_restriction"
      start_time_of_day = local.west_layer_start
      duration_seconds  = 43200
    }
  }

  layer {
    name                         = "East layer next"
    start                        = local.blue_rotation_end
    end                          = local.green_rotation_end
    rotation_virtual_start       = local.blue_rotation_end
    rotation_turn_length_seconds = 86400

    users = local.green_east_email == "<EMAIL>" ? [data.pagerduty_user.owner.id] : [resource.pagerduty_user.users[local.green_east_email].id]

    restriction {
      type              = "daily_restriction"
      start_time_of_day = local.east_layer_start
      duration_seconds  = 43200
    }
  }

  layer {
    name                         = "West layer next"
    start                        = local.blue_rotation_end
    end                          = local.green_rotation_end
    rotation_virtual_start       = local.blue_rotation_end
    rotation_turn_length_seconds = 86400

    users = [resource.pagerduty_user.users[local.green_west_email].id]

    restriction {
      type              = "daily_restriction"
      start_time_of_day = local.west_layer_start
      duration_seconds  = 43200
    }
  }
}

resource "pagerduty_schedule" "backup" {
  name      = "Backup schedule"
  time_zone = "Etc/UTC"

  layer {
    name                         = "East layer"
    start                        = local.blue_rotation_start
    end                          = local.blue_rotation_end
    rotation_virtual_start       = local.blue_rotation_start
    rotation_turn_length_seconds = 86400

    users = local.green_east_email == "<EMAIL>" ? [data.pagerduty_user.owner.id] : [resource.pagerduty_user.users[local.green_east_email].id]

    restriction {
      type              = "daily_restriction"
      start_time_of_day = local.east_layer_start
      duration_seconds  = 43200
    }
  }

  layer {
    name                         = "West layer"
    start                        = local.blue_rotation_start
    end                          = local.blue_rotation_end
    rotation_virtual_start       = local.blue_rotation_start
    rotation_turn_length_seconds = 86400

    users = [resource.pagerduty_user.users[local.green_west_email].id]
    restriction {
      type              = "daily_restriction"
      start_time_of_day = local.west_layer_start
      duration_seconds  = 43200
    }
  }

  layer {
    name                         = "East layer next"
    start                        = local.blue_rotation_end
    end                          = local.green_rotation_end
    rotation_virtual_start       = local.blue_rotation_end
    rotation_turn_length_seconds = 86400

    users = local.yellow_east_email == "<EMAIL>" ? [data.pagerduty_user.owner.id] : [resource.pagerduty_user.users[local.yellow_east_email].id]

    restriction {
      type              = "daily_restriction"
      start_time_of_day = local.east_layer_start
      duration_seconds  = 43200
    }
  }

  layer {
    name                         = "West layer next"
    start                        = local.blue_rotation_end
    end                          = local.green_rotation_end
    rotation_virtual_start       = local.blue_rotation_end
    rotation_turn_length_seconds = 86400

    users = [resource.pagerduty_user.users[local.yellow_west_email].id]

    restriction {
      type              = "daily_restriction"
      start_time_of_day = local.west_layer_start
      duration_seconds  = 43200
    }
  }
}

resource "pagerduty_schedule" "blink" {
  name      = "Blink schedule"
  time_zone = "Etc/UTC"

  layer {
    name                         = "Blink East layer"
    start                        = local.blue_rotation_start
    end                          = local.green_rotation_end
    rotation_virtual_start       = local.blue_rotation_start
    rotation_turn_length_seconds = 86400

    users = local.blink_east_email == "<EMAIL>" ? [data.pagerduty_user.owner.id] : [resource.pagerduty_user.users[local.blink_east_email].id]

    restriction {
      type              = "daily_restriction"
      start_time_of_day = local.blink_east_layer_start
      duration_seconds  = 43200
    }
  }

  layer {
    name                         = "Blink West layer"
    start                        = local.blue_rotation_start
    end                          = local.green_rotation_end
    rotation_virtual_start       = local.blue_rotation_start
    rotation_turn_length_seconds = 86400

    users = [resource.pagerduty_user.users[local.blink_west_email].id]

    restriction {
      type              = "daily_restriction"
      start_time_of_day = local.blink_west_layer_start
      duration_seconds  = 43200
    }
  }
}

resource "pagerduty_escalation_policy" "default" {
  name      = "default"
  num_loops = 2

  rule {
    escalation_delay_in_minutes = 20

    target {
      type = "schedule_reference"
      id   = pagerduty_schedule.primary.id
    }
  }

  rule {
    escalation_delay_in_minutes = 20

    target {
      type = "schedule_reference"
      id   = pagerduty_schedule.backup.id
    }
  }

  rule {
    escalation_delay_in_minutes = 20

    target {
      type = "schedule_reference"
      id   = pagerduty_schedule.blink.id
    }
  }
}
