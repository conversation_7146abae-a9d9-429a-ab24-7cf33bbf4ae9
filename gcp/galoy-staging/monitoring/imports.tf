import {
  id = "galoy-staging-api/err_pct"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.err_pct
}

import {
  id = "galoy-staging-ingress/500_pct"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.five_hundred_pct
}

import {
  id = "galoy-staging-ingress/401_pct"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.four_hundred_pct
}

import {
  id = "galoy-staging-api/error_warn_pct"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.error_warn_pct
}

import {
  id = "galoy-staging-api/derived.phone.countryCode"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.phone_country_code
}

import {
  id = "galoy-staging-api/derived.phone.commonPrefix"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.phone_7_char_prefix
}

import {
  id = "galoy-staging-ingress/peer.address.iponly"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.address_iponly
}

import {
  id = "galoy-staging-api/app.payments"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.app_payments
}

import {
  id = "galoy-staging-api/graphql.payments"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.graphql_payments
}

import {
  id = "galoy-staging-api/derived.payment.success.intraledger"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.derived_payment_success_intraledger
}

import {
  id = "galoy-staging-api/derived.payment.success.lightning"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.derived_payment_success_lightning
}

import {
  id = "galoy-staging-api/derived.payment.success.onchain"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.derived_payment_success_onchain
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.current_position"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.current_position
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.exposure_liability_ratio"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.exposure_liability_ratio
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.exposure_leverage_ratio"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.exposure_leverage_ratio
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.margin_leverage_ratio"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.margin_leverage_ratio
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.last_price"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.last_price
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.last_price_in_usd"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.last_price_in_usd
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.liability"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.liability
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.target_liability"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.target_liability
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.notional_usd"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.notional_usd
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.position_in_ct"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.position_in_ct
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.signed_usd_exposure"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.signed_usd_exposure
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.funding_deposit_action"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.funding_deposit_action
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.funding_transfer_action"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.funding_transfer_action
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.hedge_action"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.hedge_action
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.funding_available_balance_total"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.funding_available_balance_total
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.trading_available_balance_free"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.trading_available_balance_free
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.trading_available_balance_used"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.trading_available_balance_used
}

import {
  id = "galoy-staging-stablesats-dealer/stablesats.internal.trading_available_balance_total"
  to = module.monitoring.module.honeycomb.honeycombio_derived_column.trading_available_balance_total
}

# Grafana data source import - uncomment and update ID when needed
# To get the data source ID, run: scripts/fix-grafana-datasource-conflict.sh
# import {
#   id = "REPLACE_WITH_ACTUAL_DATASOURCE_ID"
#   to = module.monitoring.grafana_data_source.stackdriver
# }
