module "shared" {
  source = "../shared"
}

locals {
  tf_state_bucket_name    = "galoy-bbw-tf-state"
  buckets_location        = "US-CENTRAL1"
  backups_bucket_location = "US-CENTRAL1"
  backups_bucket_name     = "blink-prod-backups"
  inception_sa            = "<EMAIL>"

  users = [
    {
      id        = "user:<EMAIL>",
      bastion   = true,
      inception = true,
      platform  = true,
      logs      = true,
    },
    {
      id        = "user:<EMAIL>",
      bastion   = true,
      inception = true,
      platform  = true,
      logs      = true,
    },
    {
      id        = "user:<EMAIL>", // Nicolas
      bastion   = true,
      inception = true,
      platform  = true,
      logs      = true,
    },
    {
      id        = "user:<EMAIL>", // Jose
      bastion   = true,
      inception = false,
      platform  = false,
      logs      = false,
    },
    {
      id        = "user:<EMAIL>", // <PERSON><PERSON><PERSON>
      bastion   = true,
      inception = false,
      platform  = false,
      logs      = false,
    },
    {
      id        = "user:<EMAIL>", // Arvin
      bastion   = true,
      inception = false,
      platform  = false,
      logs      = false,
    },
    {
      id        = "user:<EMAIL>", // Sandipan
      bastion   = false,
      inception = false,
      platform  = false,
      logs      = false,
    },
    {
      id        = "user:<EMAIL>", // Openoms
      bastion   = true,
      inception = true,
      platform  = true,
      logs      = true,
    },
    {
      id        = "user:<EMAIL>", // Juan
      bastion   = true,
      inception = false,
      platform  = true,
      logs      = true,
    },
  ]
}

module "inception" {
  source = "../../../modules/infra/vendor/tf/inception/gcp"

  name_prefix             = module.shared.name_prefix
  gcp_project             = module.shared.gcp_project
  region                  = module.shared.gcp_region
  inception_sa            = local.inception_sa
  tf_state_bucket_name    = local.tf_state_bucket_name
  buckets_location        = local.buckets_location
  backups_bucket_location = local.backups_bucket_location
  backups_bucket_name     = local.backups_bucket_name
  users                   = local.users
}

output "gcp_project" {
  value = module.shared.gcp_project
}

output "bastion_name" {
  value = module.inception.bastion_name
}

output "bastion_zone" {
  value = module.inception.bastion_zone
}

output "cluster_sa" {
  value = module.inception.cluster_sa
}

output "grafana_sa" {
  value = module.inception.grafana_sa
}

output "backups_bucket_name" {
  value = module.inception.backups_bucket_name
}

output "backups_sa" {
  value = module.inception.backups_sa
}

terraform {
  backend "gcs" {
    bucket = "galoy-bbw-tf-state"
    prefix = "galoy-bbw/inception"
  }
}
