module "bootstrap" {
  source = "../../../modules/infra/vendor/tf/bootstrap/gcp"

  name_prefix              = "galoy-bbw"
  gcp_project              = "galoy-bitcoinbeach"
  tf_state_bucket_location = "US-CENTRAL1"
}

output "inception_sa" {
  value = module.bootstrap.inception_sa
}
output "tf_state_bucket_name" {
  value = module.bootstrap.tf_state_bucket_name
}
output "tf_state_bucket_location" {
  value = module.bootstrap.tf_state_bucket_location
}

terraform {
  backend "gcs" {
    bucket = "galoy-org-tf-state"
    prefix = "galoy-org/environments/galoy-bitcoinbeach"
  }
}
