module "shared" {
  source = "../shared"
}

module "platform" {
  source = "../../../modules/infra/vendor/tf/platform/gcp"

  name_prefix               = module.shared.name_prefix
  gcp_project               = module.shared.gcp_project
  region                    = module.shared.gcp_region
  node_service_account      = "<EMAIL>"
  node_default_machine_type = "n2-highmem-4"
  max_default_node_count    = 3
  deploy_lnd_ips            = true

  pg_ha = true
}

output "lnd1_public_ip" {
  value = module.platform.lnd1_ip
}

output "lnd2_public_ip" {
  value = module.platform.lnd2_ip
}

terraform {
  backend "gcs" {
    bucket = "galoy-bbw-tf-state"
    prefix = "galoy-bbw/platform"
  }
}
