module "shared" {
  source = "../shared"
}

variable "secrets" {
  sensitive = true
}

provider "kafka" {
  bootstrap_servers = ["kafka-kafka-plain-bootstrap.galoy-bbw-kafka.svc.cluster.local:9092"]
  tls_enabled       = false
}

module "monitoring" {
  source = "../../../modules/services/monitoring/"

  secrets     = var.secrets
  gcp_project = module.shared.gcp_project
  name_prefix = module.shared.name_prefix
  root_domain = module.shared.root_domain

  grafana_allowed_oauth_domain = ["galoy.io", "blinkbtc.com", "blink.sv"]
}

data "google_container_cluster" "primary" {
  project  = module.shared.gcp_project
  name     = module.shared.cluster_name
  location = module.shared.gcp_region
}

data "google_client_config" "default" {
  provider = google-beta
}

provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
}

provider "helm" {
  kubernetes {
    host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
    token                  = data.google_client_config.default.access_token
    cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-bbw-tf-state"
    prefix = "galoy-bbw/services/monitoring"
  }
  required_providers {
    kafka = {
      source  = "Mongey/kafka"
      version = "0.5.2"
    }
  }
}
