module "shared" {
  source = "../shared"
}

variable "secrets" { sensitive = true }

variable "lnd1_pass" {
  sensitive = true
}

module "bitcoin" {
  source = "../../../modules/services/bitcoin/"

  secrets         = var.secrets
  name_prefix     = module.shared.name_prefix
  gcp_project     = module.shared.gcp_project
  gcp_region      = module.shared.gcp_region
  bitcoin_network = module.shared.bitcoin_network

  specter_dns = "specter.${module.shared.root_domain}"

  lnd_base_fee = "0"
  lnd_fee_rate = "500"

  lnd1_pass           = var.lnd1_pass
  lnd1_existing_claim = "lnd1"
  lnd1_public_ip      = "**********"
  lnd1_alias          = "lnd1.blink.sv"

  lnd2_public_ip     = "**************"
  lnd2_storage_class = "premium-rwo"
  lnd2_alias         = "lnd2.blink.sv"

  okex_exchange_address = "**************************************************************"

  # The Bria cold wallet is managed outside of terraform.
  # Match this name with the cold wallet name in Bria.
  bria_cold_wallet_name = "blink-bbw-cold"
}

output "bria_api_key" {
  value     = module.bitcoin.bria_api_key
  sensitive = true
}

output "stablesats_address" {
  value = module.bitcoin.stablesats_address
}

data "google_container_cluster" "primary" {
  project  = module.shared.gcp_project
  name     = module.shared.cluster_name
  location = module.shared.gcp_region
}

data "google_client_config" "default" {
  provider = google-beta
}

provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
}

provider "helm" {
  kubernetes {
    host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
    token                  = data.google_client_config.default.access_token
    cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-bbw-tf-state"
    prefix = "galoy-bbw/services/bitcoin"
  }
}
