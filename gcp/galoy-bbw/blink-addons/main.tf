module "shared" {
  source = "../shared"
}

variable "secrets" {
  sensitive = true
}

module "blink_addons" {
  source      = "../../../modules/services/blink-addons/"
  name_prefix = module.shared.name_prefix
  root_domain = "blink.sv"

  secrets = var.secrets

  gcp_project = module.shared.gcp_project
  gcp_region  = module.shared.gcp_region

  bigquery_dataset              = "dataform_galoy_bbw"
  bigquery_report_circles_table = "report_rt_circles"

  cron_enabled = "true"

  blink_fiat_airtable_base_id            = "appIL7b7wDPZSzLNI"
  blink_fiat_telegram_group_chat_id_sell = "-1001814412000"
  blink_fiat_telegram_group_chat_id_buy  = "-1002132960081"
  blink_fiat_google_analytics_id         = "G-ZFPP7TDY0K"
  email_domain                           = "blink.sv"
  blink_fiat_email_sender_name           = "Blink Support"

  blink_kyc_onfido_workflow_id = "9eacac21-ab5b-4585-bf21-db8dba25f428"
}

data "google_container_cluster" "primary" {
  project  = module.shared.gcp_project
  name     = module.shared.cluster_name
  location = module.shared.gcp_region
}

data "google_client_config" "default" {
  provider = google-beta
}

provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
}

provider "helm" {
  kubernetes {
    host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
    token                  = data.google_client_config.default.access_token
    cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-bbw-tf-state"
    prefix = "galoy-bbw/services/blink-addons"
  }
}
