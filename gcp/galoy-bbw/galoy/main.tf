module "shared" {
  source = "../shared"
}

variable "secrets" {
  sensitive = true
}

module "galoy" {
  source = "../../../modules/galoy/"

  gcp_project = module.shared.gcp_project
  gcp_region  = module.shared.gcp_region

  rebalance_enabled = false

  max_hot_wallet = **********

  withdraw_fee_threshold = ********

  lnd_priority = "lnd2"

  email_domain = "blink.sv"

  price_currency_beacon_enabled  = false
  price_exchangeratehost_enabled = true

  quizzes_allow_phone_countries = []
  quizzes_deny_phone_countries  = ["FI", "FR", "NL", "US", "GB", "UK", "SE", "ID", "CM", "GE", "PK", "UG", "ES", "TH"]
  quizzes_deny_asns             = ["AS30986"]

  accounts_deny_ip_countries    = ["RU", "CU", "IR", "KP", "SY"]
  accounts_deny_phone_countries = ["RU", "CU", "IR", "KP", "SY"]

  secondary_domain = "blink.sv"

  tertiary_domain = "bbw.sv"

  lightning_address_domain         = "pay.bbw.sv"
  lightning_address_domain_aliases = ["pay.mainnet.galoy.io"]

  secrets          = var.secrets
  bitcoin_network  = module.shared.bitcoin_network
  name_prefix      = module.shared.name_prefix
  funder_user_name = "BitcoinBeachMarketing"
  withdraw_method  = "proportionalOnImbalance"

  galoy_instance_name = module.shared.galoy_instance_name
  backups_bucket_name = "blink-prod-backups"
  root_domain         = module.shared.root_domain

  unsupported_countries = module.shared.unsupported_countries
  # remove after meta account is set up in Twilio
  unsupported_whatsapp_countries = ["AC", "AD", "AE", "AF", "AG", "AI", "AL", "AM", "AO", "AR", "AS", "AT", "AU", "AW", "AX", "AZ", "BA", "BB", "BD", "BE", "BF", "BG", "BH", "BI", "BJ", "BL", "BM", "BN", "BO", "BQ", "BR", "BS", "BT", "BW", "BY", "BZ", "CA", "CC", "CD", "CF", "CG", "CH", "CI", "CK", "CL", "CM", "CN", "CO", "CR", "CV", "CW", "CX", "CY", "CZ", "DE", "DJ", "DK", "DM", "DO", "DZ", "EC", "EE", "EG", "EH", "ER", "ES", "ET", "FI", "FJ", "FK", "FM", "FO", "FR", "GA", "GB", "GD", "GE", "GF", "GG", "GH", "GI", "GL", "GM", "GN", "GP", "GQ", "GR", "GT", "GU", "GW", "GY", "HK", "HN", "HR", "HT", "HU", "ID", "IE", "IL", "IM", "IN", "IO", "IQ", "IS", "IT", "JE", "JM", "JO", "JP", "KE", "KG", "KH", "KI", "KM", "KN", "KR", "KW", "KY", "KZ", "LA", "LB", "LC", "LI", "LK", "LR", "LS", "LT", "LU", "LV", "LY", "MA", "MC", "MD", "ME", "MF", "MG", "MH", "MK", "ML", "MM", "MN", "MO", "MP", "MQ", "MR", "MS", "MT", "MU", "MV", "MW", "MX", "MY", "MZ", "NA", "NC", "NE", "NF", "NG", "NI", "NL", "NO", "NP", "NR", "NU", "NZ", "OM", "PA", "PE", "PF", "PG", "PH", "PK", "PL", "PM", "PR", "PS", "PT", "PW", "PY", "QA", "RE", "RO", "RS", "RW", "SA", "SB", "SC", "SD", "SE", "SG", "SH", "SI", "SJ", "SK", "SL", "SM", "SN", "SO", "SR", "SS", "ST", "SV", "SX", "SZ", "TA", "TC", "TD", "TG", "TH", "TJ", "TK", "TL", "TM", "TN", "TO", "TR", "TT", "TV", "TW", "TZ", "UA", "UG", "US", "UY", "UZ", "VA", "VC", "VE", "VG", "VI", "VN", "VU", "WF", "WS", "XK", "YE", "YT", "ZA", "ZM", "ZW"]

  extra_cors_allowed_origins = ["https://hoppscotch.io"]

  api_keys_prefix = "blink"

  mongodb_ssd_size = "15Gi"

  openai_assistant_id = "asst_SctirorHgva5DjfRWrSx1ysv"

  twilio_messaging_welcome_content_sid = "HXcabc300366eb443a865c904d5a671916"
}

output "postgresql_creds" {
  value     = module.galoy.postgresql_creds
  sensitive = true
}

data "google_container_cluster" "primary" {
  project  = module.shared.gcp_project
  name     = module.shared.cluster_name
  location = module.shared.gcp_region
}

data "google_client_config" "default" {
  provider = google-beta
}

provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
}

provider "helm" {
  kubernetes {
    host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
    token                  = data.google_client_config.default.access_token
    cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-bbw-tf-state"
    prefix = "galoy-bbw/galoy"
  }
}
