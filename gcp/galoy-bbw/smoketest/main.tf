module "shared" {
  source = "../shared"
}

module "smoketest" {
  source = "../../../modules/infra/vendor/tf/smoketest/gcp"

  name_prefix      = module.shared.name_prefix
  cluster_endpoint = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
  cluster_ca_cert  = data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate

  smoketest_cronjob = true

  k8s_secret_reader_enabled = true
}

output "smoketest_kubeconfig" {
  value     = module.smoketest.smoketest_kubeconfig
  sensitive = true
}

data "google_container_cluster" "primary" {
  project  = module.shared.gcp_project
  name     = module.shared.cluster_name
  location = module.shared.gcp_region
}

data "google_client_config" "default" {
  provider = google-beta
}

provider "kubernetes" {
  experiments {
    manifest_resource = true
  }

  host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
}

provider "helm" {
  kubernetes {
    host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
    token                  = data.google_client_config.default.access_token
    cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-bbw-tf-state"
    prefix = "galoy-bbw/smoketest"
  }
}
