module "shared" {
  source = "../shared"
}


variable "secrets" { sensitive = true }

module "addons" {
  source = "../../../modules/services/addons/"

  name_prefix = module.shared.name_prefix
  gcp_project = module.shared.gcp_project
  gcp_region  = module.shared.gcp_region

  nostr_public_key      = "8fe53b37518e3dbe9bab26d912292001d8b882de9456b7b08b615f912dc8bf4a"
  tld_domain            = "blink.sv"
  nip05_domain          = "agbeg.in"
  legacy_website_domain = "bbw.sv"

  secrets = var.secrets

  root_domain       = module.shared.root_domain
  extra_pay_domains = ["pay.blink.sv", "pay.bbw.sv"]

  extra_map_domains = ["map.blink.sv"]

  primary_offchain_lnd = "lnd2"

  bitcoin_network = module.shared.bitcoin_network

  admin_panel_authorized_emails = [
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "guiller<PERSON>@blinkbtc.com",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>"
  ]
}

data "google_container_cluster" "primary" {
  project  = module.shared.gcp_project
  name     = module.shared.cluster_name
  location = module.shared.gcp_region
}

data "google_client_config" "default" {
  provider = google-beta
}

provider "kubernetes" {
  host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
  token                  = data.google_client_config.default.access_token
  cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
}

provider "helm" {
  kubernetes {
    host                   = "https://${data.google_container_cluster.primary.private_cluster_config.0.private_endpoint}"
    token                  = data.google_client_config.default.access_token
    cluster_ca_certificate = base64decode(data.google_container_cluster.primary.master_auth.0.cluster_ca_certificate)
  }
}

terraform {
  backend "gcs" {
    bucket = "galoy-bbw-tf-state"
    prefix = "galoy-bbw/services/addons"
  }
}
