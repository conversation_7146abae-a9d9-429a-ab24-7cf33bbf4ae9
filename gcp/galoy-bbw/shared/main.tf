locals {
  name_prefix         = "galoy-bbw"
  gcp_project         = "galoy-bitcoinbeach"
  gcp_region          = "us-central1"
  root_domain         = "mainnet.galoy.io"
  cluster_name        = "${local.name_prefix}-cluster"
  bitcoin_network     = "mainnet"
  galoy_instance_name = "Blink"

  unsupported_countries = [
    "RU", # Russia
    "CU", # Cuba
    "IR", # Iran
    "KP", # North Korea
    "SY"  # Syria
  ]
}

output "galoy_instance_name" {
  value = local.galoy_instance_name
}

output "name_prefix" {
  value = local.name_prefix
}

output "gcp_project" {
  value = local.gcp_project
}

output "gcp_region" {
  value = local.gcp_region
}

output "cluster_name" {
  value = local.cluster_name
}

output "root_domain" {
  value = local.root_domain
}

output "bitcoin_network" {
  value = local.bitcoin_network
}

output "unsupported_countries" {
  value = local.unsupported_countries
}
