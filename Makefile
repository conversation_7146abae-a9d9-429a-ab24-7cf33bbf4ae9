fmt:
	tofu fmt -recursive .

sync-staging:
	bin/sync-to-bastion.sh galoy-staging

ssh-staging:
	bin/ssh-to-bastion.sh galoy-staging

bump-vendored-ref:
	bin/bump-vendored-ref.sh $(DEP) $(REF)

vendir:
	@STR_VAR_github_ssh_key_base64=$(GITHUB_SSH_KEY_BASE64) ytt --data-values-env STR_VAR -f vendir > vendir.yml
	vendir sync
	ytt -f vendir > vendir.yml

recompose-supergraph:
	bin/recompose-supergraph.sh

.PHONY: vendir
