<!-- omit in toc -->
# Environment Deployment Procedure

- [Bootstrapping](#bootstrapping)
    - [Client Project in External Organization](#client-project-in-external-organization)
    - [Internal Environment in Same Organization](#internal-environment-in-same-organization)
- [Secret Provisioning](#secret-provisioning)
- [Add Environment to Blink Deployments](#add-environment-to-blink-deployments)
- [Running the CI](#running-the-ci)
    - [1. Inception](#1-inception)
    - [2. Platform](#2-platform)
    - [3. Workers](#3-workers)
    - [4. Services](#4-services)
    - [5. Bitcoin](#5-bitcoin)
    - [6. <PERSON><PERSON><PERSON>](#6-galoy)
    - [7. Monitoring](#7-monitoring)
    - [8. <PERSON>don<PERSON>](#8-addons)
    - [9. Auth](#9-auth)
    - [10. Domains](#10-domains)
    - [11. <PERSON>pecter](#11-specter)

This is an internal guide that describes the rollout procedure for a new environment.

## Bootstrapping

Before proceeding, we need to have the following data:

1. **GCP Project** - with `Organization Owner` *(if external)* or `Project Admin` level access.
1. **Who needs access** - both, <PERSON><PERSON><PERSON>'s internal team and external client's developers.

Someone with elevated access to the GCP Project needs to run the **Bootstrap** phase of our infrastructure.
It is ideally someone who created the project or someone who has been given Project or Organization Owner access.
They will need to have the following set up on their local machine to proceed:

1. [Git](https://git-scm.com/downloads)
1. [Terraform](https://learn.hashicorp.com/tutorials/terraform/install-cli)
1. [Google Cloud SDK](https://cloud.google.com/sdk/docs/install).

#### Client Project in External Organization

Create a new folder and paste the following file:
Fetch the latest ref from [here](https://github.com/GaloyMoney/galoy-infra/blob/main/examples/gcp/bootstrap/main.tf) and modify the source in the module.

```tf
locals {
  name_prefix = ""
  gcp_project = ""
  organization_id = "
  external_users = []
}

module "bootstrap" {
  source = "git::https://github.com/GaloyMoney/galoy-infra.git//modules/bootstrap/gcp?ref=b08e2ed"

  name_prefix = local.name_prefix
  gcp_project = local.gcp_project
}

output "inception_sa" {
  value = module.bootstrap.inception_sa
}
output "tf_state_bucket_name" {
  value = module.bootstrap.tf_state_bucket_name
}
output "tf_state_bucket_location" {
  value = module.bootstrap.tf_state_bucket_location
}
```

Fill up the `locals` variables and add ourselves, with `@galoy.io` email addresses as the external users.

Now, the user needs to login to GCP and run the bootstrap phase. Execute these from `examples/gcp` level.

```bash
$ gcloud auth application-default login
$ tofu apply
$ gcloud iam service-accounts keys create inception-sa-creds.json --iam-account=$(tofu output inception_sa | jq -r)
```

The `tofu output` of Bootstrap and the `inception-sa-creds.json` file which we got from this phase is crucial to continue.
Whoever is in charge should collect these files and details and continue. The client should also backup this folder by pushing to a private git repository because the bootstrap phase could be run if bootstrap module gets changed (shouldn't happen often).

#### Internal Environment in Same Organization

Provision a new terraform backend [here](https://github.com/GaloyMoney/galoy-org-infra/blob/main/inception/tf-state-bucket.tf#L89-L97) with the proper environment name.

Inside `blink-deployments/gcp/{env}/bootstrap/main.tf`, paste the same thing as `blink-deployments/gcp/galoy-staging/bootstrap/main.tf` and modify, primarily the terraform backend with the newly added environment name to org-infra.

Finally, as the admin/owner of the GCP Project, run:

```
$ gcloud auth application-default login
$ tofu apply
$ gcloud iam service-accounts keys create inception-sa-creds.json --iam-account=$(tofu output inception_sa | jq -r)
```

## Secret Provisioning

Mostly, we create new teams per environment (unless it's the same client who needs multiple environments which could be rare).
Keep environment name same as the `name_prefix` for simplicity, the team name can be anything.
In order to create a new team with secrets provisioned in this phase, we need to create a PR with the following items:

1. Add a Concourse Team | [file](https://github.com/GaloyMoney/galoy-org-infra/blob/main/concourse-teams/main.tf)
1. Add the same team in `concourse-service.yml` | [file](https://github.com/GaloyMoney/galoy-org-infra/blob/main/services/concourse-service/concourse.tf)
1. Provision the new environment | [file](https://github.com/GaloyMoney/galoy-org-infra/blob/main/pipeline-creds/provision/main.tf)
1. Add corresponding reproduce for this new environment | [file](https://github.com/GaloyMoney/galoy-org-infra/blob/main/pipeline-creds/reproduce/main.tf)

Make the PR.

This rollout of this work can only be done by people listed [here](https://github.com/GaloyMoney/galoy-org-infra/blob/main/inception/main.tf#L16).

They would need to run both services phase, concourse-services phase and the pipeline-creds provision/reproduce phase.
You don't need to provision all of the files immediately, but can keep most of the files empty at the beginning and provision them as and when needed.
To begin with, at least have the Inception Credentials, the Honeycomb API and the Slack API URL setup.

## Add Environment to Blink Deployments

* Create a clone of `ci/environments/guatt-prod.yml` and rename it to the name of the environment.
   Go through the file and make sure the name of the pipeline, team and environment is proper.
   In the addons phase, make sure you enable those features that are required by this environment.
* Go to `cepler/*.yml` and add the new environment to each of those files with `propagation` enabled.
   They must be deployed after `galoy-bbw`.
   You'll notice that the environment name in the Cepler files is `gcp-{env}`.
* Checkout to `cepler-gates` branch and add in `{env}: HEAD` to each of the files, because the propagation would be locked at internal production environment `galoy-bbw` anyways.
* Add the folder `gcp/{env}` and add in the module interface files as is in the other folders.
   You should be careful with each of them and add proper values on each of those files.

Finally, PR.

It's recommended that you introduce the stages one by one.
When you're adding Infra, you should add in the infra modules, along with it's Cepler environment and so on.

## Running the CI

Once the secrets have been provisioned and the Cepler files are merged to `main`, you can go through and start deploying step by step using the CI:

* Go in the repipe file and comment the `unpause-pipeline` command. (to not roll out everything immediately)
* Run the following:

```bash
$ fly -t {team} login -c https://ci.blink.sv -n {team}
ci/repipe {env}
```

* Go in the Web UI for CI, pause each of the jobs and then unpause the pipeline

We are going to roll out this new environment in the following flow, therefore unpause each of them one by one:

#### 1. Inception

This phase deploys the networking and bastion. After this rolls out, you should check to see if you can SSH. But before SSHing, you might need to upload your public key to GCP:

```bash
$ gcloud compute os-login ssh-keys add --key-file=~/.ssh/id_rsa.pub --project <GCP-Project-ID>
$ ssh [ext_]username_galoy_io@ip
```

<details>
<summary>Did honeymarker fail?</summary>
<br />
Most likely, it couldn't find the dataset on Honeycomb. Here's the fix:

1. Create a file `main.tf` in an empty directory:

```terraform
resource "honeycombio_dataset" "env" {
  name = "Environment Name"
}
```

2. Fetch API Key from [here](https://ui.honeycomb.io/teams/galoy/dataset_instructions) and run:
```bash
$ export HONEYCOMBIO_APIKEY=<API_KEY>
$ tofu apply
```
</details>

Estimated time: ~ `5 mins`.

#### 2. Platform

This phase deploys the Kubernetes Master, Nodes and the PostgreSQL Server via Cloud SQL.

<details>
<summary>Are you deploying Shared PG?</summary>
<br />
If yes, don't forget to add the output blocks to `gcp/{env}/platform/main.tf` and fetch the shared pg password
to provision them on `galoy-org-infra` for this environment.
</details>

Estimated time ~ `25 mins`.

#### 3. Workers

This phase deploy the Concourse Workers that are needed for the main Concourse Server to deploy our applications inside the GKE Kubernetes Cluster.

Estimated time ~ `4 mins`.

#### 4. Services

**Prerequisite:**

Run the following locally before unpausing Services:

```bash
$ export BASTION_USER="${username}_galoy_io"
$ bin/sync-to-bastion.sh ${env}
$ bin/ssh-to-bastion.sh ${env}
$ gcloud auth login
$ gcloud auth application-default login
$ kauth
$ cd blink-deployments/gcp/${env}/services
$ tf init
$ tf apply -target module.services.helm_release.cert_manager
```

Then, unpause the services job.

Deploys the ingress and cert manager services.

Estimated time ~ `5 mins`.

#### 5. Bitcoin

Unpause the Bitcoin job. This is going to fail but don't worry, the Bitcoind is chain syncing after this job is complete.

<details>
<summary>Bitcoind preparation on galoy-org to copy the data from </summary>
<br />

Stop bitcoind
* Edit the stateful set On the galoy-org instance (needs to be run by someone listed [here](https://github.com/GaloyMoney/galoy-org-infra/blob/main/inception/main.tf#L16))
  ```
  k -n bitcoin-mainnet edit sts bitcoind
  ```

* to pause the container before bitcoind starts paste the lines starting with `- command:` before the `- image` as shown:
  ```
      spec:
        containers:
        - command:
          - sh
          - -c
          - |
            echo "sleeping a day ..."
            sleep 86400
          image: lncm/bitcoind:v22.0
          imagePullPolicy: IfNotPresent
          name: bitcoind
          ports:
          - containerPort: 8332
  ```
* also remove the livenessProbe and the readinessProbe (the lines to be removes are commented):
  ```
        #  livenessProbe:
        #    exec:
        #      command:
        #      - bitcoin-cli
        #      - -conf=/data/.bitcoin/bitcoin.conf
        #      - getblockchaininfo
        #    failureThreshold: 3
        #    initialDelaySeconds: 120
        #    periodSeconds: 30
        #    successThreshold: 1
        #    timeoutSeconds: 1

        #  readinessProbe:
        #    exec:
        #      command:
        #      - bitcoin-cli
        #      - -conf=/data/.bitcoin/bitcoin.conf
        #      - getblockchaininfo
        #    failureThreshold: 3
        #    periodSeconds: 4
        #    successThreshold: 3
        #    timeoutSeconds: 1
  ```

Delete the chainstate directory and rsync from the SidecarContainer
* See these intructions in the logs with:
    ```
    $ k -n bitcoind-mainnet logs bitcoind-0 -c chain-syncer
    ```

1. connect to the sidecarContainer
    ```
    $ k -n bitcoind-mainnet exec -it bitcoind-0 -c chain-syncer -- /bin/sh
    ```
2. delete the old chainstate from the storage bucket
    ```
    $ gsutil -m rm -r gs://bitcoin-blockchain-state/mainnet/chainstate/
    ```
3. copy the blocks, chainstate and indexes (optional) to the storage bucket
    ```
    $ gsutil -m rsync -r /data/.bitcoin/blocks gs://bitcoin-blockchain-state/mainnet/blocks \
     && gsutil -m rsync -r /data/.bitcoin/chainstate gs://bitcoin-blockchain-state/mainnet/chainstate \
     && gsutil -m rsync -r /data/.bitcoin/indexes gs://bitcoin-blockchain-state/mainnet/indexes
    ```
</details>


On the first start of the new instance the `initial-sync` init container will copy the blocks and chainstate from the google storage bucket.
* monitor the copy process:
  ```
  k -n galoy-inc-bitcoin logs bitcoind-0 -c initial-sync -f
  ```

Once the copy has finished bitcoind will start to sync from the blockheight left in the storage bucket.
Monitor the bitcoind syncing process:
* with kubectl
  ```
  $ k -n galoy-inc-bitcoin logs bitcoind-0 -c bitcoind -f | grep "progress="
  ```
* or with this script:
  ```bash
  $ cat <<EOF >script.sh
  #!/bin/bash

  done=$(kubectl -n {env}-bitcoin exec bitcoind-0 -c bitcoind -- bitcoin-cli getblockcount)
  full=$(wget -q -O- https://blockchain.info/q/getblockcount; echo)

  echo "scale=4 ; $done / $full * 100" | bc
  EOF

  $ chmod +x script.sh
  $ watch -n 60 ./script.sh
  ```

Once Bitcoind has synced 100%, `LND` pods should come up, which you can test using:

```bash
$ k -n {env}-bitcoin get pods
```

Create the LND wallets
* get the lnd1pass on the bastion - use it as the wallet password
  ```
  $ k -n ${env}-bitcoin get secret lnd1-pass -o jsonpath='{.data.password}' | base64 -d
  ```
* create a passphrase (aka Cipher Seed Password) in your local terminal
  ```
  $ diceware -n 12 -d' ' --no-caps
  ```
* connect to the lnd pod
  ```
  $ k -n ${env}-bitcoin exec -it lnd1-0 -c lnd -- /bin/sh
  ```
* create the wallet
  ```
  $ lncli create
  ```
* repeat the process for lnd2
</details>

If the smoketests failed, make sure you have provisioned `smoketest_kubeconfig` fetched from the services phase on galoy-org. (Get output from services, and put it into the secrets directory in teh galoy-org bastion and run tf apply.)

Estimated time ~ `10 mins` for job to fail, ~ `2d` to `1w` for the Bitcoin Sync to complete depending on the power of the Kubernetes Nodes. Once it completes, rerun the job.
S
#### 6. Galoy

This phase deploys the galoy backend, with price, trigger and mongodb pods.

Unpause the galoy job and wait for it to deploy. It should pass but if it doesn't make sure to check the logs. If backup fails, make sure the `backups_sa` keys are provisioned.

* pass the `shared_pg_admin_password` with [galoy-org/pipeline-creds](https://github.com/GaloyMoney/galoy-org-infra/blob/main/pipeline-creds)

Estimated time ~ `8 mins`.

#### 7. Monitoring

This phase deploys prometheus, grafana and honeycomb otel stack.

For grafana:
* an oath app for grafana - create in google console
* a key for the grafana_sa - create in google console

Unpause the monitoring job and wait for it to deploy.

<details>
<summary> Debug Honeycomb </summary>
<br />
Honeycomb doesn't have the references being provisioned in the queries.

* set the `HONEYCOMB_API_KEY` and `HONEYCOMB_DATASET`
  ```
  export HONEYCOMB_API_KEY=$(k -n ${env}-monitoring get secret honeycomb-creds -o jsonpath='{.data.api-key}' | base64 -d)
  export HONEYCOMB_DATASET=${env}
  ```
* [run the galoy integration tests](https://github.com/GaloyMoney/galoy/blob/main/DEV.md) locally to create a dataset.
  * The Honeycomb variables can be pasted also in the `galoy/.env`.
  ```
  HONEYCOMB_API_KEY=${HONEYCOMB_API_KEY}
  HONEYCOMB_DATASET=${env}
  ```

* to solve the errors in the CI like:
  ```
  Error: 422 Unprocessable Entity: unknown column or derived column 'graphql.error.type'
  Error: 422 Unprocessable Entity: unknown column or derived column 'grpc.error_message'
  ```
* [use Honeytail](https://github.com/honeycombio/honeytail/tree/main/example) to create the missing keys

  ```
  # install go <https://go.dev/doc/install>
  export PATH=$PATH:~/go/bin/
  go install github.com/honeycombio/honeytail@latest
  export PATH=$PATH:/usr/local/go/bin
  ```
* create a `missing-keys.log `
  ```
  cat <<EOF > ./missing-keys.log
  time="2022-08-15T18:16:04Z" level=debug grpc.error_message="added with honeytail"
  time="2022-08-15T18:16:05Z" level=debug graphql.error.type="added with honeytail"
  time="2022-08-15T18:16:06Z" level=debug graphql.error.original.type="added with honeytail"
  time="2022-08-15T18:16:07Z" level=debug graphql.error.path="added with honeytail"
  time="2022-08-15T18:16:08Z" level=debug graphql.error.original.message="added with honeytail"
  EOF
  ```
* run honeytail
  ```
  export HONEYCOMB_API_KEY=${HONEYCOMB_API_KEY}
  export HONEYCOMB_DATASET=${env}
  honeytail --debug \
      --writekey=$HONEYCOMB_API_KEY \
      --dataset=$HONEYCOMB_DATASET \
      --parser=keyval \
      --keyval.timefield=time \
      --file=./missing-keys.log \
      --backfill
  ```
* Will also need to create a trigger manually in honeycomb and add the pagerduty recipient to activate pagerduty.
</details>

Estimated time ~ `20 mins`.

#### 8. Addons

This phase deploys web wallet, galoy pay, admin panel, dealer and data exploration charts.
It needs monitoring because grafana dashboards can be provisioned here.

Estimated time ~ `8 mins`.

#### 10. Domains

Get the `EXTERNAL-IP`  of the cluster with:
```
$ k -n ${env}-ingress get svc ingress-nginx-controller
```
A-records and redirections:
* API
  ```
  ${root_domain}
  ```
* Web wallet
  ```
  wallet.${root_domain}
  ```
* LN node info and lightning address domain
  ```
  pay.${root_domain}
  lnd1.${root_domain}
  lnd2.${root_domain}
  ${lightning_address_domain}
  ${lightning_address_domain_aliases}
  ```
* grafana
  ```
  grafana.${root_domain}
  ```

* Specter
  ```
  specter.${root_domain}
  ```

#### 11. Specter
Configure Specter with a login password and the Bitcoin Core credentials.

* get the autogenerated `specter-password`:
  ```
  $ k -n ${env}-bitcoin get secrets specter-password -o jsonpath='{.data.password}' | base64 -d
  ```
* get the `bitcoind-rpcpassword`:
  ```
  $ k -n ${env}-bitcoin get secrets bitcoind-rpcpassword -o jsonpath='{.data.password}' | base64 -d
  ```

Proceed to the Specter WebUI (on specter.${rootdomain}) - Settings - Authentication
* Set Password Protection with the `specter-password`

* Configure Node:
  * Auto-detect: `false`
  * Username: `rpcuser`
  * Password: `bitcoind-rpcpassword`
  * Host: `http://bitcoind`
  * Port: 8332


<details>
<summary> Alternatively Configure Specter with it's config.json </summary>
<br />
See: <https://github.com/cryptoadvance/specter-desktop/blob/f739d9bb7acf9484ed9f0c044737cb4f63b6807d/docs/faq.md#i-forgot-my-password-how-can-i-reset-it>

* connect to the `specter-0` pod
  ```
  $ k -n ${env}-bitcoin exec -it specter-0 -- /bin/sh
  ```

* Paste the following block to the command line in the pod (extracts the torrc_passoword from the file and BTC_RPC_PASSWORD from the environment variables):
```
torrc_password=$(cat /data/config.json | grep torrc | cut -d '"' -f4)
cat <<EOF > /data/config.json
{
    "auth": {
        "method": "none",
        "password_min_chars": 6,
        "rate_limit": 10,
        "registration_link_timeout": 1
    },
    "explorers": {
        "main": "",
        "test": "",
        "regtest": "",
        "signet": ""
    },
    "explorer_id": {
        "main": "CUSTOM",
        "test": "CUSTOM",
        "regtest": "CUSTOM",
        "signet": "CUSTOM"
    },
    "asset_labels": {
        "liquidv1": {}
    },
    "active_node_alias": "default",
    "proxy_url": "socks5h://localhost:9050",
    "only_tor": false,
    "tor_control_port": "",
    "tor_status": false,
    "hwi_bridge_url": "/hwi/api/",
    "uid": "",
    "unit": "btc",
    "price_check": false,
    "alt_rate": 1,
    "alt_symbol": "BTC",
    "price_provider": "",
    "weight_unit": "oz",
    "validate_merkle_proofs": false,
    "fee_estimator": "bitcoin_core",
    "fee_estimator_custom_url": "",
    "hide_sensitive_info": true,
    "autohide_sensitive_info_timeout_minutes": 20,
    "autologout_timeout_hours": 4,
    "bitcoind": false,
    "torrc_password": "${torrc_password}",
    "auth": {
        "method": "passwordonly",
        "password_min_chars": 6,
        "rate_limit": "10",
        "registration_link_timeout": "1"
    },
    "internal_node": {
        "autodetect": false,
        "datadir": "/data/.bitcoin",
        "user": "rpcuser",
        "password": "${BTC_RPC_PASSWORD}",
        "host": "bitcoind",
        "protocol": "http",
        "port": 8332
    }
}
EOF
```

* restart the `specter-0` pod:
  ```
  k -n ${env}-bitcoin delete pod specter-0
  ```

* Open the Specter WebUI (on specter.${rootdomain})
* login with the default password: `admin`
* Open Settings - Authentication
* Set Password Protection to the `specter-password`
</details>
