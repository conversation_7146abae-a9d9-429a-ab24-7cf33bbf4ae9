apiVersion: vendir.k14s.io/v1alpha1
directories:
- contents:
  - inline: {}
    path: git-ref
  - git:
      commitTitle: 'chore: bump kubernetes to ''1.30.9-gke.1231000'''
      sha: 680891fa2e20af364f0ddc7a032401307f899338
      tags:
      - v0.12.6-28-g680891f
    path: tf
  path: modules/infra/vendor
- contents:
  - git:
      commitTitle: 'chore: update bitcoind to v27.0 (#6679)'
      sha: 29ea9caf11baec03422f718f98392fa8c2d5d43e
      tags:
      - galoy-v0.33.122-4-g29ea9caf
    path: chart
  - git:
      commitTitle: 'chore: update bitcoind to v27.0 (#6679)'
      sha: 29ea9caf11baec03422f718f98392fa8c2d5d43e
      tags:
      - galoy-v0.33.122-4-g29ea9caf
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/bitcoin/vendor/bitcoind
- contents:
  - git:
      commitTitle: 'chore(deps): bump lnd-sidecar image to ''sha256:e0ccf6d340a99cecc39c4d7ba52014c767220069b5b1b314aceb34253c4fa79f''
        (#7487)'
      sha: 7bddd07ae59eef508aa220621de60d4b18bb9666
      tags:
      - galoy-v0.34.7-45-g7bddd07a
    path: chart
  - git:
      commitTitle: 'chore(deps): bump lnd-sidecar image to ''sha256:e0ccf6d340a99cecc39c4d7ba52014c767220069b5b1b314aceb34253c4fa79f''
        (#7487)'
      sha: 7bddd07ae59eef508aa220621de60d4b18bb9666
      tags:
      - galoy-v0.34.7-45-g7bddd07a
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/bitcoin/vendor/lnd1
- contents:
  - git:
      commitTitle: 'chore(deps): bump lnd-sidecar image to ''sha256:e0ccf6d340a99cecc39c4d7ba52014c767220069b5b1b314aceb34253c4fa79f''
        (#7487)'
      sha: 7bddd07ae59eef508aa220621de60d4b18bb9666
      tags:
      - galoy-v0.34.7-45-g7bddd07a
    path: chart
  - git:
      commitTitle: 'chore(deps): bump lnd-sidecar image to ''sha256:e0ccf6d340a99cecc39c4d7ba52014c767220069b5b1b314aceb34253c4fa79f''
        (#7487)'
      sha: 7bddd07ae59eef508aa220621de60d4b18bb9666
      tags:
      - galoy-v0.34.7-45-g7bddd07a
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/bitcoin/vendor/lnd2
- contents:
  - git:
      commitTitle: 'chore: cleanup network policy labels (#3193)'
      sha: bac4373d48116492846aa306818524b8f1d0cdc4
      tags:
      - bria-v0.2.9-5-gbac4373d
    path: chart
  - git:
      commitTitle: 'chore: cleanup network policy labels (#3193)'
      sha: bac4373d48116492846aa306818524b8f1d0cdc4
      tags:
      - bria-v0.2.9-5-gbac4373d
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/bitcoin/vendor/specter
- contents:
  - git:
      commitTitle: 'chore(bria): bump bria image to ''sha256:3c2226df0fc242a4fd7e1767a5c9eb9ea7f55dbf2d4271ea05a42bf0d99a6f05''
        (#7820)...'
      sha: af48974418b8dcec0fc4f362df6efa29b4b6afb8
      tags:
      - galoy-v0.34.7-106-gaf489744
    path: chart
  - git:
      commitTitle: 'chore(bria): bump bria image to ''sha256:3c2226df0fc242a4fd7e1767a5c9eb9ea7f55dbf2d4271ea05a42bf0d99a6f05''
        (#7820)...'
      sha: af48974418b8dcec0fc4f362df6efa29b4b6afb8
      tags:
      - galoy-v0.34.7-106-gaf489744
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/bitcoin/vendor/bria
- contents:
  - git:
      commitTitle: 'chore: enable admin in fulcrum chart + update termination grace
        period (#7437)'
      sha: 6c6975563eb51bc14c3b5b547b9747446feb7cd2
      tags:
      - galoy-v0.34.7-37-g6c697556
    path: chart
  - git:
      commitTitle: 'chore: enable admin in fulcrum chart + update termination grace
        period (#7437)'
      sha: 6c6975563eb51bc14c3b5b547b9747446feb7cd2
      tags:
      - galoy-v0.34.7-37-g6c697556
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/bitcoin/vendor/fulcrum
- contents:
  - git:
      commitTitle: 'fix: revert the jwk token issuer used in testflight (#7822)'
      sha: eaee8fa5614b34403dbb3ba1fd4a9eff1aefb9d4
      tags:
      - galoy-v0.34.7-108-geaee8fa5
    path: chart
  - git:
      commitTitle: 'fix: revert the jwk token issuer used in testflight (#7822)'
      sha: eaee8fa5614b34403dbb3ba1fd4a9eff1aefb9d4
      tags:
      - galoy-v0.34.7-108-geaee8fa5
    path: ci
  - inline: {}
    path: git-ref
  path: modules/galoy/vendor/galoy
- contents:
  - git:
      commitTitle: 'chore(deps): update grafana helm chart in monitoring (#7732)'
      sha: 87ae0b0d962805a0597d44459d8e2c0d7254631b
      tags:
      - galoy-v0.34.7-96-g87ae0b0d
    path: chart
  - git:
      commitTitle: 'chore(deps): update grafana helm chart in monitoring (#7732)'
      sha: 87ae0b0d962805a0597d44459d8e2c0d7254631b
      tags:
      - galoy-v0.34.7-96-g87ae0b0d
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/monitoring/vendor/monitoring
- contents:
  - git:
      commitTitle: 'chore: changes to blink.sv domain (#7821)'
      sha: 2f29c0438445c32f2177305b6960df56a9ab8656
      tags:
      - galoy-v0.34.7-107-g2f29c043
    path: chart
  - git:
      commitTitle: 'chore: changes to blink.sv domain (#7821)'
      sha: 2f29c0438445c32f2177305b6960df56a9ab8656
      tags:
      - galoy-v0.34.7-107-g2f29c043
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/addons/vendor/admin-panel
- contents:
  - git:
      commitTitle: 'chore: fix metadata to fix open-charts-pr.sh script (#7746)...'
      sha: 06221a57a68708c983d2c6cdafd1e402a8474837
      tags:
      - galoy-v0.34.7-98-g06221a57
    path: chart
  - git:
      commitTitle: 'chore: fix metadata to fix open-charts-pr.sh script (#7746)...'
      sha: 06221a57a68708c983d2c6cdafd1e402a8474837
      tags:
      - galoy-v0.34.7-98-g06221a57
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/addons/vendor/galoy-pay
- contents:
  - git:
      commitTitle: 'chore: changes to blink.sv domain (#7821)'
      sha: 2f29c0438445c32f2177305b6960df56a9ab8656
      tags:
      - galoy-v0.34.7-107-g2f29c043
    path: chart
  - git:
      commitTitle: 'chore: changes to blink.sv domain (#7821)'
      sha: 2f29c0438445c32f2177305b6960df56a9ab8656
      tags:
      - galoy-v0.34.7-107-g2f29c043
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/stablesats/vendor/stablesats
- contents:
  - git:
      commitTitle: 'chore: fix metadata to fix open-charts-pr.sh script (#7746)...'
      sha: 06221a57a68708c983d2c6cdafd1e402a8474837
      tags:
      - galoy-v0.34.7-98-g06221a57
    path: chart
  - git:
      commitTitle: 'chore: fix metadata to fix open-charts-pr.sh script (#7746)...'
      sha: 06221a57a68708c983d2c6cdafd1e402a8474837
      tags:
      - galoy-v0.34.7-98-g06221a57
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/addons/vendor/api-dashboard
- contents:
  - git:
      commitTitle: 'chore: add k8s_events incl access rights to otel config (#7714)...'
      sha: 4c818bc401bfe7b26aaeef51090da6323579e535
      tags:
      - galoy-v0.34.7-92-g4c818bc4
    path: chart
  - git:
      commitTitle: 'chore: add k8s_events incl access rights to otel config (#7714)...'
      sha: 4c818bc401bfe7b26aaeef51090da6323579e535
      tags:
      - galoy-v0.34.7-92-g4c818bc4
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/galoy-deps/vendor/galoy-deps
- contents:
  - git:
      commitTitle: 'Fix the UTO upgrade issue reported in #9470. (#9474)...'
      sha: ef60183b123245490900dd103a0cf2e15a4f5d3e
      tags:
      - 0.39.0
    path: crds
  path: modules/services/galoy-deps/vendor/strimzi-kafka-operator
- contents:
  - git:
      commitTitle: 'chore(deps): bump kafka-connect image to ''sha256:bd3ab2452b33b02514f7f02fc250edc9e94f4f3d23c26a48dc6ab8d0c61b6191''
        (#4297)'
      sha: da666b837cceb71e299ff19bd309b5d22af020d3
      tags:
      - galoy-v0.21.33-3-gda666b83
    path: chart
  - git:
      commitTitle: 'chore(deps): bump kafka-connect image to ''sha256:bd3ab2452b33b02514f7f02fc250edc9e94f4f3d23c26a48dc6ab8d0c61b6191''
        (#4297)'
      sha: da666b837cceb71e299ff19bd309b5d22af020d3
      tags:
      - galoy-v0.21.33-3-gda666b83
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/monitoring/vendor/kafka-connect
- contents:
  - git:
      commitTitle: 'chore: more galoy to blink changes (#713)...'
      sha: 27920b700341087dc1ea9ebef59d786812c95708
    path: chart
  - git:
      commitTitle: 'chore: more galoy to blink changes (#713)...'
      sha: 27920b700341087dc1ea9ebef59d786812c95708
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/blink-addons/vendor/circles
- contents:
  - git:
      commitTitle: chore-bump-blink-fiat-image-1200cc5 (#718)...
      sha: a4bdf436fac8498280369be2b4bdecf51a38c3f3
    path: chart
  - git:
      commitTitle: chore-bump-blink-fiat-image-1200cc5 (#718)...
      sha: a4bdf436fac8498280369be2b4bdecf51a38c3f3
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/blink-addons/vendor/blink-fiat
- contents:
  - git:
      commitTitle: 'chore: more galoy to blink changes (#713)...'
      sha: 27920b700341087dc1ea9ebef59d786812c95708
    path: chart
  - git:
      commitTitle: 'chore: more galoy to blink changes (#713)...'
      sha: 27920b700341087dc1ea9ebef59d786812c95708
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/blink-addons/vendor/blink-kyc
- contents:
  - git:
      commitTitle: 'chore: fix metadata to fix open-charts-pr.sh script (#7746)...'
      sha: 06221a57a68708c983d2c6cdafd1e402a8474837
      tags:
      - galoy-v0.34.7-98-g06221a57
    path: chart
  - git:
      commitTitle: 'chore: fix metadata to fix open-charts-pr.sh script (#7746)...'
      sha: 06221a57a68708c983d2c6cdafd1e402a8474837
      tags:
      - galoy-v0.34.7-98-g06221a57
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/addons/vendor/map
- contents:
  - git:
      commitTitle: 'chore: fix metadata to fix open-charts-pr.sh script (#7746)...'
      sha: 06221a57a68708c983d2c6cdafd1e402a8474837
      tags:
      - galoy-v0.34.7-98-g06221a57
    path: chart
  - git:
      commitTitle: 'chore: fix metadata to fix open-charts-pr.sh script (#7746)...'
      sha: 06221a57a68708c983d2c6cdafd1e402a8474837
      tags:
      - galoy-v0.34.7-98-g06221a57
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/addons/vendor/voucher
- contents:
  - git:
      commitTitle: 'chore(deps): update postgresql helm chart in cala (#7116)'
      sha: 5fd70375531a619f12d8ebdd6736edd310c1d364
      tags:
      - galoy-v0.33.124-5-g5fd70375
    path: chart
  - git:
      commitTitle: 'chore(deps): update postgresql helm chart in cala (#7116)'
      sha: 5fd70375531a619f12d8ebdd6736edd310c1d364
      tags:
      - galoy-v0.33.124-5-g5fd70375
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/cala/vendor/cala
- contents:
  - git:
      commitTitle: 'chore(deps): update postgresql helm chart in cala-enterprise (#558)'
      sha: 7c01e1b47f6d50d80c348cb025cd2b6caa1f454f
    path: chart
  - git:
      commitTitle: 'chore(deps): update postgresql helm chart in cala-enterprise (#558)'
      sha: 7c01e1b47f6d50d80c348cb025cd2b6caa1f454f
    path: ci
  - inline: {}
    path: git-ref
  path: modules/services/cala/vendor/cala-enterprise
kind: LockConfig
