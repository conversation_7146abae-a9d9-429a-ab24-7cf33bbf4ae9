#@ load("@ytt:data", "data")

#@ def pipeline_image():
#@   return data.values.docker_registry + "/blink-deployments-pipeline"
#@ end

#@ def task_image_config():
type: registry-image
source:
  username: #@ data.values.docker_registry_user
  password: #@ data.values.docker_registry_password
  repository: #@ pipeline_image()
#@ end

#@ def cepler_config(deployment):
#@   return "cepler/" + deployment + ".yml"
#@ end

#@ def gates_file(deployment):
#@   return "cepler-gates/" + deployment + ".yml"
#@ end

#@ def cepler_resource_name(deployment, env):
#@   return env + "-" + deployment
#@ end

#@ def cepler_out_name(deployment):
#@   return deployment + "-out"
#@ end

#@ def put_honeymarker(env):
in_parallel:
- put: honeymarker
  params:
    dataset: #@ env
#@ end

#@ def cepler_in(deployment, env):
name: #@ cepler_resource_name(deployment, env)
type: cepler-in
source:
  uri: #@ data.values.git_uri
  branch: #@ data.values.git_branch
  private_key: #@ data.values.github_private_key
  environment: #@ "gcp-" + env
  config: #@ cepler_config(deployment)
  gates_branch: #@ data.values.gates_branch
  gates_file: #@ gates_file(deployment)
#@ end

#@ def cepler_out(deployment):
name: #@ cepler_out_name(deployment)
type: cepler-out
source:
  uri: #@ data.values.git_uri
  branch: #@ data.values.git_branch
  private_key: #@ data.values.github_private_key
  config: #@ cepler_config(deployment)
  gates_branch: #@ data.values.gates_branch
  gates_file: #@ gates_file(deployment)
#@ end

#@ def tf_resource_name(deployment, env):
#@    return "tf-" + env + "-" + deployment
#@ end

#@ def tf_resource(deployment, env, prefix_path = ""):
#@ prefix = env + "/" + deployment
#@ if prefix_path != "":
#@   prefix = env + "/" + prefix_path
#@ end
name: #@ tf_resource_name(deployment, env)
type: terraform
source:
  env_name: default
  backend_type: gcs
  backend_config:
    bucket: #@ env + "-tf-state"
    prefix: #@ prefix
    credentials: #@ env_value(env, "creds")
  env:
    GOOGLE_CREDENTIALS: #@ env_value(env, "creds")
#@ end

#@ def chart_smoke_test(chart, deployment, env):
#@ base_path = "pipeline-tasks/modules/services/" + deployment
#@ if chart == "galoy":
#@   base_path = "pipeline-tasks/modules/" + deployment
#@ end
do:
- task: get-smoketest-settings
  tags:
  - #@ env
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: #@ cepler_resource_name(deployment, env)
      path: pipeline-tasks
    outputs:
    - name: #@ chart + "-smoketest-settings"
    params:
      SMOKETEST_SECRET: #@ chart + "-smoketest"
      SMOKETEST_KUBECONFIG: #@ env_value(env, "smoketest_kubeconfig")
      OUT: #@ chart + "-smoketest-settings"
    run:
      path: #@ base_path + "/vendor/" + chart + "/ci/tasks/get-smoketest-settings.sh"
- task: #@ chart + "-smoketest"
  tags:
  - #@ env
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: #@ cepler_resource_name(deployment, env)
      path: pipeline-tasks
    - name: #@ chart + "-smoketest-settings"
      path: smoketest-settings
    run:
  #@ if chart.startswith("lnd"):
      path: #@ "pipeline-tasks/modules/services/bitcoin/vendor/" + chart + "/ci/tasks/lnd-smoketest.sh"
  #@ else:
      path: #@ base_path + "/vendor/" + chart + "/ci/tasks/" + chart + "-smoketest.sh"
  #@ end
#@ end

#@ def slack_failure_notification(deployment):
#@ fail_url = "<$ATC_EXTERNAL_URL/teams/$BUILD_TEAM_NAME/pipelines/$BUILD_PIPELINE_NAME/jobs/$BUILD_JOB_NAME/builds/$BUILD_NAME| :face_with_symbols_on_mouth: $BUILD_JOB_NAME> failed!"
put: #@ deployment + "-slack"
params:
  username: #@ data.values.slack_username
  icon_url: https://cl.ly/2F421Y300u07/concourse-logo-blue-transparent.png
  text:    #@ fail_url
#@ end

#@ def slack_success_notification(deployment):
#@ success_url = "<$ATC_EXTERNAL_URL/teams/$BUILD_TEAM_NAME/pipelines/$BUILD_PIPELINE_NAME/jobs/$BUILD_JOB_NAME/builds/$BUILD_NAME| :zap: $BUILD_JOB_NAME> completed!"
put: #@ deployment + "-slack"
params:
  username: #@ data.values.slack_username
  icon_url: https://cl.ly/2F421Y300u07/concourse-logo-blue-transparent.png
  text:    #@ success_url
#@ end

#@ def slack_resource(deployment):
name: #@ deployment + "-slack"
type: slack-notification
source:
  url: #@ getattr(data.values, deployment + "_slack_webhook_url")
#@ end

#@ def env_value(env, value):
#@   return getattr(data.values, env.replace("-", "_") + "_" + value)
#@ end

---

#@ def resource_types():
- name: cepler-in
  type: registry-image
  source:
    repository: cepler/cepler-concourse-resource
    tag: latest

- name: cepler-out
  type: registry-image
  source:
    repository: cepler/cepler-concourse-resource
    tag: latest

- name: terraform
  type: docker-image
  source:
    repository: ljfranklin/terraform-resource
    tag: latest

- name: honeymarker
  type: docker-image
  source:
    repository: #@ data.values.docker_registry + "/honeymarker-resource"
    tag: edge

- name: slack-notification
  type: docker-image
  source:
    repository: cfcommunity/slack-notification-resource

- name: k8s-resource
  type: docker-image
  source:
    repository: jgriff/k8s-resource
#@ end
