#@ load("@ytt:data", "data")
#@ load("shared.lib.yml",
#@   "pipeline_image",
#@   "task_image_config",
#@   "tf_resource_name",
#@   "put_honeymarker",
#@   "cepler_resource_name",
#@   "cepler_out_name",
#@   "chart_smoke_test",
#@   "env_value")

#@ def addons_job(env, addon_modules = ["admin-panel", "galoy-pay", "map", "voucher"]):
name: #@ env + "-addons"
serial: true
plan:
- in_parallel:
  - get: #@ cepler_resource_name("addons", env)
    trigger: true
  - get: pipeline-tasks
- in_parallel:
  - #@ put_honeymarker(env)
  - do:
    - put: #@ tf_resource_name("addons", env)
      tags:
      - #@ env
      params:
        terraform_source: #@ cepler_resource_name("addons", env) + "/gcp/" + env + "/addons"
        vars:
          secrets: #@ env_value(env, "addons_secrets")
    - in_parallel:
    #@ for module in addon_modules:
      - #@ chart_smoke_test(module, "addons", env)
    #@ end
    - put: #@ cepler_out_name("addons")
      params:
        repository: #@ cepler_resource_name("addons", env)
        environment: #@ "gcp-" + env
#@ end
