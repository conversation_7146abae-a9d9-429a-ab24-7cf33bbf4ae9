#@ load("@ytt:data", "data")
#@ load("shared.lib.yml",
#@   "pipeline_image",
#@   "task_image_config",
#@   "tf_resource_name",
#@   "put_honeymarker",
#@   "cepler_resource_name",
#@   "cepler_out_name",
#@   "chart_smoke_test",
#@   "env_value")

#@ def blink_addons_job(env, addon_modules = ["circles", "blink-fiat"]):
name: #@ env + "-blink-addons"
serial: true
plan:
- in_parallel:
  - get: #@ cepler_resource_name("blink-addons", env)
    trigger: true
  - get: pipeline-tasks
- in_parallel:
  - #@ put_honeymarker(env)
  - do:
    - put: #@ tf_resource_name("blink-addons", env)
      tags:
      - #@ env
      params:
        terraform_source: #@ cepler_resource_name("blink-addons", env) + "/gcp/" + env + "/blink-addons"
        vars:
          secrets: #@ env_value(env, "blink_addons_secrets")
    - in_parallel:
    #@ for module in addon_modules:
      - #@ chart_smoke_test(module, "blink-addons", env)
    #@ end
    - put: #@ cepler_out_name("blink-addons")
      params:
        repository: #@ cepler_resource_name("blink-addons", env)
        environment: #@ "gcp-" + env
#@ end
