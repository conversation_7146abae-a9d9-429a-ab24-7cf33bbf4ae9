#@ load("@ytt:data", "data")

#@ load("shared.lib.yml",
#@   "pipeline_image",
#@   "task_image_config",
#@   "tf_resource_name",
#@   "put_honeymarker",
#@   "cepler_resource_name",
#@   "cepler_out_name",
#@   "chart_smoke_test",
#@   "env_value")

#@ def galoy_job(env, additional_tf_args = {}):
name: #@ env + "-galoy"
serial: true
plan:
- in_parallel:
  - get: #@ cepler_resource_name("galoy", env)
    trigger: true
  - get: pipeline-tasks
  - get: #@ env + "-lnd-creds"
    tags: #@ [env]
    trigger: true
    params:
      sensitive: true
- in_parallel:
  - #@ put_honeymarker(env)
  - put: #@ tf_resource_name("galoy", env)
    tags:
    - #@ env
    params:
      terraform_source: #@ cepler_resource_name("galoy", env) + "/gcp/" + env + "/galoy"
      #@ secrets = {"secrets": env_value(env, "galoy_secrets")}
      #@ secrets.update(additional_tf_args)
      vars: #@ secrets
- #@ chart_smoke_test("galoy", "galoy", env)
- put: #@ cepler_out_name("galoy")
  params:
    repository: #@ cepler_resource_name("galoy", env)
    environment: #@ "gcp-" + env
#@ end

#@ def lnd_creds_k8s_secret(env):
name: #@ env + "-lnd-creds"
type: k8s-resource
tags: #@ [env]
icon: kubernetes
source:
  namespace: #@ env + "-bitcoin"
  resource_types: secrets
  filter:
    name: "lnd[1,2]-credentials"
  url: https://kubernetes.default.svc:443
  token: #@ env_value(env, "k8s_secret_reader_token")
  certificate_authority: #@ env_value(env, "k8s_ca_cert")
#@ end
