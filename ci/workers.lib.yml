#@ load("@ytt:data", "data")

#@ load("shared.lib.yml",
#@   "task_image_config",
#@   "cepler_out_name",
#@   "cepler_resource_name",
#@   "env_value")

#@ def workers_job(env):
name: #@ env + "-workers"
serial_groups:
- #@ env
plan:
- get: #@ cepler_resource_name("workers", env)
  trigger: true
- task: workers
  config:
    platform: linux
    image_resource: #@ task_image_config()
    inputs:
    - name: #@ cepler_resource_name("workers", env)
      path: repo
    params:
      ENVIRONMENT: #@ env
      GOOGLE_CREDENTIALS: #@ env_value(env, "creds")
      SSH_PRIVATE_KEY: #@ env_value(env, "ssh_private_key")
      SSH_PUB_KEY: #@ env_value(env, "ssh_pub_key")
      BASTION_ZONE: #@ env_value(env, "bastion_zone")
      WORKER_KEY: #@ data.values.worker_key
      WORKER_KEY_PUB: #@ data.values.worker_key_pub
      HOST_KEY_PUB: #@ data.values.host_key_pub
    run:
      path: repo/ci/tasks/workers.sh
- put: #@ cepler_out_name("workers")
  params:
    repository: #@ cepler_resource_name("workers", env)
    environment: #@ "gcp-" + env
#@ end
