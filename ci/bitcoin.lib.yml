#@ load("@ytt:data", "data")
#@ load("shared.lib.yml",
#@   "pipeline_image",
#@   "task_image_config",
#@   "put_honeymarker",
#@   "tf_resource_name",
#@   "cepler_resource_name",
#@   "cepler_out_name",
#@   "chart_smoke_test",
#@   "env_value")

#@ def bitcoin_job(env):
name: #@ env + "-bitcoin"
serial: true
plan:
- in_parallel:
  - #@ put_honeymarker(env)
  - do:
    - get: #@ cepler_resource_name("bitcoin", env)
      trigger: true
    - put: #@ tf_resource_name("bitcoin", env)
      tags:
      - #@ env
      params:
        terraform_source: #@ cepler_resource_name("bitcoin", env) + "/gcp/" + env + "/bitcoin"
    #@ if env == "galoy-bbw" or env == "galoy-staging":
        vars:
          lnd1_pass: #@ env_value(env, "lnd1_pass")
          secrets: #@ env_value(env, "bitcoin_secrets")
    #@ else:
        vars:
          secrets: #@ env_value(env, "bitcoin_secrets")
    #@ end
    - in_parallel:
    #@ for chart in ["bitcoind", "specter", "lnd1", "lnd2", "fulcrum"]:
      - #@ chart_smoke_test(chart, "bitcoin", env)
    #@ end
    - put: #@ cepler_out_name("bitcoin")
      params:
        repository: #@ cepler_resource_name("bitcoin", env)
        environment: #@ "gcp-" + env
#@ end
