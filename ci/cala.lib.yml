#@ load("@ytt:data", "data")

#@ load("shared.lib.yml",
#@   "tf_resource_name",
#@   "put_honeymarker",
#@   "cepler_resource_name",
#@   "cepler_out_name",
#@   "chart_smoke_test",
#@   "env_value")

#@ def cala_job(env):
name: #@ env + "-cala"
serial: true
plan:
- get: #@ cepler_resource_name("cala", env)
  trigger: true
- in_parallel:
  - #@ put_honeymarker(env)
  - do:
    - put: #@ tf_resource_name("cala", env)
      tags:
      - #@ env
      params:
        terraform_source: #@ cepler_resource_name("cala", env) + "/gcp/" + env + "/cala"
    - #@ chart_smoke_test("cala", "cala", env)
    - put: #@ cepler_out_name("cala")
      params:
        repository: #@ cepler_resource_name("cala", env)
        environment: #@ "gcp-" + env
#@ end
