FROM ubuntu

ENV DEBIAN_FRONTEND=noninteractive
RUN apt-get update \
  && apt-get install -y \
  tzdata curl make git build-essential lsb-release rename \
  libtool autotools-dev autoconf libssl-dev libboost-all-dev unzip \
  apt-transport-https ca-certificates \
  gnupg software-properties-common \
  vim jq rsync wget netcat-openbsd kafkacat postgresql-client \
  && apt-get clean all

ARG YQ_VERSION=v4.32.2
ARG YQ_BINARY=yq_linux_amd64
ARG YQ_SHASUM=0e5c6b5a74d4ccd6eed43180f60dd48a6e1d0e778f834dca33a312301674b628
RUN wget https://github.com/mikefarah/yq/releases/download/${YQ_VERSION}/${YQ_BINARY} -O /usr/bin/yq \
  && echo $YQ_SHASUM /usr/bin/yq | sha256sum --check \
  && chmod +x /usr/bin/yq

ARG BITCOIN_VERSION=24.0.1
ARG BITCOIN_SHASUM=49df6e444515d457ea0b885d66f521f2a26ca92ccf73d5296082e633544253bf
RUN wget https://bitcoincore.org/bin/bitcoin-core-${BITCOIN_VERSION}/bitcoin-${BITCOIN_VERSION}-x86_64-linux-gnu.tar.gz \
  && echo $BITCOIN_SHASUM bitcoin-${BITCOIN_VERSION}-x86_64-linux-gnu.tar.gz | sha256sum --check \
  && tar -xvf bitcoin-${BITCOIN_VERSION}-x86_64-linux-gnu.tar.gz \
  && mv bitcoin-${BITCOIN_VERSION}/bin/* /usr/local/bin && bitcoin-cli -version

ENV OPENTOFU_VERSION=1.8.2
RUN curl --proto '=https' --tlsv1.2 -fsSL https://get.opentofu.org/install-opentofu.sh -o install-opentofu.sh \
  && chmod +x install-opentofu.sh \
  && ./install-opentofu.sh --install-method standalone --opentofu-version $OPENTOFU_VERSION \
  && rm -f install-opentofu.sh

ARG KUBECTL_VERSION=v1.24.12
ARG KUBECTL_SHASUM=25875551d4242339bcc8cef0c18f0a0f631ea621f6fab1190a5aaab466634e7c
RUN curl -LO https://dl.k8s.io/release/$KUBECTL_VERSION/bin/linux/amd64/kubectl \
  && echo $KUBECTL_SHASUM kubectl | sha256sum --check \
  && chmod +x ./kubectl \
  && mv ./kubectl /usr/local/bin

RUN echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] http://packages.cloud.google.com/apt cloud-sdk main" | \
  tee -a /etc/apt/sources.list.d/google-cloud-sdk.list \
  && curl https://packages.cloud.google.com/apt/doc/apt-key.gpg \
  | apt-key --keyring /usr/share/keyrings/cloud.google.gpg  add - \
  && apt-get update -y \
  && apt-get install google-cloud-sdk -y

RUN wget -O- https://k14s.io/install.sh | bash

RUN curl https://raw.githubusercontent.com/helm/helm/master/scripts/get-helm-3 | bash

RUN curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | gpg --dearmor -o /usr/share/keyrings/githubcli-archive-keyring.gpg \
  && echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" \
  | tee /etc/apt/sources.list.d/github-cli.list > /dev/null \
  && apt update && apt install gh

ARG CEPLER_VERSION=0.7.13
RUN wget https://github.com/bodymindarts/cepler/releases/download/v${CEPLER_VERSION}/cepler-x86_64-unknown-linux-musl-${CEPLER_VERSION}.tar.gz \
  && tar -zxvf cepler-x86_64-unknown-linux-musl-${CEPLER_VERSION}.tar.gz \
  && mv cepler-x86_64-unknown-linux-musl-${CEPLER_VERSION}/cepler /usr/local/bin \
  && chmod +x /usr/local/bin/cepler \
  && rm -rf ./cepler-*

ARG HONEYMARKER_VERSION=0.2.10
RUN wget https://github.com/honeycombio/honeymarker/releases/download/v${HONEYMARKER_VERSION}/honeymarker-linux-amd64

ARG GRPCURL_VERSION=1.8.7
ARG GRPCURL_SHASUM=b50a9c9cdbabab03c0460a7218eab4a954913d696b4d69ffb720f42d869dbdd5
RUN wget https://github.com/fullstorydev/grpcurl/releases/download/v${GRPCURL_VERSION}/grpcurl_${GRPCURL_VERSION}_linux_x86_64.tar.gz \
  && echo $GRPCURL_SHASUM grpcurl_${GRPCURL_VERSION}_linux_x86_64.tar.gz | sha256sum --check \
  && tar -zxvf grpcurl_${GRPCURL_VERSION}_linux_x86_64.tar.gz \
  && mv grpcurl /usr/local/bin \
  && chmod +x /usr/local/bin/grpcurl \
  && rm grpcurl_${GRPCURL_VERSION}_linux_x86_64.tar.gz

ARG HCLEDIT_VERSION=0.2.6
ARG HCLEDIT_SHASUM=c11b73643202936e835dfcb15b7d72211cbaed12b3740cab7a377156ebb36816
RUN wget https://github.com/minamijoyo/hcledit/releases/download/v${HCLEDIT_VERSION}/hcledit_${HCLEDIT_VERSION}_linux_amd64.tar.gz \
  && echo $HCLEDIT_SHASUM hcledit_${HCLEDIT_VERSION}_linux_amd64.tar.gz | sha256sum --check\
  && tar -zxvf hcledit_${HCLEDIT_VERSION}_linux_amd64.tar.gz \
  && mv hcledit /usr/local/bin \
  && chmod +x /usr/local/bin/hcledit \
  && rm hcledit_${HCLEDIT_VERSION}_linux_amd64.tar.gz

RUN wget -O- https://carvel.dev/install.sh | bash

ARG STABLESATS_VERSION=0.9.3
RUN mkdir stablesats && cd stablesats \
  && wget https://github.com/GaloyMoney/stablesats-rs/releases/download/${STABLESATS_VERSION}/stablesats-x86_64-unknown-linux-musl-${STABLESATS_VERSION}.tar.gz -O stablesats.tar.gz \
  && tar --strip-components=1 -xf stablesats.tar.gz \
  && mv stablesats /usr/local/bin && cd ../ && rm -rf ./stablesats

ARG GHTOKEN_VERSION=2.0.1
RUN wget -O ghtoken \
  https://github.com/Link-/gh-token/releases/download/v${GHTOKEN_VERSION}/linux-amd64 && \
  echo "f76e8cb35f0b04b59073a486cc952e50fa9f1c930a25619ea9abcf44a13165c4  ghtoken" | \
  shasum -c - && \
  chmod u+x ./ghtoken && \
  mv ./ghtoken /usr/local/bin/ghtoken

RUN wget https://github.com/mike-engel/jwt-cli/releases/download/4.0.0/jwt-linux.tar.gz \
  && echo "6b0740c3f4c7134a0cbcf802b95b033bd2246d592ad16aa2ee2d80e5b289b4d6  jwt-linux.tar.gz" > jwt-linux.sha256 \
  && shasum --check --status ./jwt-linux.sha256 \
  && tar -xzf jwt-linux.tar.gz \
  && mv jwt /usr/local/bin/jwt

RUN curl -sSL https://rover.apollo.dev/nix/v0.21.0 | sh
ENV PATH="$PATH:/root/.rover/bin"

ARG NIX_VERSION=0.15.1
RUN wget https://github.com/DeterminateSystems/nix-installer/releases/download/v${NIX_VERSION}/nix-installer.sh \
  && chmod +x ./nix-installer.sh \
  && ./nix-installer.sh install linux --init none --no-confirm \
  && rm ./nix-installer.sh
ENV PATH="${PATH}:/nix/var/nix/profiles/default/bin"


# TODO: Check if this is necessary beyond debugging
ARG DIRENV_VERSION=2.33.0
RUN wget https://github.com/direnv/direnv/releases/download/v${DIRENV_VERSION}/direnv.linux-amd64 -O /usr/bin/direnv \
  && chmod +x /usr/bin/direnv \
  && echo 'eval "$(direnv hook bash)"' >> ~/.bashrc
