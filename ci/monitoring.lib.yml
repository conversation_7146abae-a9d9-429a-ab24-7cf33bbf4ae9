#@ load("@ytt:data", "data")

#@ load("shared.lib.yml",
#@   "pipeline_image",
#@   "task_image_config",
#@   "tf_resource_name",
#@   "put_honeymarker",
#@   "cepler_resource_name",
#@   "cepler_out_name",
#@   "chart_smoke_test",
#@   "env_value")

#@ def monitoring_job(env):
name: #@ env + "-monitoring"
serial: true
plan:
- in_parallel:
  - #@ put_honeymarker(env)
  - do:
    - get: #@ cepler_resource_name("monitoring", env)
      trigger: true
    - put: #@ tf_resource_name("monitoring", env)
      tags:
      - #@ env
      params:
        terraform_source: #@ cepler_resource_name("monitoring", env) + "/gcp/" + env + "/monitoring"
        vars:
          secrets: #@ env_value(env, "monitoring_secrets")
        env:
          HONEYCOMBIO_APIKEY: #@ env_value(env, "honeycomb_api_key")
    - in_parallel:
    #@ for chart in ["monitoring", "kafka-connect"]:
      - #@ chart_smoke_test(chart, "monitoring", env)
    #@ end
    - put: #@ cepler_out_name("monitoring")
      params:
        repository: #@ cepler_resource_name("monitoring", env)
        environment: #@ "gcp-" + env
#@ end
