#!/bin/bash

set -eu

current_head=$(yq e '.image.git_ref' deployment-files/modules/${CHART_SUBPATH}/chart/values.yaml)

if [[ $current_head == "null" ]]; then
  current_head=$(yq e ".${CHART}.image.git_ref" deployment-files/modules/${CHART_SUBPATH}/chart/values.yaml)
fi

if [[ $current_head == "null" ]]; then
  current_head=$(yq e ".${CHART}.images.app.git_ref" deployment-files/modules/${CHART_SUBPATH}/chart/values.yaml)
fi

cd repo

git checkout --force ${current_head}
