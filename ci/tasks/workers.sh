#!/bin/bash

set -eu

export CI_ROOT="$(pwd)"
export CI_ROOT_DIR="${CI_ROOT##*/}"

cat <<EOF > ${CI_ROOT}/gcloud-creds.json
${GOOGLE_CREDENTIALS}
EOF
cat <<EOF > ${CI_ROOT}/login.ssh
${SSH_PRIVATE_KEY}
EOF
chmod 600 ${CI_ROOT}/login.ssh
cat <<EOF > ${CI_ROOT}/login.ssh.pub
${SSH_PUB_KEY}
EOF
gcloud auth activate-service-account --key-file ${CI_ROOT}/gcloud-creds.json
export project_id="$(cat ${CI_ROOT}/gcloud-creds.json  | jq -r '.project_id')"
gcloud --project=${project_id} compute os-login ssh-keys add --key-file=${CI_ROOT}/login.ssh.pub > /dev/null

export BASTION_USER="sa_$(cat ${CI_ROOT}/gcloud-creds.json  | jq -r '.client_id')"
export ADDITIONAL_SSH_OPTS="-l ${BASTION_USER} -o StrictHostKeyChecking=no -i ${CI_ROOT}/login.ssh"

pushd repo
cat <<EOF > gcp/${ENVIRONMENT}/workers/terraform.tfvars
worker_key = <<EOT
${WORKER_KEY}EOT
worker_key_pub = <<EOT
${WORKER_KEY_PUB}EOT
host_key_pub = <<EOT
${HOST_KEY_PUB}EOT
EOF

cp ${CI_ROOT}/gcloud-creds.json ./

GCP_PROJECT=${project_id} bin/sync-to-bastion.sh ${ENVIRONMENT}

gcloud compute ssh --ssh-key-file=${CI_ROOT}/login.ssh ${ENVIRONMENT}-bastion --project=${project_id} --zone=${BASTION_ZONE} -- "export GOOGLE_APPLICATION_CREDENTIALS=\$(pwd)/repo/gcloud-creds.json; cd repo/gcp/${ENVIRONMENT}/workers; tofu init && echo yes | tofu apply"

gcloud compute ssh --ssh-key-file=${CI_ROOT}/login.ssh ${ENVIRONMENT}-bastion --project=${project_id} --zone=${BASTION_ZONE} -- "rm repo/gcp/${ENVIRONMENT}/workers/terraform.tfvars; rm repo/gcloud-creds.json;"
