# write_current_git_refs:
# Searches a directory recursively for lines containing 'METADATA::'
# and extracts relevant information to produce a JSON representation of Git metadata.
# The result is written to the provided output file.
#
# Example input line:
# ./path/to/file: ... # METADATA:: repository=https://github.com/GaloyMoney/galoy;commit_ref=1739e6c;app=consent,monorepo_subdir=apps/consent;
#
# Example output JSON:
# {
#   "consent": {
#     "commit_ref": "1739e6c",
#     "repo_url": "https://github.com/GaloyMoney/galoy",
#     "app": "consent",
#     "monorepo_subdir": "apps/consent"
#   },
#   ...
# }
write_current_git_refs() {
  search_dir=$1
  output_file=$2
  grep -R 'METADATA::' "${search_dir}" 2>/dev/null | awk -F'# METADATA:: ' '
  BEGIN { print "[" }
  {
    split($2, metadata, ";");
    delete data;
    for (i in metadata) {
      split(metadata[i], pair, "=");
      data[pair[1]] = pair[2];
    }
    if (data["repository"] && data["commit_ref"] && data["app"]) {
      app_name = data["app"];
      if (data["monorepo_subdir"]) {
        printf("{\"%s\":{\"commit_ref\":\"%s\",\"repo_url\":\"%s\",\"app\":\"%s\",\"monorepo_subdir\":\"%s\"}},", app_name, data["commit_ref"], data["repository"], app_name, data["monorepo_subdir"]);
      } else {
        printf("{\"%s\":{\"commit_ref\":\"%s\",\"repo_url\":\"%s\",\"app\":\"%s\"}},", app_name, data["commit_ref"], data["repository"], app_name);
      }
    }
  }
  END { print "{}]" }
  ' | jq 'reduce .[] as $item ({}; . + $item)' > $output_file
}

get_src_current_heads() {
  if [ -f modules/galoy/vendor/galoy/chart/values.yaml ]; then
    galoy_current_head=$(yq e '.galoy.images.app.git_ref' modules/galoy/vendor/galoy/chart/values.yaml)
  fi
  if [ -f modules/galoy/vendor/galoy/chart/charts/price/values.yaml ]; then
    price_current_head=$(yq e '.image.git_ref' modules/galoy/vendor/galoy/chart/charts/price/values.yaml)
  fi
}

get_src_prepared_heads() {
  if [ -f modules/galoy/vendor/galoy/chart/values.yaml ]; then
    galoy_prepared_head=$(yq e '.galoy.images.app.git_ref' modules/galoy/vendor/galoy/chart/values.yaml)
  fi
  if [ -f modules/galoy/vendor/galoy/chart/charts/price/values.yaml ]; then
    price_prepared_head=$(yq e '.image.git_ref' modules/galoy/vendor/galoy/chart/charts/price/values.yaml)
  fi
}

put_src_diffs() {
  before_file="$1"
  after_file="$2"

  # Extract keys (app names) from the before file
  keys=$(jq 'keys[]' $before_file)

  mkdir -p src-changes
  # For each key, compare the commit_refs and generate output if they differ
  for key in $keys; do
      # Remove quotes from the key
      app_name=$(echo $key | tr -d '"')

      # Extract commit_refs and repo_url
      before_commit_ref=$(jq -r ".\"${app_name}\".commit_ref" $before_file)
      after_commit_ref=$(jq -r ".\"${app_name}\".commit_ref" $after_file)
      repo_url=$(jq -r ".\"${app_name}\".repo_url" $before_file)
      app_target="//$(jq -r ".\"${app_name}\".monorepo_subdir" $after_file):"

      if [[ "$repo_url" == "https://github.com/GaloyMoney/galoy" ]]; then
        if [[ "$before_commit_ref" != "$after_commit_ref" ]]; then
          pushd ../galoy-repo
          declare -A relevant_commits
          relevant_commits=()
          generate_relevant_commits "$before_commit_ref" "$after_commit_ref" "$app_target" relevant_commits
					popd

          if [[ "${#relevant_commits[@]}" -eq 0 ]]; then
            echo "No relevant changes" >> src-changes/${app_name}
          else
            cat > "src-changes/${app_name}" <<- EOF
						${repo_url}/compare/${app_name}-${before_commit_ref}...${app_name}-${after_commit_ref}

						Relevant commits:

						EOF
            for commit in "${!relevant_commits[@]}"; do
              echo "- ${repo_url}/commit/${commit} - ${relevant_commits[$commit]}" >> src-changes/${app_name}
            done
					fi
        fi
      else
        if [[ "$before_commit_ref" != "$after_commit_ref" ]]; then
          echo "${repo_url}/compare/${before_commit_ref}...${after_commit_ref}" > src-changes/${app_name}
        fi
      fi
  done

  if [[ "${galoy_current_head:-""}" != "${galoy_prepared_head:-""}" ]]; then
		pushd ../galoy-repo
    declare -A relevant_commits
    relevant_commits=()
		generate_core_bundle_relevant_commits "$galoy_current_head" "$galoy_prepared_head" "//core/..." relevant_commits
		popd

		cat > "src-changes/galoy" <<- EOF
		https://github.com/GaloyMoney/galoy/compare/core-${galoy_current_head}...core-${galoy_prepared_head}

		Relevant commits:

		EOF

		if [[ "${#relevant_commits[@]}" -eq 0 ]]; then
			echo "- No relevant commits" >> src-changes/galoy
		else
			for commit in "${!relevant_commits[@]}"; do
				echo "- ${repo_url}/commit/${commit} - ${relevant_commits[$commit]}" >> src-changes/galoy
			done
		fi
  fi
  if [[ "${price_current_head:-""}" != "${price_prepared_head:-""}" ]]; then
    echo "https://github.com/blinkbitcoin/price/compare/${price_current_head}...${price_prepared_head}" > src-changes/price
  fi
}

generate_relevant_commits() {
  before_commit_ref="$1"
  after_commit_ref="$2"
  app_target="$3"
  local -n _relevant_commits=$4

  git checkout $after_commit_ref
  app_src_files=($(nix develop -c buck2 uquery "inputs(deps(\"${app_target}\"))" 2>/dev/null))

  for commit in $(git log --format="%H" ${before_commit_ref}..${after_commit_ref}); do
    changed_files=$(git diff-tree --no-commit-id --name-only -r $commit)

    for file in ${changed_files[@]}; do
      if printf '%s\n' "${app_src_files[@]}" | grep -Fxq "$file"; then
        commit_message=$(git log --format="%s" -n 1 $commit)
        pr_number=$(echo "$commit_message" | grep -oE '#[0-9]+' | tail -1 | sed 's/#//')

        if [[ -n "$pr_number" ]]; then
          pr_link="https://github.com/GaloyMoney/galoy/pull/${pr_number}"
          commit_message="${commit_message/ (#$pr_number)/}"
          _relevant_commits["$commit"]="[$commit_message (#$pr_number)]($pr_link)"
        else
          _relevant_commits["$commit"]="$commit_message"
        fi

        break
      fi
    done
  done
}

generate_core_bundle_relevant_commits() {
  before_commit_ref="$1"
  after_commit_ref="$2"
  app_target="$3"
  local -n _core_bundle_relevant_commits=$4

  git checkout $after_commit_ref
  app_src_files=($(nix develop -c buck2 uquery 'inputs(deps("//core/api:")) + inputs(deps("//core/api-ws-server:")) + inputs(deps("//core/api-trigger:")) + inputs(deps("//core/api-exporter:")) + inputs(deps("//core/api-cron:"))' 2>/dev/null))

  for commit in $(git log --format="%H" ${before_commit_ref}..${after_commit_ref}); do
    changed_files=$(git diff-tree --no-commit-id --name-only -r $commit)

    for file in ${changed_files[@]}; do
      if printf '%s\n' "${app_src_files[@]}" | grep -Fxq "$file"; then
        commit_message=$(git log --format="%s" -n 1 $commit)
        pr_number=$(echo "$commit_message" | grep -oE '#[0-9]+' | sed 's/#//')

        if [[ -n "$pr_number" ]]; then
          pr_link="https://github.com/GaloyMoney/galoy/pull/${pr_number}"
          commit_message="${commit_message/ (#$pr_number)/}"
          _core_bundle_relevant_commits["$commit"]="[$commit_message (#$pr_number)]($pr_link)"
        else
          _core_bundle_relevant_commits["$commit"]="$commit_message"
        fi

        break
      fi
    done
  done
}
