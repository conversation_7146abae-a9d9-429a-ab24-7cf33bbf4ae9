#@ load("@ytt:data", "data")

#@ load("shared.lib.yml",
#@   "cepler_resource_name",
#@   "cepler_in",
#@   "task_image_config",
#@   "put_honeymarker",
#@   "tf_resource_name",
#@   "cepler_out_name",
#@   "env_value")

#@ def inception_job(env):
name: #@ env + "-inception"
serial_groups:
- #@ env
plan:
- in_parallel:
  - #@ put_honeymarker(env)
  - do:
    - get: #@ cepler_resource_name("infra", env)
      trigger: true
    - put: #@ tf_resource_name("inception", env)
      params:
        terraform_source: #@ cepler_resource_name("infra", env) + "/gcp/" + env + "/inception"
    - put: #@ tf_resource_name("inception", env)
      params:
        terraform_source: #@ cepler_resource_name("infra", env) + "/gcp/" + env + "/inception"
#@ end

#@ def platform_job(env):
name: #@ env + "-platform"
serial_groups:
- #@ env
plan:
- in_parallel:
  - #@ put_honeymarker(env)
  - do:
    - get: #@ cepler_resource_name("infra", env)
      trigger: true
      passed:
      - #@ env + "-inception"
    - attempts: 2
      do:
      - put: #@ tf_resource_name("platform", env)
        params:
          terraform_source: #@ cepler_resource_name("infra", env) + "/gcp/" + env + "/platform"
        on_failure:
          task: wait-and-retry
          config:
            platform: linux
            image_resource: #@ task_image_config()
            run:
              path: sh
              args:
              - "-c"
              - "sleep 300"

    - attempts: 2
      do:
      - put: #@ tf_resource_name("platform", env)
        params:
          terraform_source: #@ cepler_resource_name("infra", env) + "/gcp/" + env + "/platform"
        on_failure:
          task: wait-and-retry
          config:
            platform: linux
            image_resource: #@ task_image_config()
            run:
              path: sh
              args:
              - "-c"
              - "sleep 300"
#@ end

#@ def smoketest_job(env):
name: #@ env + "-smoketest"
serial_groups:
- #@ env
plan:
- in_parallel:
  - #@ put_honeymarker(env)
  - do:
    - get: #@ cepler_resource_name("infra", env)
      trigger: true
      passed:
      - #@ env + "-platform"
    - put: #@ tf_resource_name("smoketest", env)
      tags:
      - #@ env
      params:
        terraform_source: #@ cepler_resource_name("infra", env) + "/gcp/" + env + "/smoketest"
    - put: #@ cepler_out_name("infra")
      params:
        repository: #@ cepler_resource_name("infra", env)
        environment: #@ "gcp-" + env
#@ end
