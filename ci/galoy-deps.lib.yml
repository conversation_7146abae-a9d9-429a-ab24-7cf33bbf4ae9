#@ load("@ytt:data", "data")

#@ load("shared.lib.yml",
#@   "tf_resource_name",
#@   "put_honeymarker",
#@   "cepler_resource_name",
#@   "cepler_out_name",
#@   "chart_smoke_test",
#@   "env_value")

#@ def galoy_deps_job(env):
name: #@ env + "-galoy-deps"
serial: true
plan:
- get: #@ cepler_resource_name("galoy-deps", env)
  trigger: true
- in_parallel:
  - #@ put_honeymarker(env)
  - do:
    - put: #@ tf_resource_name("galoy-deps", env)
      tags:
      - #@ env
      params:
        terraform_source: #@ cepler_resource_name("galoy-deps", env) + "/gcp/" + env + "/galoy-deps"
        vars:
          smoketest_kubeconfig: #@ env_value(env, "smoketest_kubeconfig")
          secrets: #@ env_value(env, "galoy_deps_secrets")
    - #@ chart_smoke_test("galoy-deps", "galoy-deps", env)
    - put: #@ cepler_out_name("galoy-deps")
      params:
        repository: #@ cepler_resource_name("galoy-deps", env)
        environment: #@ "gcp-" + env
#@ end
