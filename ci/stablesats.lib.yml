#@ load("@ytt:data", "data")
#@ load("shared.lib.yml",
#@   "pipeline_image",
#@   "task_image_config",
#@   "tf_resource_name",
#@   "put_honeymarker",
#@   "cepler_resource_name",
#@   "cepler_out_name",
#@   "chart_smoke_test",
#@   "env_value")

#@ def stablesats_job(env):
name: #@ env + "-stablesats"
serial: true
plan:
- in_parallel:
  - get: #@ cepler_resource_name("stablesats", env)
    trigger: true
  - get: pipeline-tasks
- in_parallel:
  - #@ put_honeymarker(env)
  - do:
    - put: #@ tf_resource_name("stablesats", env)
      tags:
      - #@ env
      params:
        terraform_source: #@ cepler_resource_name("stablesats", env) + "/gcp/" + env + "/stablesats"
        vars:
          secrets: #@ env_value(env, "stablesats_secrets")
    - #@ chart_smoke_test("stablesats", "stablesats", env)
    - put: #@ cepler_out_name("stablesats")
      params:
        repository: #@ cepler_resource_name("stablesats", env)
        environment: #@ "gcp-" + env
#@ end
