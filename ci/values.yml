#@data/values
---
git_org_uri: **************:blinkbitcoin
git_uri: **************:blinkbitcoin/blink-deployments.git
git_branch: main
gates_branch: cepler-gates
github_private_key: ((github-blinkbitcoin.private_key))
github_token: ((github-blinkbitcoin.api_token))
github_app_id: ((github-blinkbitcoin.github_app_id))
github_app_private_key: ((github-blinkbitcoin.github_app_private_key))
docker_registry: us.gcr.io/galoy-org
docker_registry_user: ((docker-creds.username))
docker_registry_password: ((docker-creds.password))
galoy_repo_git_uri: **************:blinkbitcoin/blink.git

worker_key: ((external-worker.worker-key))
worker_key_pub: ((external-worker.worker-key-pub))
host_key_pub: ((external-worker.host-key-pub))

infra_slack_webhook_url: ((galoy-infra-slack.api_url))
galoy_slack_webhook_url: ((galoy-main-slack.api_url))
monitoring_slack_webhook_url: ((monitoring-slack.api_url))
bitcoin_slack_webhook_url: ((bitcoin-slack.api_url))
addons_slack_webhook_url: ((addons-slack.api_url))
cala_slack_webhook_url: ((addons-slack.api_url))
stablesats_slack_webhook_url: ((addons-slack.api_url))
galoy-deps_slack_webhook_url: ((galoy-deps-slack.api_url))
blink-addons_slack_webhook_url: ((blink-addons-slack.api_url))
slack_username: concourse

galoy_staging_creds: ((staging-gcp-creds.creds_json))
galoy_staging_smoketest_kubeconfig: ((staging-smoketest.kubeconfig))
galoy_staging_ssh_private_key: ((staging-ssh.ssh_private_key))
galoy_staging_ssh_pub_key: ((staging-ssh.ssh_public_key))
galoy_staging_bastion_zone: ((staging-ssh.bastion_zone))
galoy_staging_lnd1_pass: ((staging-lnd1.password))
galoy_staging_galoy_secrets: ((staging-secrets.galoy))
galoy_staging_bitcoin_secrets: ((staging-secrets.bitcoin))
galoy_staging_addons_secrets: ((staging-secrets.addons))
galoy_staging_stablesats_secrets: ((staging-secrets.stablesats))
galoy_staging_monitoring_secrets: ((staging-secrets.monitoring))
galoy_staging_galoy_deps_secrets: ((staging-secrets.galoy_deps))
galoy_staging_blink_addons_secrets: ((staging-secrets.blink_addons))
galoy_staging_honeycomb_api_key: ((staging-honeycomb.api_key))
galoy_staging_k8s_secret_reader_token: ((k8s-secret-reader-token.token))
galoy_staging_k8s_ca_cert: ((k8s-ca-cert.ca_cert))

galoy_bbw_creds: ((bbw-gcp-creds.creds_json))
galoy_bbw_smoketest_kubeconfig: ((bbw-smoketest.kubeconfig))
galoy_bbw_ssh_private_key: ((bbw-ssh.ssh_private_key))
galoy_bbw_ssh_pub_key: ((bbw-ssh.ssh_public_key))
galoy_bbw_bastion_zone: ((bbw-ssh.bastion_zone))
galoy_bbw_lnd1_pass: ((bbw-lnd1.password))
galoy_bbw_galoy_secrets: ((bbw-secrets.galoy))
galoy_bbw_bitcoin_secrets: ((bbw-secrets.bitcoin))
galoy_bbw_addons_secrets: ((bbw-secrets.addons))
galoy_bbw_stablesats_secrets: ((bbw-secrets.stablesats))
galoy_bbw_monitoring_secrets: ((bbw-secrets.monitoring))
galoy_bbw_services_secrets: ((bbw-secrets.services))
galoy_bbw_galoy_deps_secrets: ((bbw-secrets.galoy_deps))
galoy_bbw_blink_addons_secrets: ((bbw-secrets.blink_addons))
galoy_bbw_honeycomb_api_key: ((bbw-honeycomb.api_key))
galoy_bbw_k8s_secret_reader_token: ((k8s-secret-reader-token.token))
galoy_bbw_k8s_ca_cert: ((k8s-ca-cert.ca_cert))
