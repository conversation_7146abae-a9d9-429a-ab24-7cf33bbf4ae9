#! @config/pipeline = "blink-staging"
#! @config/team = "dev"

#@ load("@ytt:data", "data")

#@ load("shared.lib.yml",
#@   "pipeline_image",
#@   "task_image_config",
#@   "cepler_in",
#@   "cepler_out",
#@   "tf_resource",
#@   "slack_resource",
#@   "env_value",
#@   "resource_types")
#@ load("infra.lib.yml",
#@   "inception_job",
#@   "platform_job",
#@   "smoketest_job")
#@ load("workers.lib.yml",
#@   "workers_job")
#@ load("bitcoin.lib.yml",
#@   "bitcoin_job")
#@ load("stablesats.lib.yml",
#@   "stablesats_job")
#@ load("cala.lib.yml",
#@   "cala_job")
#@ load("addons.lib.yml",
#@   "addons_job")
#@ load("galoy.lib.yml",
#@   "galoy_job",
#@   "lnd_creds_k8s_secret")
#@ load("monitoring.lib.yml",
#@   "monitoring_job")
#@ load("galoy-deps.lib.yml",
#@   "galoy_deps_job")
#@ load("blink-addons.lib.yml",
#@   "blink_addons_job")

#@ environments = ["galoy-staging"]
groups:
- name: all
  jobs:
#@ for env in environments:
  #@ for deployment in ["workers", "bitcoin", "galoy", "monitoring", "addons", "cala", "stablesats", "galoy-deps", "blink-addons"]:
  - #@ env + "-" + deployment
  #@ end
  - #@ env + "-inception"
  - #@ env + "-platform"
  - #@ env + "-smoketest"
#@ end
#@ for deployment in ["galoy", "bitcoin", "monitoring", "addons", "cala", "stablesats", "galoy-deps", "blink-addons"]:
- name: #@ deployment
  jobs:
  #@ for env in environments:
  - #@ env + "-" + deployment
  #@ end
#@ end
- name: infra
  jobs:
#@ for env in environments:
  - #@ env + "-inception"
  - #@ env + "-platform"
  - #@ env + "-smoketest"
#@ end
- name: workers
  jobs:
  #@ for env in environments:
  - #@ env + "-workers"
  #@ end

jobs:
#@ for env in environments:
- #@ inception_job(env)
- #@ platform_job(env)
- #@ smoketest_job(env)
- #@ workers_job(env)
- #@ bitcoin_job(env)
- #@ addons_job(env)
- #@ cala_job(env)
- #@ stablesats_job(env)
- #@ galoy_job(env, { "smoketest_kubeconfig": env_value(env, "smoketest_kubeconfig") })
- #@ monitoring_job(env)
- #@ galoy_deps_job(env)
- #@ blink_addons_job(env)
#@ end

resources:
- #@ cepler_out("workers")
- #@ cepler_out("infra")
- #@ cepler_out("bitcoin")
- #@ cepler_out("stablesats")
- #@ cepler_out("addons")
- #@ cepler_out("cala")
- #@ cepler_out("galoy")
- #@ cepler_out("monitoring")
- #@ cepler_out("galoy-deps")
- #@ cepler_out("blink-addons")

#@ for env in environments:
  #@ for deployment in ["workers", "bitcoin", "galoy", "infra", "monitoring", "addons", "cala", "stablesats", "galoy-deps", "blink-addons"]:
- #@ cepler_in(deployment, env)
  #@ end
- #@ tf_resource("bitcoin", env, "services/bitcoin")
- #@ tf_resource("stablesats", env, "services/stablesats")
- #@ tf_resource("galoy-deps", env, "services/galoy-deps")
- #@ tf_resource("blink-addons", env, "services/blink-addons")
- #@ tf_resource("addons", env, "services/addons")
- #@ tf_resource("cala", env, "services/cala")
- #@ tf_resource("monitoring", env, "services/monitoring")
- #@ tf_resource("galoy", env)
- #@ tf_resource("inception", env)
- #@ tf_resource("platform", env)
- #@ tf_resource("smoketest", env)
- #@ lnd_creds_k8s_secret(env)
#@ end

- name: pipeline-tasks
  type: git
  source:
    paths: [ci/tasks/*, Makefile]
    uri: #@ data.values.git_uri
    branch: #@ data.values.git_branch
    private_key: #@ data.values.github_private_key

- name: honeymarker
  type: honeymarker
  source:
    api_key: #@ env_value(env, "honeycomb_api_key")

resource_types: #@ resource_types()
