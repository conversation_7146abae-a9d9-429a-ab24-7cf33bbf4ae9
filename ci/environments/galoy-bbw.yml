#! @config/pipeline = "blink-deployments"
#! @config/team = "galoy"

#@ load("@ytt:data", "data")

#@ load("shared.lib.yml",
#@   "pipeline_image",
#@   "task_image_config",
#@   "cepler_in",
#@   "cepler_out",
#@   "tf_resource",
#@   "slack_resource",
#@   "env_value",
#@   "resource_types")
#@ load("bump.lib.yml",
#@   "open_pr_job",
#@   "ungated_cepler_in")
#@ load("infra.lib.yml",
#@   "inception_job",
#@   "platform_job",
#@   "smoketest_job")
#@ load("workers.lib.yml",
#@   "workers_job")
#@ load("bitcoin.lib.yml",
#@   "bitcoin_job")
#@ load("stablesats.lib.yml",
#@   "stablesats_job")
#@ load("addons.lib.yml",
#@   "addons_job")
#@ load("galoy.lib.yml",
#@   "galoy_job",
#@   "lnd_creds_k8s_secret")
#@ load("monitoring.lib.yml",
#@   "monitoring_job")
#@ load("galoy-deps.lib.yml",
#@   "galoy_deps_job")
#@ load("blink-addons.lib.yml",
#@   "blink_addons_job")

#@ environments = ["galoy-bbw"]
groups:
- name: all
  jobs:
#@ for env in environments:
  #@ for deployment in ["workers", "bitcoin", "galoy", "monitoring", "addons", "stablesats", "galoy-deps", "blink-addons"]:
  - #@ env + "-" + deployment
  - #@ "bump-" + env + "-" + deployment
  #@ end
  - #@ "bump-" + env + "-infra"
  - #@ env + "-inception"
  - #@ env + "-platform"
  - #@ env + "-smoketest"
#@ for deployment in ["galoy", "bitcoin", "monitoring", "addons", "stablesats", "galoy-deps", "blink-addons"]:
- name: #@ deployment
  jobs:
  #@ for env in environments:
  - #@ env + "-" + deployment
  - #@ "bump-" + env + "-" + deployment
  #@ end
#@ end
- name: infra
  jobs:
#@ for env in environments:
  - #@ "bump-" + env + "-infra"
  - #@ env + "-inception"
  - #@ env + "-platform"
  - #@ env + "-smoketest"
#@ end
- name: workers
  jobs:
  #@ for env in environments:
  - #@ env + "-workers"
  - #@ "bump-" + env + "-workers"
  #@ end
#@ end
- name: image
  jobs:
  - build-pipeline-image

jobs:
#@ for env in environments:
  #@ for deployment in ["infra", "workers", "bitcoin", "galoy", "monitoring", "addons", "stablesats", "galoy-deps", "blink-addons"]:
- #@ open_pr_job(deployment, env)
  #@ end
- #@ inception_job(env)
- #@ platform_job(env)
- #@ smoketest_job(env)
- #@ workers_job(env)
- #@ bitcoin_job(env)
- #@ addons_job(env)
- #@ stablesats_job(env)
- #@ galoy_job(env)
- #@ monitoring_job(env)
- #@ galoy_deps_job(env)
- #@ blink_addons_job(env)
#@ end

- name: build-pipeline-image
  serial: true
  plan:
  - {get: pipeline-image-def, trigger: true}
  - task: build
    config:
      platform: linux
      image_resource:
        type: registry-image
        source:
          repository: gcr.io/kaniko-project/executor
          tag: debug
      inputs:
      - name: pipeline-image-def
      outputs:
      - name: image
      run:
        path: /kaniko/executor
        args:
        - --context=pipeline-image-def/ci/image
        - --use-new-run
        - --single-snapshot
        - --cache=false
        - --no-push
        - --tar-path=image/image.tar
  - put: pipeline-image
    params:
      image: image/image.tar

resources:
- #@ cepler_out("workers")
- #@ cepler_out("infra")
- #@ cepler_out("bitcoin")
- #@ cepler_out("stablesats")
- #@ cepler_out("addons")
- #@ cepler_out("galoy")
- #@ cepler_out("monitoring")
- #@ cepler_out("galoy-deps")
- #@ cepler_out("blink-addons")

#@ for env in environments:
  #@ for deployment in ["workers", "bitcoin", "galoy", "infra", "monitoring", "addons", "stablesats", "galoy-deps", "blink-addons"]:
- #@ ungated_cepler_in(deployment, env)
- #@ cepler_in(deployment, env)
  #@ end

- #@ tf_resource("bitcoin", env, "services/bitcoin")
- #@ tf_resource("stablesats", env, "services/stablesats")
- #@ tf_resource("addons", env, "services/addons")
- #@ tf_resource("monitoring", env, "services/monitoring")
- #@ tf_resource("galoy-deps", env, "services/galoy-deps")
- #@ tf_resource("blink-addons", env, "services/blink-addons")
- #@ tf_resource("galoy", env)
- #@ tf_resource("inception", env)
- #@ tf_resource("platform", env)
- #@ tf_resource("smoketest", env)

- #@ lnd_creds_k8s_secret(env)
#@ end

- name: galoy-repo
  type: git
  source:
    uri: #@ data.values.galoy_repo_git_uri
    branch: main
    private_key: #@ data.values.github_private_key

- name: gates-branch
  type: git
  source:
    uri: #@ data.values.git_uri
    branch: #@ data.values.gates_branch
    private_key: #@ data.values.github_private_key

- name: bot-push
  type: git
  source:
    uri: #@ data.values.git_uri
    private_key: #@ data.values.github_private_key

- name: pipeline-tasks
  type: git
  source:
    paths: [ci/tasks/*, Makefile]
    uri: #@ data.values.git_uri
    branch: #@ data.values.git_branch
    private_key: #@ data.values.github_private_key

- name: pipeline-image
  type: registry-image
  source:
    tag: latest
    username: #@ data.values.docker_registry_user
    password: #@ data.values.docker_registry_password
    repository: #@ pipeline_image()

- name: pipeline-image-def
  type: git
  source:
    paths: [ci/image/Dockerfile]
    uri: #@ data.values.git_uri
    branch: #@ data.values.git_branch
    private_key: #@ data.values.github_private_key

- name: honeymarker
  type: honeymarker
  source:
    api_key: #@ env_value(env, "honeycomb_api_key")

resource_types: #@ resource_types()
